# MongoDB
MONGODB_URI=mongodb://localhost:27017/aitools

# NextAuth.js
# NEXTAUTH_URL 会自动根据运行时环境检测（开发环境、Vercel、Netlify等）
# 通常不需要手动设置，除非有特殊需求
# NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Email Configuration
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App Configuration
# NEXT_PUBLIC_APP_URL 会自动根据运行时环境检测
# 支持开发环境、Vercel、Netlify、Railway等部署平台
# 通常不需要手动设置，除非有特殊需求
# NEXT_PUBLIC_APP_URL=http://localhost:3000

# API Configuration
# API URLs 会自动根据应用URL构建，通常不需要手动设置
# NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
