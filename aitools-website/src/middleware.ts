import { NextRequest, NextResponse } from 'next/server';
import { locales, defaultLocale } from './i18n/config';

// 自定义语言检测函数
function detectLocale(request: NextRequest): string {
  // 获取Accept-Language头
  const acceptLanguage = request.headers.get('accept-language');

  // 开发环境下打印调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('🌐 Language Detection:', {
      acceptLanguage,
      url: request.url,
      pathname: request.nextUrl.pathname
    });
  }

  if (acceptLanguage) {
    // 检查是否包含中文
    const isChinesePreferred = acceptLanguage.includes('zh') ||
                              acceptLanguage.includes('zh-CN') ||
                              acceptLanguage.includes('zh-TW') ||
                              acceptLanguage.includes('zh-HK');

    if (isChinesePreferred) {
      console.log('🇨🇳 Detected Chinese browser, redirecting to /zh');
      return 'zh';
    }
  }

  // 默认返回英文
  console.log('🇺🇸 Default to English, redirecting to /en');
  return 'en';
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // 检查路径是否已经包含语言前缀
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  // 如果路径已经包含语言前缀，直接继续
  if (pathnameHasLocale) {
    return NextResponse.next();
  }

  // 如果是根路径，根据浏览器语言重定向
  if (pathname === '/') {
    const detectedLocale = detectLocale(request);
    return NextResponse.redirect(new URL(`/${detectedLocale}`, request.url));
  }

  // 对于其他路径，添加默认语言前缀
  const detectedLocale = detectLocale(request);
  return NextResponse.redirect(new URL(`/${detectedLocale}${pathname}`, request.url));
}

export const config = {
  // 匹配所有路径，除了以下路径：
  // - API 路由 (/api)
  // - 静态文件 (_next/static)
  // - 图片文件 (_next/image)
  // - favicon.ico
  // - robots.txt
  // - sitemap.xml
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'
  ]
};
