import createMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './i18n/config';

export default createMiddleware({
  // 支持的语言列表
  locales,
  
  // 默认语言
  defaultLocale,
  
  // 语言检测策略
  localeDetection: true,
  
  // 路径前缀策略 - 默认语言不显示前缀
  localePrefix: 'as-needed',
  
  // 备用语言
  alternateLinks: true,
});

export const config = {
  // 匹配所有路径，除了以下路径：
  // - API 路由 (/api)
  // - 静态文件 (_next/static)
  // - 图片文件 (_next/image)
  // - favicon.ico
  // - robots.txt
  // - sitemap.xml
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'
  ]
};
