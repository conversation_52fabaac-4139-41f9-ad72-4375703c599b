// API 响应消息国际化
export interface ApiMessages {
  // 通用错误消息
  errors: {
    fetch_failed: string;
    network_error: string;
    validation_failed: string;
    unauthorized: string;
    forbidden: string;
    not_found: string;
    internal_error: string;
    invalid_request: string;
    missing_required_field: string;
    duplicate_name: string;
    create_failed: string;
    update_failed: string;
    delete_failed: string;
  };
  
  // 成功消息
  success: {
    created: string;
    updated: string;
    deleted: string;
    submitted: string;
    approved: string;
    rejected: string;
    published: string;
  };
  
  // 工具相关消息
  tools: {
    fetch_failed: string;
    create_failed: string;
    name_required: string;
    description_required: string;
    website_required: string;
    category_required: string;
    pricing_required: string;
    submitter_name_required: string;
    submitter_email_required: string;
    name_exists: string;
    submit_success: string;
    approve_success: string;
    reject_success: string;
    approve_failed: string;
    reject_failed: string;
    not_found: string;
    update_success: string;
    update_failed: string;
  };
  
  // 用户相关消息
  user: {
    not_found: string;
    unauthorized: string;
    profile_update_success: string;
    profile_update_failed: string;
  };
  
  // 认证相关消息
  auth: {
    invalid_credentials: string;
    code_sent: string;
    code_send_failed: string;
    invalid_code: string;
    login_success: string;
    login_failed: string;
    logout_success: string;
  };
  
  // 支付相关消息
  payment: {
    create_intent_failed: string;
    payment_success: string;
    payment_failed: string;
    webhook_error: string;
  };
}

// 中文消息
export const zhMessages: ApiMessages = {
  errors: {
    fetch_failed: '获取数据失败',
    network_error: '网络错误，请重试',
    validation_failed: '验证失败',
    unauthorized: '未授权访问',
    forbidden: '禁止访问',
    not_found: '资源未找到',
    internal_error: '服务器内部错误',
    invalid_request: '无效请求',
    missing_required_field: '缺少必需字段',
    duplicate_name: '名称已存在',
    create_failed: '创建失败',
    update_failed: '更新失败',
    delete_failed: '删除失败',
  },
  success: {
    created: '创建成功',
    updated: '更新成功',
    deleted: '删除成功',
    submitted: '提交成功',
    approved: '批准成功',
    rejected: '拒绝成功',
    published: '发布成功',
  },
  tools: {
    fetch_failed: '获取工具列表失败',
    create_failed: '创建工具失败',
    name_required: 'name 是必需的',
    description_required: 'description 是必需的',
    website_required: 'website 是必需的',
    category_required: 'category 是必需的',
    pricing_required: 'pricing 是必需的',
    submitter_name_required: 'submitterName 是必需的',
    submitter_email_required: 'submitterEmail 是必需的',
    name_exists: '该工具名称已存在',
    submit_success: '工具提交成功，等待审核',
    approve_success: '工具审核通过',
    reject_success: '工具已拒绝',
    approve_failed: '审核通过失败',
    reject_failed: '拒绝失败',
    not_found: '工具未找到',
    update_success: '工具更新成功',
    update_failed: '工具更新失败',
  },
  user: {
    not_found: '用户未找到',
    unauthorized: '用户未授权',
    profile_update_success: '个人资料更新成功',
    profile_update_failed: '个人资料更新失败',
  },
  auth: {
    invalid_credentials: '无效的登录凭据',
    code_sent: '验证码已发送',
    code_send_failed: '验证码发送失败',
    invalid_code: '无效的验证码',
    login_success: '登录成功',
    login_failed: '登录失败',
    logout_success: '退出成功',
  },
  payment: {
    create_intent_failed: '创建支付意图失败',
    payment_success: '支付成功',
    payment_failed: '支付失败',
    webhook_error: 'Webhook 处理错误',
  },
};

// 英文消息
export const enMessages: ApiMessages = {
  errors: {
    fetch_failed: 'Failed to fetch data',
    network_error: 'Network error, please try again',
    validation_failed: 'Validation failed',
    unauthorized: 'Unauthorized access',
    forbidden: 'Access forbidden',
    not_found: 'Resource not found',
    internal_error: 'Internal server error',
    invalid_request: 'Invalid request',
    missing_required_field: 'Missing required field',
    duplicate_name: 'Name already exists',
    create_failed: 'Creation failed',
    update_failed: 'Update failed',
    delete_failed: 'Deletion failed',
  },
  success: {
    created: 'Created successfully',
    updated: 'Updated successfully',
    deleted: 'Deleted successfully',
    submitted: 'Submitted successfully',
    approved: 'Approved successfully',
    rejected: 'Rejected successfully',
    published: 'Published successfully',
  },
  tools: {
    fetch_failed: 'Failed to fetch tools list',
    create_failed: 'Failed to create tool',
    name_required: 'name is required',
    description_required: 'description is required',
    website_required: 'website is required',
    category_required: 'category is required',
    pricing_required: 'pricing is required',
    submitter_name_required: 'submitterName is required',
    submitter_email_required: 'submitterEmail is required',
    name_exists: 'Tool name already exists',
    submit_success: 'Tool submitted successfully, awaiting review',
    approve_success: 'Tool approved successfully',
    reject_success: 'Tool rejected successfully',
    approve_failed: 'Failed to approve tool',
    reject_failed: 'Failed to reject tool',
    not_found: 'Tool not found',
    update_success: 'Tool updated successfully',
    update_failed: 'Failed to update tool',
  },
  user: {
    not_found: 'User not found',
    unauthorized: 'User unauthorized',
    profile_update_success: 'Profile updated successfully',
    profile_update_failed: 'Failed to update profile',
  },
  auth: {
    invalid_credentials: 'Invalid credentials',
    code_sent: 'Verification code sent',
    code_send_failed: 'Failed to send verification code',
    invalid_code: 'Invalid verification code',
    login_success: 'Login successful',
    login_failed: 'Login failed',
    logout_success: 'Logout successful',
  },
  payment: {
    create_intent_failed: 'Failed to create payment intent',
    payment_success: 'Payment successful',
    payment_failed: 'Payment failed',
    webhook_error: 'Webhook processing error',
  },
};

// 获取API消息的工具函数
export function getApiMessage(locale: 'zh' | 'en', key: string): string {
  const messages = locale === 'zh' ? zhMessages : enMessages;
  
  // 支持嵌套键，如 'tools.fetch_failed'
  const keys = key.split('.');
  let message: any = messages;
  
  for (const k of keys) {
    if (message && typeof message === 'object' && k in message) {
      message = message[k];
    } else {
      // 如果找不到对应的键，返回默认的中文消息
      return locale === 'zh' ? '操作失败' : 'Operation failed';
    }
  }
  
  return typeof message === 'string' ? message : (locale === 'zh' ? '操作失败' : 'Operation failed');
}

// 从请求头中获取语言偏好
export function getLocaleFromRequest(request: Request): 'zh' | 'en' {
  const acceptLanguage = request.headers.get('accept-language') || '';
  const pathname = new URL(request.url).pathname;
  
  // 首先检查URL路径中的语言前缀
  if (pathname.startsWith('/en/')) {
    return 'en';
  } else if (pathname.startsWith('/zh/')) {
    return 'zh';
  }
  
  // 然后检查Accept-Language头
  if (acceptLanguage.includes('en')) {
    return 'en';
  }
  
  // 默认返回中文
  return 'zh';
}
