'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import CategoryCard from '@/components/CategoryCard';
import ErrorMessage from '@/components/ErrorMessage';
import { Grid, TrendingUp } from 'lucide-react';
import { Locale } from '@/i18n/config';

// 分类页面组件

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
}

interface CategoriesPageClientProps {
  categories: Category[];
  error: string | null;
}

export default function CategoriesPageClient({ categories, error }: CategoriesPageClientProps) {
  const pathname = usePathname();
  const t = useTranslations('categories_page');

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  // Generate localized href
  const getLocalizedHref = (path: string) => {
    return currentLocale === 'zh' ? path : `/en${path}`;
  };

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ErrorMessage message={error} />
      </div>
    );
  }

  const popularCategories = categories
    .sort((a, b) => b.toolCount - a.toolCount)
    .slice(0, 6);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          <Grid className="inline-block mr-3 h-10 w-10 text-blue-600" />
          {t('title')}
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          {t('subtitle')}
        </p>
      </div>

      {/* Stats */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {categories.length}
            </div>
            <div className="text-gray-700">{t('categories_count')}</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {categories.reduce((sum, cat) => sum + cat.toolCount, 0)}
            </div>
            <div className="text-gray-700">{t('tools_count')}</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {categories.length > 0 ? Math.round(categories.reduce((sum, cat) => sum + cat.toolCount, 0) / categories.length) : 0}
            </div>
            <div className="text-gray-700">{t('avg_tools_per_category')}</div>
          </div>
        </div>
      </div>

      {/* Popular Categories */}
      <section className="mb-16">
        <div className="flex items-center mb-8">
          <TrendingUp className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-900">{t('popular_categories')}</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {popularCategories.map((category) => (
            <CategoryCard key={category._id} category={category} />
          ))}
        </div>
      </section>

      {/* All Categories */}
      <section>
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold text-gray-900">{t('all_categories')}</h2>
          <div className="text-sm text-gray-600">
            {t('categories_total', { count: categories.length })}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => (
            <CategoryCard key={category._id} category={category} />
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="mt-16 bg-blue-600 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold text-white mb-4">
          {t('not_found_title')}
        </h2>
        <p className="text-blue-100 mb-6">
          {t('not_found_desc')}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href={getLocalizedHref('/submit')}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors"
          >
            {t('submit_tool')}
          </Link>
          <Link
            href={getLocalizedHref('/contact')}
            className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors"
          >
            {t('contact_us')}
          </Link>
        </div>
      </section>
    </div>
  );
}
