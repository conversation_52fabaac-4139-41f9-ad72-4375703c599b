'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Calendar, Clock, CreditCard, CheckCircle } from 'lucide-react';
import { LAUNCH_OPTIONS, formatPrice, PRICING_CONFIG } from '@/constants/pricing';
import { Locale } from '@/i18n/config';

// 使用统一的发布选项配置
const launchOptions = LAUNCH_OPTIONS;

interface LaunchDateSelectorProps {
  toolId?: string;
  currentOption?: 'free' | 'paid';
  currentDate?: string;
  isEditing?: boolean;
  onSubmit: (option: 'free' | 'paid', date: string) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
}

export default function LaunchDateSelector({
  currentOption = 'free',
  currentDate,
  isEditing = false,
  onSubmit,
  isSubmitting,
  error
}: LaunchDateSelectorProps) {
  const [selectedOption, setSelectedOption] = useState<'free' | 'paid'>(currentOption);
  const [selectedDate, setSelectedDate] = useState<string>('');

  const pathname = usePathname();
  const t = useTranslations('launch');

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  // 获取最早可选择的免费日期（一个月后）
  const getMinFreeDate = () => {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date.toISOString().split('T')[0];
  };

  // 获取最早可选择的付费日期（明天）
  const getMinPaidDate = () => {
    const date = new Date();
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  };

  useEffect(() => {
    if (currentDate) {
      setSelectedDate(currentDate);
    } else {
      // 根据选择的选项设置默认日期
      if (selectedOption === 'free') {
        setSelectedDate(getMinFreeDate());
      } else {
        setSelectedDate(getMinPaidDate());
      }
    }
  }, [selectedOption, currentDate]);

  const handleOptionChange = (option: 'free' | 'paid') => {
    setSelectedOption(option);
    // 当切换选项时，重新设置日期
    if (option === 'free') {
      setSelectedDate(getMinFreeDate());
    } else {
      setSelectedDate(getMinPaidDate());
    }
  };

  const handleSubmit = async () => {
    if (!selectedDate) {
      return;
    }
    await onSubmit(selectedOption, selectedDate);
  };

  return (
    <div className="space-y-8">
      {/* 选项选择 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {isEditing ? t('select_plan') : t('select_option')}
        </h3>
        <div className="grid md:grid-cols-2 gap-6">
          {launchOptions.map((option) => (
            <div
              key={option.id}
              className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${
                selectedOption === option.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${'recommended' in option && option.recommended ? 'ring-2 ring-blue-200' : ''}`}
              onClick={() => handleOptionChange(option.id)}
            >
              {'recommended' in option && option.recommended && (
                <div className="absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  {t('recommended')}
                </div>
              )}
              
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  {option.id === 'free' ? (
                    <Calendar className="h-6 w-6 text-gray-600 mr-3" />
                  ) : (
                    <CreditCard className="h-6 w-6 text-blue-600 mr-3" />
                  )}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">{option.title}</h4>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    {formatPrice(option.price)}
                  </div>
                </div>
              </div>
              
              <ul className="space-y-2">
                {option.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
              
              <div className="mt-4">
                <input
                  type="radio"
                  name="launchOption"
                  value={option.id}
                  checked={selectedOption === option.id}
                  onChange={() => handleOptionChange(option.id)}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 ${
                  selectedOption === option.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300'
                }`}>
                  {selectedOption === option.id && (
                    <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 日期选择 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          {t('select_date')}
        </h3>
        
        <div className="max-w-md">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            min={selectedOption === 'free' ? getMinFreeDate() : getMinPaidDate()}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          
          <p className="text-sm text-gray-500 mt-2">
            {selectedOption === 'free'
              ? t('free_date_info')
              : t('paid_date_info')
            }
          </p>
        </div>
      </div>

      {/* 提交按钮 */}
      <div className="text-center">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting || !selectedDate}
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {selectedOption === 'paid' ? t('processing') : t('saving')}
            </>
          ) : (
            <>
              {selectedOption === 'paid' ? (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  {isEditing ? t('upgrade_and_pay', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice) }) : t('pay_amount', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice) })}
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {isEditing ? t('save_changes') : t('confirm_date')}
                </>
              )}
            </>
          )}
        </button>
        
        {error && (
          <p className="text-red-600 text-sm mt-4">{error}</p>
        )}
        
        <p className="text-gray-500 text-sm mt-4">
          {selectedOption === 'paid'
            ? t('payment_redirect')
            : isEditing
              ? t('changes_effective')
              : t('review_queue')
          }
        </p>
      </div>
    </div>
  );
}
