'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import ToolCard from '@/components/ToolCard';
import { Tool, ToolsResponse, Category, apiClient } from '@/lib/api';
import { Search, Filter, Grid, List, SortAsc, SortDesc } from 'lucide-react';
import { type Locale } from '@/i18n/config';

interface SearchPageClientProps {
  initialQuery: string;
  initialResults: ToolsResponse | null;
  initialCategories: Category[];
  initialPage?: number;
  initialCategory?: string;
  initialSort?: string;
  locale: Locale;
}

export default function SearchPageClient({
  initialQuery,
  initialResults,
  initialCategories,
  initialPage = 1,
  initialCategory = '',
  initialSort = 'createdAt',
  locale,
}: SearchPageClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('search');

  const [searchTerm, setSearchTerm] = useState(initialQuery);
  const [results, setResults] = useState<ToolsResponse | null>(initialResults);
  const [categories] = useState<Category[]>(initialCategories);
  const [selectedCategory, setSelectedCategory] = useState(initialCategory);
  const [sortBy, setSortBy] = useState(initialSort);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(initialPage);

  // 更新URL参数
  const updateURL = (params: Record<string, string>) => {
    const newSearchParams = new URLSearchParams(searchParams || '');

    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newSearchParams.set(key, value);
      } else {
        newSearchParams.delete(key);
      }
    });

    router.push(`/search?${newSearchParams.toString()}`);
  };

  // 执行搜索
  const performSearch = async (query: string = '', page: number = 1, category: string = '', sort: string = 'createdAt') => {
    // if (!query.trim()) {
    //   setResults(null);
    //   return;
    // }

    setLoading(true);
    try {
      const response = await apiClient.getTools({
        search: query,
        page,
        limit: 12,
        category: category || undefined,
        sort,
        order: 'desc',
      });

      if (response.success) {
        setResults(response.data || null);
      } else {
        console.error('Search failed:', response.error);
        setResults(null);
      }
    } catch (error) {
      console.error('Search error:', error);
      setResults(null);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索提交
  const handleSearch = (e: React.FormEvent) => {
    e?.preventDefault();
    // if (searchTerm.trim()) {
    setCurrentPage(1);
    updateURL({
      q: searchTerm?.trim(),
      page: '1',
      category: selectedCategory,
      sort: sortBy,
    });
    performSearch(searchTerm?.trim(), 1, selectedCategory, sortBy);
    // }
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    updateURL({
      q: searchTerm,
      page: page.toString(),
      category: selectedCategory,
      sort: sortBy,
    });
    performSearch(searchTerm, page, selectedCategory, sortBy);
  };

  useEffect(() => {
    if(!searchTerm.trim()) {
      performSearch('', 1, selectedCategory, sortBy);
    }
  }, [searchTerm, selectedCategory, sortBy])

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{t('title')}</h1>
          {searchTerm && (
            <p className="text-lg text-gray-600">
              {t('search_results', { term: searchTerm })}
              {results && ` - ${t('found_tools', { count: results.pagination.totalItems })}`}
            </p>
          )}
        </div>

        {/* Search Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <form onSubmit={handleSearch}>
            <div className="relative">
              <input
                type="text"
                placeholder={t('placeholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <button
                type="submit"
                className="absolute right-2 top-2 px-4 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                style={{
                  whiteSpace: 'nowrap',
                  marginLeft: 10
                }}
              >
                {
                  !searchTerm.trim() && t('all')
                }
                {
                  searchTerm.trim() && t('search_button')
                }
              </button>
            </div>
          </form>
        </div>

        {/* 如果没有搜索关键词，显示提示 */}
        {!searchTerm && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">{t('start_search_title')}</h3>
            <p className="text-gray-600">
              {t('start_search_desc')}
            </p>
          </div>
        )}

        {/* 搜索结果 */}

        {/* Filters and Controls */}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t('searching')}</p>
          </div>
        )}

        {/* Results */}
        {!loading && results && (
          <>
            {/* Results Count */}
            <div className="mb-6">
              <p className="text-gray-600">
                {t('results_count', {
                  showing: results.tools.length,
                  total: results.pagination.totalItems
                })}
                {selectedCategory && ` ${t('in_category', { category: categories.find(c => c.id === selectedCategory)?.name || '' })}`}
              </p>
            </div>

            {/* Tools Grid/List */}
            {results.tools.length > 0 ? (
              <>
                <div className={viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'
                  : 'space-y-4 mb-8'
                }>
                  {results.tools.map((tool) => (
                    <ToolCard key={tool._id} tool={tool} />
                  ))}
                </div>

                {/* Pagination */}
                {results.pagination.totalPages > 1 && (
                  <div className="flex justify-center">
                    <nav className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={!results.pagination.hasPrevPage}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {t('prev_page')}
                      </button>

                      <span className="px-3 py-2 text-sm text-gray-700">
                        {t('page_info', { current: currentPage, total: results.pagination.totalPages })}
                      </span>

                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={!results.pagination.hasNextPage}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {t('next_page')}
                      </button>
                    </nav>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <Search className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('no_results')}</h3>
                <p className="text-gray-600">
                  {t('try_different_keywords')}
                </p>
              </div>
            )}
          </>
        )}

      </div>
    </>
  );
}
