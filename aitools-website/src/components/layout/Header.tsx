'use client';

import { useState } from 'react';
import NextLink from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { FaBars, FaTimes, FaSearch, FaGlobe } from 'react-icons/fa';
import UserMenu from '../auth/UserMenu';
import { locales, localeNames, type Locale } from '@/i18n/config';

const NavLink = ({ children, href }: { children: React.ReactNode; href: string }) => (
  <NextLink
    href={href}
    className="px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors"
  >
    {children}
  </NextLink>
);

// Language switcher component
const LanguageSwitcher = ({ currentLocale }: { currentLocale: Locale }) => {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const switchLanguage = (newLocale: Locale) => {
    // Remove current locale from pathname and add new locale
    const currentPath = pathname || '/';
    const pathWithoutLocale = currentPath.replace(/^\/[a-z]{2}/, '') || '/';
    const newPath = newLocale === 'zh' ? pathWithoutLocale : `/${newLocale}${pathWithoutLocale}`;
    router.push(newPath);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
      >
        <FaGlobe className="w-4 h-4" />
        <span className="text-sm">{localeNames[currentLocale]}</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50">
          {locales.map((locale) => (
            <button
              key={locale}
              onClick={() => switchLanguage(locale)}
              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                locale === currentLocale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
            >
              {localeNames[locale]}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations('navigation');

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  // Generate navigation links with current locale
  const getLocalizedHref = (path: string) => {
    return currentLocale === 'zh' ? path : `/en${path}`;
  };

  const links = [
    { name: t('home'), href: getLocalizedHref('/') },
    { name: t('tools'), href: getLocalizedHref('/tools') },
    { name: t('categories'), href: getLocalizedHref('/categories') },
    { name: t('submit'), href: getLocalizedHref('/submit') },
  ];

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const query = formData.get('search') as string;
    if (query.trim()) {
      router.push(getLocalizedHref(`/search?q=${encodeURIComponent(query.trim())}`));
    }
  };

  return (
    <>
      <header className="bg-white px-4 shadow-sm border-b border-gray-200">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-8">
            <NextLink href={getLocalizedHref('/')} className="flex items-center space-x-2 hover:no-underline">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                {currentLocale === 'zh' ? 'AI工具导航' : 'AI Tools'}
              </span>
            </NextLink>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-4">
              {links.map((link) => (
                <NavLink key={link.name} href={link.href}>
                  {link.name}
                </NavLink>
              ))}
            </nav>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-md mx-8 hidden md:block">
            <form onSubmit={handleSearch}>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="text-gray-400" />
                </div>
                <input
                  name="search"
                  type="text"
                  placeholder={t('search_placeholder')}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </form>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-2">
            {/* Language Switcher */}
            <LanguageSwitcher currentLocale={currentLocale} />

            {/* User Menu */}
            <UserMenu />

            {/* Mobile menu button */}
            <button
              className="md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Open Menu"
            >
              {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden pb-4">
            <nav className="space-y-4">
              {links.map((link) => (
                <NavLink key={link.name} href={link.href}>
                  {link.name}
                </NavLink>
              ))}

              {/* Mobile Search */}
              <div className="pt-4">
                <form onSubmit={handleSearch}>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaSearch className="text-gray-400" />
                    </div>
                    <input
                      name="search"
                      type="text"
                      placeholder={t('search_placeholder')}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </form>
              </div>
            </nav>
          </div>
        )}
      </header>
    </>
  );
}
