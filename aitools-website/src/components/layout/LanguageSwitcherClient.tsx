'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { FaGlobe } from 'react-icons/fa';
import { locales, localeNames, type Locale } from '@/i18n/config';

interface LanguageSwitcherClientProps {
  currentLocale: Locale;
}

export default function LanguageSwitcherClient({ currentLocale }: LanguageSwitcherClientProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const switchLanguage = (newLocale: Locale) => {
    // Get current path without locale prefix
    const currentPath = pathname || '/';
    let pathWithoutLocale = currentPath;

    // Remove current locale prefix if it exists
    if (currentPath.startsWith('/en/')) {
      pathWithoutLocale = currentPath.replace('/en', '');
    } else if (currentPath.startsWith('/zh/')) {
      pathWithoutLocale = currentPath.replace('/zh', '');
    } else if (currentPath === '/en') {
      pathWithoutLocale = '/';
    } else if (currentPath === '/zh') {
      pathWithoutLocale = '/';
    }

    // Ensure pathWithoutLocale starts with /
    if (!pathWithoutLocale.startsWith('/')) {
      pathWithoutLocale = '/' + pathWithoutLocale;
    }

    // Build new path with new locale
    const newPath = newLocale === 'zh' ? pathWithoutLocale : `/en${pathWithoutLocale}`;

    router.push(newPath);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
      >
        <FaGlobe className="w-4 h-4" />
        <span className="text-sm">{localeNames[currentLocale]}</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50">
          {locales.map((locale) => (
            <button
              key={locale}
              onClick={() => switchLanguage(locale)}
              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                locale === currentLocale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
            >
              {localeNames[locale]}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
