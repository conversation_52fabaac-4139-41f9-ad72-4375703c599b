import React from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import SearchPageClient from '@/components/search/SearchPageClient';
import { apiClient } from '@/lib/api';

interface SearchPageProps {
  searchParams: Promise<{
    q?: string;
    page?: string;
    category?: string;
    sort?: string;
  }>;
}

// 生成动态metadata
export async function generateMetadata({ searchParams }: SearchPageProps): Promise<Metadata> {
  const params = await searchParams;
  const query = params.q || '';

  if (!query.trim()) {
    return {
      title: '搜索 AI 工具 - AI Tools',
      description: '搜索和发现最新的AI工具，提升您的工作效率',
    };
  }

  return {
    title: `搜索 "${query}" - AI Tools`,
    description: `搜索 "${query}" 相关的AI工具，发现最适合您需求的工具`,
    openGraph: {
      title: `搜索 "${query}" - AI Tools`,
      description: `搜索 "${query}" 相关的AI工具，发现最适合您需求的工具`,
    },
  };
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const params = await searchParams;
  const query = params.q || '';
  const page = parseInt(params.page || '1');
  const category = params.category || '';
  const sort = params.sort || 'createdAt';

  // 如果没有搜索关键词，显示空状态
  if (!query.trim()) {
    return <SearchPageClient initialQuery="" initialResults={null} initialCategories={[]} />;
  }

  try {
    // 获取搜索结果
    const [toolsResponse, categoriesResponse] = await Promise.all([
      apiClient.getTools({
        search: query,
        page,
        limit: 12,
        category: category || undefined,
        sort,
        order: 'desc',
      }),
      apiClient.getCategories(),
    ]);

    if (!toolsResponse.success) {
      throw new Error(toolsResponse.error || '获取搜索结果失败');
    }

    if (!categoriesResponse.success) {
      throw new Error(categoriesResponse.error || '获取分类失败');
    }

    return (
      <SearchPageClient
        initialQuery={query}
        initialResults={toolsResponse.data}
        initialCategories={categoriesResponse.data?.categories || []}
        initialPage={page}
        initialCategory={category}
        initialSort={sort}
      />
    );
  } catch (error) {
    console.error('Search page error:', error);
    notFound();
  }
}
