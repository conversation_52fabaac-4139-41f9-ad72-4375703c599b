import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';

import { getTranslations } from 'next-intl/server';
import CategoryCard from '@/components/CategoryCard';
import ToolCard from '@/components/ToolCard';
import { Tool, apiClient } from '@/lib/api';
import { CATEGORY_METADATA } from '@/constants/categories';
import { getWebsiteStructuredData, getOrganizationStructuredData, getToolListStructuredData } from '@/lib/seo/structuredData';
import { Search, Zap, Star } from 'lucide-react';
import { type Locale } from '@/i18n/config';

type Props = {
  params: { locale: Locale };
};

// 生成动态metadata
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'site' });
  try {
    // 获取工具和分类数据用于动态描述
    const [toolsResponse, categoriesResponse] = await Promise.all([
      apiClient.getTools({
        status: 'published',
        limit: 1
      }),
      apiClient.getCategories()
    ]);

    const totalTools = toolsResponse.success && toolsResponse.data ? toolsResponse.data.pagination.totalItems : 0;
    const totalCategories = categoriesResponse.success && categoriesResponse.data ? categoriesResponse.data.categories.length : 0;

    const title = t('title') + ' - ' + t('subtitle');
    const description = t('description') + `已收录${totalTools}+个优质AI工具，涵盖${totalCategories}个分类，包括ChatGPT、Midjourney等热门AI工具，涵盖文本生成、图像创作、数据分析、自动化等各个领域。`;
    const keywords = t('keywords');

    return {
      title,
      description,
      keywords,
      authors: [{ name: locale === 'zh' ? 'AI工具导航团队' : 'AI Tools Directory Team' }],
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        type: 'website',
        locale: locale === 'zh' ? 'zh_CN' : 'en_US',
        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
        siteName: t('title'),
        title,
        description,
        images: [
          {
            url: '/og-homepage.jpg',
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: ['/og-homepage.jpg'],
      },
      alternates: {
        canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
        languages: {
          'zh': '/',
          'en': '/en',
        },
      },
      other: {
        'google-site-verification': process.env.GOOGLE_SITE_VERIFICATION || '',
      },
    };
  } catch (error) {
    // 如果获取数据失败，返回默认metadata
    return {
      title: 'AI工具导航 - 发现最好的人工智能工具',
      description: '专业的AI工具发现平台，汇集最新最好的人工智能工具。包括ChatGPT、Midjourney等热门AI工具，涵盖文本生成、图像创作、数据分析、自动化等各个领域。',
      keywords: 'AI工具,人工智能工具,ChatGPT,Midjourney,AI工具导航,机器学习工具,深度学习,AI应用,自动化工具,智能工具,AI工具推荐',
    };
  }
}

// 服务端数据获取函数
async function getHomePageData(locale: Locale) {
  try {
    // 并行获取所有数据
    const [featuredResponse, recentResponse, categoriesResponse] = await Promise.all([
      // 热门工具 - 按浏览量排序
      apiClient.getTools({
        status: 'published',
        limit: 6,
        sort: 'views',
        order: 'desc'
      }),
      // 最近发布的工具 - 按发布时间排序，获取更多数据供客户端筛选
      apiClient.getTools({
        status: 'published',
        limit: 50,
        sort: 'launchDate',
        order: 'desc'
      }),
      // 分类数据
      apiClient.getCategories()
    ]);

    // 简单地按发布时间排序，不进行复杂的日期过滤
    const allRecentTools: Tool[] = recentResponse.success && recentResponse.data ? recentResponse.data.tools : [];

    // 处理分类数据，取前4个分类用于主页显示
    const allCategories = categoriesResponse.success && categoriesResponse.data ? categoriesResponse.data.categories : [];
    const topCategories = allCategories.slice(0, 4).map((apiCategory: { id: string; name: string; count: number }) => {
      // 使用统一的分类元数据映射
      const metadata = CATEGORY_METADATA[apiCategory.id] || {
        description: '优质AI工具集合',
        icon: '🔧',
        color: '#6B7280'
      };

      return {
        _id: apiCategory.id,
        name: apiCategory.name,
        slug: apiCategory.id,
        description: metadata.description,
        icon: metadata.icon,
        color: metadata.color,
        toolCount: apiCategory.count
      };
    });

    return {
      featuredTools: featuredResponse.success && featuredResponse.data ? featuredResponse.data.tools.slice(0, 6) : [],
      // 将最近的工具分为两组：前6个作为"今日发布"，接下来6个作为"最近发布"
      todayTools: allRecentTools.slice(0, 6),
      recentTools: allRecentTools.slice(6, 12),
      categories: topCategories,
      error: null
    };
  } catch (error) {
    console.error('Failed to fetch homepage data:', error);
    return {
      featuredTools: [],
      todayTools: [],
      recentTools: [],
      categories: [],
      error: locale === 'zh' ? '网络错误，请重试' : 'Network error, please try again'
    };
  }
}

export default async function Home({ params }: Props) {
  const { locale } = await params;
  const { featuredTools, todayTools, recentTools, categories, error } = await getHomePageData(locale);
  const t = await getTranslations({ locale, namespace: 'home' });

  // 生成结构化数据
  const websiteStructuredData = getWebsiteStructuredData();
  const organizationStructuredData = getOrganizationStructuredData();
  const allTools = [...featuredTools, ...todayTools, ...recentTools];
  const toolListStructuredData = allTools.length > 0 ? getToolListStructuredData(allTools) : null;

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteStructuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationStructuredData)
        }}
      />
      {toolListStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(toolListStructuredData)
          }}
        />
      )}

      {/* 如果有错误，显示错误信息 */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20" role="banner">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <header className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              {t('hero_title')}
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              {t('hero_subtitle')}
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-8">
              <form action={`/${locale}/search`} method="GET">
                <div className="relative">
                  <input
                    name="q"
                    type="text"
                    placeholder={t('search_placeholder')}
                    className="w-full pl-12 pr-16 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg"
                  />
                  <Search className="absolute left-4 top-4 h-6 w-6 text-gray-400" />
                  <button
                    type="submit"
                    className="absolute right-2 top-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    style={{
                      alignSelf: 'anchor-center',
                      whiteSpace: 'nowrap',
                      marginLeft: 10,
                      borderRadius: 30
                    }}
                  >
                    {t('search_button')}
                  </button>
                </div>
              </form>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href={`/${locale}/tools`}
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <Zap className="mr-2 h-5 w-5" />
                {t('view_all_tools')}
              </Link>
              <Link
                href={`/${locale}/submit`}
                className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                {locale === 'zh' ? '提交您的工具' : 'Submit Your Tool'}
              </Link>
            </div>
          </header>
        </div>
      </section>

      {/* Featured Tools Section - 服务端渲染 */}
      <section className="py-16 bg-white" aria-labelledby="featured-tools-heading">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 id="featured-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
              <span className="inline-block mr-2 text-yellow-500" aria-hidden="true">⭐</span>
              {t('featured_tools')}
            </h2>
            <p className="text-lg text-gray-600">
              {locale === 'zh' ? '最受欢迎和评价最高的 AI 工具' : 'Most popular and highly rated AI tools'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredTools.map((tool: Tool) => (
              <ToolCard
                key={tool._id}
                tool={tool}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href={`/${locale}/tools`}
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              {t('view_all_tools')}
            </Link>
          </div>
        </div>
      </section>

      {/* Today's Tools Section - 服务端渲染 */}
      {todayTools.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50" aria-labelledby="today-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="today-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 text-green-600" aria-hidden="true">📅</span>
                {t('today_tools')}
              </h2>
              <p className="text-lg text-gray-600">
                今天刚刚发布的最新 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="list" aria-label="今日发布的AI工具">
              {todayTools.map((tool: Tool) => (
                <article key={tool._id} role="listitem">
                  <ToolCard
                    tool={tool}
                  />
                </article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Recent Tools Section - 服务端渲染 */}
      {recentTools.length > 0 && (
        <section className="py-16 bg-gray-50" aria-labelledby="recent-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="recent-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 text-blue-600" aria-hidden="true">🕒</span>
                最近发布
              </h2>
              <p className="text-lg text-gray-600">
                过去一周内发布的新 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentTools.map((tool: Tool) => (
                <ToolCard
                  key={tool._id}
                  tool={tool}
                />
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href="/tools?sort=launchDate&order=desc"
                className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
              >
                查看更多最新工具
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              <Star className="inline-block mr-2 h-8 w-8 text-blue-600" />
              热门分类
            </h2>
            <p className="text-lg text-gray-600">
              按功能分类浏览 AI 工具
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category: { _id: string; name: string; slug: string; description: string; icon: string; color: string; toolCount: number }) => (
              <CategoryCard key={category._id} category={category} />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/categories"
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              查看所有分类
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-white mb-2">500+</div>
              <div className="text-blue-100">{t('stats.total_tools')}</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-white mb-2">50+</div>
              <div className="text-blue-100">{t('stats.total_categories')}</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-white mb-2">10K+</div>
              <div className="text-blue-100">{t('stats.monthly_visits')}</div>
            </div>
          </div>
        </div>
      </section>

    </>
  );
}
