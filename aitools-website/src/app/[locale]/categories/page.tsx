import React from 'react';
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import CategoriesPageClient from '@/components/categories/CategoriesPageClient';
import { CATEGORY_METADATA } from '@/constants/categories';
import { apiClient } from '@/lib/api';
import { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';
import { type Locale } from '@/i18n/config';

interface Props {
  params: Promise<{ locale: Locale }>;
}

// 生成动态metadata
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'categories' });

  try {
    // 获取分类数据用于动态描述
    const response = await apiClient.getCategories();

    const totalCategories = response.success && response.data ? response.data.categories.length : 0;
    const totalTools = response.success && response.data ? response.data.overview.totalTools : 0;

    const title = t('page_title');
    const description = t('page_description', { categories: totalCategories, tools: totalTools });
    const keywords = t('page_keywords');

    return {
      title,
      description,
      keywords,
      authors: [{ name: locale === 'zh' ? 'AI工具导航团队' : 'AI Tools Directory Team' }],
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        type: 'website',
        locale: locale === 'zh' ? 'zh_CN' : 'en_US',
        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/categories`,
        siteName: t('site_name'),
        title,
        description,
        images: [
          {
            url: '/og-categories.jpg',
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: ['/og-categories.jpg'],
      },
      alternates: {
        canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/categories`,
      },
    };
  } catch (error) {
    // 如果获取数据失败，返回默认metadata
    const fallbackTitle = locale === 'zh' ? 'AI工具分类 - 按功能浏览人工智能工具' : 'AI Tools Categories - Browse AI Tools by Function';
    const fallbackDescription = locale === 'zh'
      ? '按功能分类浏览AI工具，包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。'
      : 'Browse AI tools by function categories, including text generation, image creation, data analysis, automation, audio processing and other AI tool categories.';
    const fallbackKeywords = locale === 'zh'
      ? 'AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类'
      : 'AI tools categories,artificial intelligence categories,AI tool types,machine learning categories,deep learning tool categories,AI application categories,smart tool categories';

    return {
      title: fallbackTitle,
      description: fallbackDescription,
      keywords: fallbackKeywords,
    };
  }
}

// 服务端数据获取函数
async function getCategoriesData() {
  try {
    const response = await apiClient.getCategories();

    if (response.success && response.data) {
      // 转换API数据格式为组件期望的格式
      const transformedCategories = response.data.categories.map((apiCategory: { id: string; name: string; count: number }) => {
        // 使用统一的分类元数据映射
        const metadata = CATEGORY_METADATA[apiCategory.id] || {
          description: '优质AI工具集合',
          icon: '🔧',
          color: '#6B7280'
        };

        return {
          _id: apiCategory.id,
          name: apiCategory.name,
          slug: apiCategory.id,
          description: metadata.description,
          icon: metadata.icon,
          color: metadata.color,
          toolCount: apiCategory.count
        };
      });

      return {
        categories: transformedCategories,
        error: null
      };
    } else {
      return {
        categories: [],
        error: response.error || 'Failed to fetch categories list'
      };
    }
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    return {
      categories: [],
      error: 'Failed to fetch categories list, please try again later'
    };
  }
}

export default async function CategoriesPage({ params }: Props) {
  const { locale } = await params;
  const { categories, error } = await getCategoriesData();
  const t = await getTranslations({ locale, namespace: 'categories' });

  // 生成结构化数据
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: t('breadcrumb_home'), url: `/${locale}` },
    { name: t('breadcrumb_categories'), url: `/${locale}/categories` }
  ]);

  // 生成分类列表结构化数据
  const categoriesStructuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": t('structured_data_name'),
    "description": t('structured_data_description'),
    "numberOfItems": categories.length,
    "itemListElement": categories.map((category, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Thing",
        "name": category.name,
        "description": category.description,
        "url": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/categories/${category.slug}`,
        "additionalProperty": {
          "@type": "PropertyValue",
          "name": t('structured_data_tool_count'),
          "value": category.toolCount
        }
      }
    }))
  };

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(categoriesStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label={t('breadcrumb_aria_label')}>
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href={`/${locale}`}
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                {t('breadcrumb_home')}
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">{t('breadcrumb_categories')}</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <CategoriesPageClient categories={categories} error={error} locale={locale} />
    </>
  );
}
