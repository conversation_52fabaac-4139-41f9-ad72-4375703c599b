import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import CategoriesPageClient from '@/components/categories/CategoriesPageClient';
import { CATEGORY_METADATA } from '@/constants/categories';
import { apiClient } from '@/lib/api';
import { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';

// 生成动态metadata
export async function generateMetadata(): Promise<Metadata> {
  try {
    // 获取分类数据用于动态描述
    const response = await apiClient.getCategories();

    const totalCategories = response.success && response.data ? response.data.categories.length : 0;
    const totalTools = response.success && response.data ? response.data.overview.totalTools : 0;
    const description = `按功能分类浏览AI工具，共${totalCategories}个分类，收录${totalTools}+个优质AI工具。包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。`;

    return {
      title: 'AI工具分类 - 按功能浏览人工智能工具',
      description,
      keywords: 'AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类',
      authors: [{ name: 'AI工具导航团队' }],
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        type: 'website',
        locale: 'zh_CN',
        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/categories`,
        siteName: 'AI工具导航',
        title: 'AI工具分类 - 按功能浏览人工智能工具',
        description,
        images: [
          {
            url: '/og-categories.jpg',
            width: 1200,
            height: 630,
            alt: 'AI工具分类 - 按功能浏览人工智能工具',
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: 'AI工具分类 - 按功能浏览人工智能工具',
        description,
        images: ['/og-categories.jpg'],
      },
      alternates: {
        canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/categories`,
      },
    };
  } catch (error) {
    // 如果获取数据失败，返回默认metadata
    return {
      title: 'AI工具分类 - 按功能浏览人工智能工具',
      description: '按功能分类浏览AI工具，包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。',
      keywords: 'AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类',
    };
  }
}

// 服务端数据获取函数
async function getCategoriesData() {
  try {
    const response = await apiClient.getCategories();

    if (response.success && response.data) {
      // 转换API数据格式为组件期望的格式
      const transformedCategories = response.data.categories.map((apiCategory: { id: string; name: string; count: number }) => {
        // 使用统一的分类元数据映射
        const metadata = CATEGORY_METADATA[apiCategory.id] || {
          description: '优质AI工具集合',
          icon: '🔧',
          color: '#6B7280'
        };

        return {
          _id: apiCategory.id,
          name: apiCategory.name,
          slug: apiCategory.id,
          description: metadata.description,
          icon: metadata.icon,
          color: metadata.color,
          toolCount: apiCategory.count
        };
      });

      return {
        categories: transformedCategories,
        error: null
      };
    } else {
      return {
        categories: [],
        error: response.error || '获取分类列表失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    return {
      categories: [],
      error: '获取分类列表失败，请稍后重试'
    };
  }
}

export default async function CategoriesPage() {
  const { categories, error } = await getCategoriesData();

  // 生成结构化数据
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: '首页', url: '/' },
    { name: 'AI工具分类', url: '/categories' }
  ]);

  // 生成分类列表结构化数据
  const categoriesStructuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "AI工具分类",
    "description": "按功能分类的AI工具目录",
    "numberOfItems": categories.length,
    "itemListElement": categories.map((category, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Thing",
        "name": category.name,
        "description": category.description,
        "url": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/categories/${category.slug}`,
        "additionalProperty": {
          "@type": "PropertyValue",
          "name": "工具数量",
          "value": category.toolCount
        }
      }
    }))
  };

  return (
    <Layout>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(categoriesStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label="面包屑导航">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                首页
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">AI工具分类</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <CategoriesPageClient categories={categories} error={error} />
    </Layout>
  );
}
