'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import LoadingSpinner from '@/components/LoadingSpinner';
import { apiClient, Tool } from '@/lib/api';
import {
  User,
  Heart,
  Plus,
  BarChart3,
  Calendar,
  Eye,
  Settings,
  Edit
} from 'lucide-react';

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const t = useTranslations('profile');
  const [userStats, setUserStats] = useState({
    submittedTools: 0,
    approvedTools: 0,
    totalViews: 0,
    totalLikes: 0,
    likedTools: 0
  });
  const [recentTools, setRecentTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }
    
    if (status === 'authenticated') {
      fetchUserData();
    }
  }, [status, router]);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      // 获取用户提交的工具
      const toolsResponse = await apiClient.getAdminTools();
      
      if (toolsResponse.success && toolsResponse.data) {
        const tools = toolsResponse.data.tools;
        const approvedTools = tools.filter(t => t.status === 'approved');
        
        setUserStats({
          submittedTools: tools.length,
          approvedTools: approvedTools.length,
          totalViews: approvedTools.reduce((sum, t) => sum + t.views, 0),
          totalLikes: approvedTools.reduce((sum, t) => sum + t.likes, 0),
          likedTools: 0 // TODO: 实现获取用户点赞的工具数量
        });
        
        // 获取最近的工具
        setRecentTools(tools.slice(0, 3));
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <LoadingSpinner size="lg" className="py-20" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
                {session.user?.image ? (
                  <img
                    src={session.user.image}
                    alt={session.user.name || ''}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-2xl font-medium text-gray-600">
                    {session.user?.name?.charAt(0) || 'U'}
                  </span>
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {session.user?.name}
                </h1>
                <p className="text-gray-600">{session.user?.email}</p>
                {session.user?.role === 'admin' && (
                  <span className="inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                    {t('admin_badge')}
                  </span>
                )}
                <p className="text-sm text-gray-500 mt-1">
                  {t('join_date')}: {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>
            <Link
              href="/settings"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <Settings className="mr-2 h-4 w-4" />
              {t('edit_profile')}
            </Link>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Plus className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.submitted_tools')}</p>
                <p className="text-2xl font-bold text-gray-900">{userStats.submittedTools}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.approved_tools')}</p>
                <p className="text-2xl font-bold text-gray-900">{userStats.approvedTools}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Eye className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.total_views')}</p>
                <p className="text-2xl font-bold text-gray-900">{userStats.totalViews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Heart className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">{t('stats.total_likes')}</p>
                <p className="text-2xl font-bold text-gray-900">{userStats.totalLikes}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link
            href="/profile/submitted"
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <Plus className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{t('actions.my_submitted_tools')}</h3>
                <p className="text-gray-600">{t('actions.manage_submitted_tools')}</p>
              </div>
            </div>
          </Link>

          <Link
            href="/profile/liked"
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <Heart className="h-8 w-8 text-red-600 mr-4" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{t('actions.my_favorites')}</h3>
                <p className="text-gray-600">{t('actions.view_favorites')}</p>
              </div>
            </div>
          </Link>

          <Link
            href="/submit"
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <Plus className="h-8 w-8 text-green-600 mr-4" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{t('actions.submit_new_tool')}</h3>
                <p className="text-gray-600">{t('actions.share_ai_tools')}</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Recent Activity */}
        {recentTools.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">{t('recent_tools.title')}</h2>
              <Link
                href="/profile/submitted"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                {t('recent_tools.view_all')}
              </Link>
            </div>
            <div className="space-y-4">
              {recentTools.map((tool) => (
                <div key={tool._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{tool.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{tool.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {new Date(tool.submittedAt).toLocaleDateString('zh-CN')}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        tool.status === 'approved' ? 'bg-green-100 text-green-800' :
                        tool.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {tool.status === 'approved' ? '已通过' :
                         tool.status === 'pending' ? '审核中' : '已拒绝'}
                      </span>
                    </div>
                  </div>
                  {tool.status === 'approved' && (
                    <Link
                      href={`/tools/${tool._id}`}
                      className="ml-4 text-blue-600 hover:text-blue-700"
                    >
                      <Eye className="h-5 w-5" />
                    </Link>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
}
