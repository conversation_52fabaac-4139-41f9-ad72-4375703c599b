'use client';

import React, { useState, Fragment } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import LoginModal from '@/components/auth/LoginModal';
import { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';
import { CATEGORY_OPTIONS } from '@/constants/categories';
import {
  Upload,
  Link as LinkIcon,
  Info
} from 'lucide-react';
import { MAX_TAGS_COUNT } from '@/constants/tags';
import TagSelector from '@/components/TagSelector';

// 使用统一的分类选项配置
const categories = CATEGORY_OPTIONS;

// 使用统一的价格选项配置
const pricingOptions = TOOL_PRICING_FORM_OPTIONS;

interface FormData {
  name: string;
  tagline: string;
  description: string;
  website: string;
  logo: string;
  category: string;
  tags: string[];
  pricing: string;
}

export default function SubmitPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const t = useTranslations('submit');
  const [formData, setFormData] = useState<FormData>({
    name: '',
    tagline: '',
    description: '',
    website: '',
    logo: '',
    category: '',
    tags: [],
    pricing: ''
  });





  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitMessage, setSubmitMessage] = useState('');
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string>('');

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = '工具名称是必填项';
    if (!formData.description.trim()) newErrors.description = '工具描述是必填项';
    if (!formData.website.trim()) newErrors.website = '官方网站是必填项';
    if (!formData.category) newErrors.category = '请选择一个分类';
    if (!formData.pricing) newErrors.pricing = '请选择价格模式';

    // URL validation
    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = '请输入有效的网站地址（以 http:// 或 https:// 开头）';
    }



    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 检查用户是否已登录
    if (!session) {
      setIsLoginModalOpen(true);
      return;
    }

    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      let logoUrl = formData.logo;

      // 如果有选择的logo文件，先上传
      if (logoFile) {
        const logoFormData = new FormData();
        logoFormData.append('logo', logoFile);

        const uploadResponse = await fetch('/api/upload/logo', {
          method: 'POST',
          body: logoFormData,
        });

        const uploadData = await uploadResponse.json();
        if (uploadData.success) {
          logoUrl = uploadData.data.url;
        } else {
          throw new Error(uploadData.message || 'Logo上传失败');
        }
      }

      // 调用新的提交API
      const response = await fetch('/api/tools/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          tagline: formData.tagline,
          description: formData.description,
          website: formData.website,
          logo: logoUrl || undefined,
          category: formData.category,
          tags: formData.tags,
          pricing: formData.pricing
        }),
      });

      const data = await response.json();

      if (data.success) {
        // 跳转到发布日期选择页面
        router.push(`/submit/launch-date/${data.data.toolId}`);
      } else {
        setSubmitStatus('error');
        setSubmitMessage(data.message || '提交失败，请重试');
      }
    } catch (error) {
      console.error('Error submitting tool:', error);
      setSubmitStatus('error');
      setSubmitMessage('网络错误，请检查连接后重试');
    } finally {
      setIsSubmitting(false);
    }
  };



  // 处理logo文件选择
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setLogoFile(file);
      // 创建预览URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Fragment>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            <Upload className="inline-block mr-3 h-8 w-8 text-blue-600" />
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        {/* Success/Error Messages */}
        {submitStatus === 'success' && (
          <SuccessMessage
            message={submitMessage || t('form.success_message')}
            onClose={() => setSubmitStatus('idle')}
            className="mb-6"
          />
        )}

        {submitStatus === 'error' && (
          <ErrorMessage
            message={submitMessage || t('form.error_message')}
            onClose={() => setSubmitStatus('idle')}
            className="mb-6"
          />
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          {/* Basic Information */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('form.basic_info')}</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.tool_name')} *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.name ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder={t('form.tool_name_placeholder')}
                />
                {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.tagline')}
                </label>
                <input
                  type="text"
                  value={formData.tagline}
                  onChange={(e) => setFormData(prev => ({ ...prev, tagline: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('form.tagline_placeholder')}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.website_url')} *
                </label>
                <div className="relative">
                  <input
                    type="url"
                    value={formData.website}
                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.website ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder={t('form.website_url_placeholder')}
                  />
                  <LinkIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                </div>
                {errors.website && <p className="text-red-600 text-sm mt-1">{errors.website}</p>}
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.description')} *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder={t('form.description_placeholder')}
              />
              {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.logo_upload')}
              </label>
              <div className="flex items-center gap-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {logoPreview && (
                  <div className="flex-shrink-0">
                    <img
                      src={logoPreview}
                      alt={t('form.logo_preview')}
                      className="w-16 h-16 object-cover rounded-lg border border-gray-300"
                    />
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                {t('form.logo_upload_hint')}
              </p>
            </div>
          </div>

          {/* Category and Pricing */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">{t('form.category_and_pricing')}</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.category')} *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.category ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">{t('form.category_placeholder')}</option>
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
                {errors.category && <p className="text-red-600 text-sm mt-1">{errors.category}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.pricing_model')} *
                </label>
                <select
                  value={formData.pricing}
                  onChange={(e) => setFormData(prev => ({ ...prev, pricing: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.pricing ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">{t('form.pricing_placeholder')}</option>
                  {pricingOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.pricing && <p className="text-red-600 text-sm mt-1">{errors.pricing}</p>}
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="mb-8">
            <TagSelector
              selectedTags={formData.tags}
              onTagsChange={(tags) => setFormData(prev => ({ ...prev, tags }))}
              maxTags={MAX_TAGS_COUNT}
            />
          </div>



          {/* User Info Display */}
          {session && (
            <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-green-800 mb-2">{t('form.submitter_info')}</h3>
              <p className="text-sm text-green-700">
                {t('form.submitter')}: {session.user?.name || session.user?.email}
              </p>
              <p className="text-sm text-green-700">
                {t('form.email')}: {session.user?.email}
              </p>
            </div>
          )}

          {/* Guidelines */}
          <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start">
              <Info className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-blue-900 mb-2">{t('form.guidelines_title')}</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• {t('form.guideline_1')}</li>
                  <li>• {t('form.guideline_2')}</li>
                  <li>• {t('form.guideline_3')}</li>
                  <li>• {t('form.guideline_4')}</li>
                  <li>• {t('form.guideline_5')}</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                isSubmitting
                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" className="mr-2" />
                  {t('form.submitting')}
                </div>
              ) : (
                t('form.submit_button')
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Fragment>
  );
}