import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// GET /api/user/tools - 获取当前用户提交的工具列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const sort = searchParams.get('sort') || 'submittedAt';
    const order = searchParams.get('order') || 'desc';

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: '用户不存在' },
        { status: 404 }
      );
    }

    // 构建查询条件
    const query: any = {
      submittedBy: user._id.toString()
    };
    
    if (status && status !== 'all') {
      query.status = status;
    }

    // 计算跳过的文档数
    const skip = (page - 1) * limit;

    // 构建排序条件
    const sortOrder = order === 'desc' ? -1 : 1;
    const sortQuery: any = {};
    sortQuery[sort] = sortOrder;

    // 执行查询
    const tools = await Tool.find(query)
      .sort(sortQuery)
      .skip(skip)
      .limit(limit)
      .lean();

    // 获取总数
    const total = await Tool.countDocuments(query);

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // 获取状态统计
    const statusStats = await Tool.aggregate([
      {
        $match: { submittedBy: user._id.toString() }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const stats = {
      total,
      draft: statusStats.find(s => s._id === 'draft')?.count || 0,
      pending: statusStats.find(s => s._id === 'pending')?.count || 0,
      approved: statusStats.find(s => s._id === 'approved')?.count || 0,
      rejected: statusStats.find(s => s._id === 'rejected')?.count || 0,
      totalViews: tools.reduce((sum: number, tool: any) => sum + (tool.views || 0), 0),
      totalLikes: tools.reduce((sum: number, tool: any) => sum + (tool.likes || 0), 0)
    };

    return NextResponse.json({
      success: true,
      data: {
        tools,
        stats,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage
        }
      }
    });

  } catch (error) {
    console.error('Error fetching user tools:', error);
    return NextResponse.json(
      { success: false, message: '获取工具列表失败' },
      { status: 500 }
    );
  }
}
