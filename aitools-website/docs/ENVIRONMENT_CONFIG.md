# 动态环境配置说明

本项目支持动态环境配置，可以自动根据运行时环境检测和设置URL配置，无需在配置文件中硬编码。

## 🚀 主要特性

- **自动环境检测**：支持开发环境、生产环境和各种部署平台
- **动态端口支持**：开发环境可以使用任意端口，无需修改配置
- **部署平台兼容**：自动适配 Vercel、Netlify、Railway 等平台
- **零配置部署**：大多数情况下无需手动设置URL配置

## 📁 相关文件

- `src/lib/env.ts` - 动态环境配置工具
- `.env.local` - 本地开发环境配置
- `.env.example` - 环境配置示例

## 🔧 工作原理

### 1. URL自动检测优先级

系统按以下优先级检测和设置URL：

1. **明确的环境变量**（最高优先级）
   - `NEXT_PUBLIC_APP_URL`
   - `NEXTAUTH_URL`
   - `NEXT_PUBLIC_API_BASE_URL`

2. **部署平台环境变量**
   - Vercel: `VERCEL_URL`
   - Netlify: `URL`
   - Railway: `RAILWAY_STATIC_URL`
   - 通用: `APP_URL`

3. **运行时检测**
   - 服务器端：基于 `PORT` 环境变量
   - 客户端：基于 `window.location`

4. **默认值**（最低优先级）
   - 开发环境：`http://localhost:3001`

### 2. 支持的配置项

```typescript
// 从 src/lib/env.ts 导入
import { dynamicEnv, getBaseUrl, getApiBaseUrl } from '@/lib/env';

// 可用的配置
dynamicEnv.baseUrl        // 应用基础URL
dynamicEnv.apiBaseUrl     // API基础URL
dynamicEnv.nextAuthUrl    // NextAuth URL
dynamicEnv.environment    // 运行环境
dynamicEnv.isDevelopment  // 是否开发环境
dynamicEnv.isProduction   // 是否生产环境
dynamicEnv.port          // 当前端口
```

## 🛠️ 使用方法

### 开发环境

**无需任何配置**，直接启动即可：

```bash
# 使用默认端口 3001
npm run dev

# 使用自定义端口
npm run dev -- --port 3002
npm run dev -- --port 4000
```

系统会自动检测端口并配置相应的URL。

### 生产环境

#### Vercel 部署
无需任何配置，Vercel会自动设置 `VERCEL_URL` 环境变量。

#### Netlify 部署
无需任何配置，Netlify会自动设置 `URL` 环境变量。

#### 自定义域名
如果使用自定义域名，设置环境变量：

```bash
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXTAUTH_URL=https://yourdomain.com
```

#### Docker 部署
```bash
# 设置端口和域名
PORT=3000
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

## 🔍 调试和验证

### 查看当前配置

在开发环境中，启动应用时会在控制台显示当前配置：

```
🔧 Dynamic Environment Configuration:
  Base URL: http://localhost:3002
  API Base URL: http://localhost:3002/api
  NextAuth URL: http://localhost:3002
  Environment: development
  Port: 3002
```

### 手动检查配置

```typescript
import { dynamicEnv } from '@/lib/env';

console.log('Current config:', dynamicEnv);
```

## ⚙️ 高级配置

### 强制指定URL

如果需要强制指定URL（不推荐），可以在 `.env.local` 中设置：

```bash
# 强制指定应用URL
NEXT_PUBLIC_APP_URL=http://localhost:3001

# 强制指定NextAuth URL
NEXTAUTH_URL=http://localhost:3001

# 强制指定API URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
```

### 不同环境的配置

```bash
# .env.development
NEXT_PUBLIC_APP_URL=http://localhost:3001

# .env.production
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# .env.staging
NEXT_PUBLIC_APP_URL=https://staging.yourdomain.com
```

## 🚨 注意事项

1. **客户端vs服务器端**：`NEXT_PUBLIC_*` 变量在客户端可用，其他变量仅在服务器端可用
2. **构建时vs运行时**：某些配置在构建时确定，某些在运行时确定
3. **缓存问题**：修改环境变量后需要重启开发服务器
4. **HTTPS要求**：生产环境的OAuth回调通常需要HTTPS

## 🔗 相关链接

- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [NextAuth.js Configuration](https://next-auth.js.org/configuration/options)
- [Vercel Environment Variables](https://vercel.com/docs/concepts/projects/environment-variables)
