[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx": "82", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts": "83", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "84", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "85", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts": "86", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "87", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "88", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "89", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "90", "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts": "91", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "92", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "93", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "94", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "95", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "96", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "97"}, {"size": 8982, "mtime": 1751080369147, "results": "98", "hashOfConfig": "99"}, {"size": 12560, "mtime": 1751091563790, "results": "100", "hashOfConfig": "99"}, {"size": 17347, "mtime": 1751094258454, "results": "101", "hashOfConfig": "99"}, {"size": 17469, "mtime": 1751100630323, "results": "102", "hashOfConfig": "99"}, {"size": 8201, "mtime": 1751080106824, "results": "103", "hashOfConfig": "99"}, {"size": 6566, "mtime": 1751081195986, "results": "104", "hashOfConfig": "99"}, {"size": 11513, "mtime": 1751080448998, "results": "105", "hashOfConfig": "99"}, {"size": 13916, "mtime": 1751072868169, "results": "106", "hashOfConfig": "99"}, {"size": 5730, "mtime": 1751125136142, "results": "107", "hashOfConfig": "99"}, {"size": 15649, "mtime": 1751125852820, "results": "108", "hashOfConfig": "99"}, {"size": 8048, "mtime": 1751018710311, "results": "109", "hashOfConfig": "99"}, {"size": 10482, "mtime": 1751080503293, "results": "110", "hashOfConfig": "99"}, {"size": 7667, "mtime": 1751086387926, "results": "111", "hashOfConfig": "99"}, {"size": 10389, "mtime": 1750945315721, "results": "112", "hashOfConfig": "99"}, {"size": 21016, "mtime": 1751039634571, "results": "113", "hashOfConfig": "99"}, {"size": 2408, "mtime": 1751105825073, "results": "114", "hashOfConfig": "99"}, {"size": 21004, "mtime": 1750945519465, "results": "115", "hashOfConfig": "99"}, {"size": 22918, "mtime": 1751072978974, "results": "116", "hashOfConfig": "99"}, {"size": 6764, "mtime": 1751005731451, "results": "117", "hashOfConfig": "99"}, {"size": 4621, "mtime": 1751005744888, "results": "118", "hashOfConfig": "99"}, {"size": 15711, "mtime": 1751072831098, "results": "119", "hashOfConfig": "99"}, {"size": 12431, "mtime": 1751004268664, "results": "120", "hashOfConfig": "99"}, {"size": 11961, "mtime": 1751080591727, "results": "121", "hashOfConfig": "99"}, {"size": 4543, "mtime": 1750930937103, "results": "122", "hashOfConfig": "99"}, {"size": 6972, "mtime": 1751018762448, "results": "123", "hashOfConfig": "99"}, {"size": 3885, "mtime": 1751018851458, "results": "124", "hashOfConfig": "99"}, {"size": 5154, "mtime": 1751078866193, "results": "125", "hashOfConfig": "99"}, {"size": 5098, "mtime": 1751105826257, "results": "126", "hashOfConfig": "99"}, {"size": 5321, "mtime": 1750906802986, "results": "127", "hashOfConfig": "99"}, {"size": 1926, "mtime": 1751014639961, "results": "128", "hashOfConfig": "99"}, {"size": 2073, "mtime": 1751038886395, "results": "129", "hashOfConfig": "99"}, {"size": 3121, "mtime": 1751014815428, "results": "130", "hashOfConfig": "99"}, {"size": 171, "mtime": 1750921851894, "results": "131", "hashOfConfig": "99"}, {"size": 3714, "mtime": 1750921931408, "results": "132", "hashOfConfig": "99"}, {"size": 4489, "mtime": 1750930430193, "results": "133", "hashOfConfig": "99"}, {"size": 2140, "mtime": 1751072700801, "results": "134", "hashOfConfig": "99"}, {"size": 3866, "mtime": 1750984404444, "results": "135", "hashOfConfig": "99"}, {"size": 2238, "mtime": 1750995712168, "results": "136", "hashOfConfig": "99"}, {"size": 4057, "mtime": 1751018497440, "results": "137", "hashOfConfig": "99"}, {"size": 5242, "mtime": 1751013821668, "results": "138", "hashOfConfig": "99"}, {"size": 1022, "mtime": 1750984456438, "results": "139", "hashOfConfig": "99"}, {"size": 4403, "mtime": 1750924179468, "results": "140", "hashOfConfig": "99"}, {"size": 11976, "mtime": 1751038689677, "results": "141", "hashOfConfig": "99"}, {"size": 3797, "mtime": 1751088061800, "results": "142", "hashOfConfig": "99"}, {"size": 7403, "mtime": 1751038610858, "results": "143", "hashOfConfig": "99"}, {"size": 3519, "mtime": 1751038552644, "results": "144", "hashOfConfig": "99"}, {"size": 5019, "mtime": 1751038932824, "results": "145", "hashOfConfig": "99"}, {"size": 5538, "mtime": 1750952541605, "results": "146", "hashOfConfig": "99"}, {"size": 2484, "mtime": 1750938669998, "results": "147", "hashOfConfig": "99"}, {"size": 2237, "mtime": 1750949131424, "results": "148", "hashOfConfig": "99"}, {"size": 3202, "mtime": 1750953105864, "results": "149", "hashOfConfig": "99"}, {"size": 230, "mtime": 1751125155513, "results": "150", "hashOfConfig": "99"}, {"size": 2916, "mtime": 1751080520878, "results": "151", "hashOfConfig": "99"}, {"size": 1425, "mtime": 1750903550616, "results": "152", "hashOfConfig": "99"}, {"size": 845, "mtime": 1750908285683, "results": "153", "hashOfConfig": "99"}, {"size": 7687, "mtime": 1751017905894, "results": "154", "hashOfConfig": "99"}, {"size": 3162, "mtime": 1751023074001, "results": "155", "hashOfConfig": "99"}, {"size": 505, "mtime": 1750908273441, "results": "156", "hashOfConfig": "99"}, {"size": 3527, "mtime": 1751018284048, "results": "157", "hashOfConfig": "99"}, {"size": 863, "mtime": 1750908296528, "results": "158", "hashOfConfig": "99"}, {"size": 5768, "mtime": 1750942157899, "results": "159", "hashOfConfig": "99"}, {"size": 4598, "mtime": 1751084769792, "results": "160", "hashOfConfig": "99"}, {"size": 9109, "mtime": 1750930558601, "results": "161", "hashOfConfig": "99"}, {"size": 6661, "mtime": 1751091563612, "results": "162", "hashOfConfig": "99"}, {"size": 4515, "mtime": 1751076728457, "results": "163", "hashOfConfig": "99"}, {"size": 10787, "mtime": 1751076826290, "results": "164", "hashOfConfig": "99"}, {"size": 6839, "mtime": 1751126055408, "results": "165", "hashOfConfig": "99"}, {"size": 867, "mtime": 1750922283437, "results": "166", "hashOfConfig": "99"}, {"size": 362, "mtime": 1750922147686, "results": "167", "hashOfConfig": "99"}, {"size": 8694, "mtime": 1751108580314, "results": "168", "hashOfConfig": "99"}, {"size": 5275, "mtime": 1751023043127, "results": "169", "hashOfConfig": "99"}, {"size": 2605, "mtime": 1751019665417, "results": "170", "hashOfConfig": "99"}, {"size": 8935, "mtime": 1750924218629, "results": "171", "hashOfConfig": "99"}, {"size": 3041, "mtime": 1751085104518, "results": "172", "hashOfConfig": "99"}, {"size": 9621, "mtime": 1751082329362, "results": "173", "hashOfConfig": "99"}, {"size": 8364, "mtime": 1751106462100, "results": "174", "hashOfConfig": "99"}, {"size": 5433, "mtime": 1751023279983, "results": "175", "hashOfConfig": "99"}, {"size": 3048, "mtime": 1751023262678, "results": "176", "hashOfConfig": "99"}, {"size": 3849, "mtime": 1751072665492, "results": "177", "hashOfConfig": "99"}, {"size": 4413, "mtime": 1751019030952, "results": "178", "hashOfConfig": "99"}, {"size": 2449, "mtime": 1750942881883, "results": "179", "hashOfConfig": "99"}, {"size": 5354, "mtime": 1751086484747, "results": "180", "hashOfConfig": "99"}, {"size": 786, "mtime": 1751124839297, "results": "181", "hashOfConfig": "99"}, {"size": 7104, "mtime": 1751122846740, "results": "182", "hashOfConfig": "99"}, {"size": 5474, "mtime": 1751122879189, "results": "183", "hashOfConfig": "99"}, {"size": 3457, "mtime": 1751122835025, "results": "184", "hashOfConfig": "99"}, {"size": 921, "mtime": 1750903252798, "results": "185", "hashOfConfig": "99"}, {"size": 5018, "mtime": 1751073079886, "results": "186", "hashOfConfig": "99"}, {"size": 3274, "mtime": 1751081422931, "results": "187", "hashOfConfig": "99"}, {"size": 3985, "mtime": 1751017840303, "results": "188", "hashOfConfig": "99"}, {"size": 737, "mtime": 1751124850711, "results": "189", "hashOfConfig": "99"}, {"size": 1667, "mtime": 1750903308052, "results": "190", "hashOfConfig": "99"}, {"size": 2141, "mtime": 1750921803605, "results": "191", "hashOfConfig": "99"}, {"size": 3989, "mtime": 1750984256539, "results": "192", "hashOfConfig": "99"}, {"size": 4854, "mtime": 1751073025366, "results": "193", "hashOfConfig": "99"}, {"size": 3406, "mtime": 1751090710634, "results": "194", "hashOfConfig": "99"}, {"size": 720, "mtime": 1750903327281, "results": "195", "hashOfConfig": "99"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 20, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx", ["487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx", ["501", "502", "503", "504", "505", "506", "507", "508", "509", "510"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx", ["511", "512", "513", "514", "515", "516"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx", ["517", "518", "519", "520", "521"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx", ["522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx", ["537", "538", "539", "540", "541", "542", "543", "544"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx", ["545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx", ["560", "561"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx", ["562", "563"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx", ["564", "565", "566", "567", "568"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx", ["569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx", ["583", "584"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx", ["585", "586", "587"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx", ["588"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx", ["589", "590", "591", "592", "593", "594"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx", ["595"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx", ["596", "597"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx", ["598", "599"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx", ["600"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx", ["601", "602", "603"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx", ["604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx", ["624", "625", "626", "627"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx", ["628"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx", ["629"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx", ["630", "631", "632", "633", "634", "635", "636", "637"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["638", "639"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["640", "641"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["642"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["643"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["644"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["645", "646"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["647", "648"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["649", "650", "651"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["652"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["653", "654", "655", "656"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["657"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["658", "659", "660"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["661", "662"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx", ["663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx", ["677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["688", "689", "690"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["691", "692", "693", "694"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", ["695"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["696", "697", "698", "699", "700"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["701"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["702", "703", "704"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx", ["705"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["706"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["707", "708", "709", "710", "711"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts", ["712"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["713"], [], {"ruleId": "714", "severity": 2, "message": "715", "line": 89, "column": 15, "nodeType": "716", "endLine": 92, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 89, "column": 15, "nodeType": "716", "endLine": 92, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 89, "column": 15, "nodeType": "716", "endLine": 92, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 89, "column": 15, "nodeType": "716", "endLine": 92, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 89, "column": 15, "nodeType": "716", "endLine": 92, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 89, "column": 15, "nodeType": "716", "endLine": 92, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 89, "column": 15, "nodeType": "716", "endLine": 92, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "717", "line": 200, "column": 13, "nodeType": "716", "endLine": 203, "endColumn": 14}, {"ruleId": "714", "severity": 2, "message": "717", "line": 200, "column": 13, "nodeType": "716", "endLine": 203, "endColumn": 14}, {"ruleId": "714", "severity": 2, "message": "717", "line": 200, "column": 13, "nodeType": "716", "endLine": 203, "endColumn": 14}, {"ruleId": "714", "severity": 2, "message": "717", "line": 200, "column": 13, "nodeType": "716", "endLine": 203, "endColumn": 14}, {"ruleId": "714", "severity": 2, "message": "717", "line": 200, "column": 13, "nodeType": "716", "endLine": 203, "endColumn": 14}, {"ruleId": "714", "severity": 2, "message": "717", "line": 200, "column": 13, "nodeType": "716", "endLine": 203, "endColumn": 14}, {"ruleId": "714", "severity": 2, "message": "717", "line": 200, "column": 13, "nodeType": "716", "endLine": 203, "endColumn": 14}, {"ruleId": "718", "severity": 2, "message": "719", "line": 11, "column": 3, "nodeType": null, "messageId": "720", "endLine": 11, "endColumn": 8}, {"ruleId": "718", "severity": 2, "message": "721", "line": 16, "column": 3, "nodeType": null, "messageId": "720", "endLine": 16, "endColumn": 8}, {"ruleId": "718", "severity": 2, "message": "722", "line": 17, "column": 3, "nodeType": null, "messageId": "720", "endLine": 17, "endColumn": 11}, {"ruleId": "718", "severity": 2, "message": "723", "line": 20, "column": 3, "nodeType": null, "messageId": "720", "endLine": 20, "endColumn": 7}, {"ruleId": "718", "severity": 2, "message": "724", "line": 25, "column": 7, "nodeType": null, "messageId": "720", "endLine": 25, "endColumn": 21}, {"ruleId": "725", "severity": 1, "message": "726", "line": 48, "column": 6, "nodeType": "727", "endLine": 48, "endColumn": 17, "suggestions": "728"}, {"ruleId": "718", "severity": 2, "message": "729", "line": 62, "column": 14, "nodeType": null, "messageId": "720", "endLine": 62, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "730", "line": 69, "column": 9, "nodeType": null, "messageId": "720", "endLine": 69, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "731", "line": 78, "column": 9, "nodeType": null, "messageId": "720", "endLine": 78, "endColumn": 24}, {"ruleId": "718", "severity": 2, "message": "732", "line": 91, "column": 9, "nodeType": null, "messageId": "720", "endLine": 91, "endColumn": 27}, {"ruleId": "718", "severity": 2, "message": "733", "line": 15, "column": 3, "nodeType": null, "messageId": "720", "endLine": 15, "endColumn": 6}, {"ruleId": "725", "severity": 1, "message": "734", "line": 62, "column": 6, "nodeType": "727", "endLine": 62, "endColumn": 20, "suggestions": "735"}, {"ruleId": "718", "severity": 2, "message": "729", "line": 79, "column": 14, "nodeType": null, "messageId": "720", "endLine": 79, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "729", "line": 112, "column": 14, "nodeType": null, "messageId": "720", "endLine": 112, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "729", "line": 138, "column": 14, "nodeType": null, "messageId": "720", "endLine": 138, "endColumn": 17}, {"ruleId": "736", "severity": 1, "message": "737", "line": 305, "column": 27, "nodeType": "716", "endLine": 309, "endColumn": 29}, {"ruleId": "718", "severity": 2, "message": "729", "line": 75, "column": 14, "nodeType": null, "messageId": "720", "endLine": 75, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "729", "line": 102, "column": 14, "nodeType": null, "messageId": "720", "endLine": 102, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "729", "line": 135, "column": 14, "nodeType": null, "messageId": "720", "endLine": 135, "endColumn": 17}, {"ruleId": "736", "severity": 1, "message": "737", "line": 259, "column": 15, "nodeType": "716", "endLine": 263, "endColumn": 17}, {"ruleId": "736", "severity": 1, "message": "737", "line": 359, "column": 21, "nodeType": "716", "endLine": 364, "endColumn": 23}, {"ruleId": "718", "severity": 2, "message": "738", "line": 109, "column": 12, "nodeType": null, "messageId": "720", "endLine": 109, "endColumn": 17}, {"ruleId": "714", "severity": 2, "message": "715", "line": 226, "column": 15, "nodeType": "716", "endLine": 229, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 226, "column": 15, "nodeType": "716", "endLine": 229, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 226, "column": 15, "nodeType": "716", "endLine": 229, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 226, "column": 15, "nodeType": "716", "endLine": 229, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 226, "column": 15, "nodeType": "716", "endLine": 229, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 226, "column": 15, "nodeType": "716", "endLine": 229, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 226, "column": 15, "nodeType": "716", "endLine": 229, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "739", "line": 238, "column": 17, "nodeType": "716", "endLine": 241, "endColumn": 18}, {"ruleId": "714", "severity": 2, "message": "739", "line": 238, "column": 17, "nodeType": "716", "endLine": 241, "endColumn": 18}, {"ruleId": "714", "severity": 2, "message": "739", "line": 238, "column": 17, "nodeType": "716", "endLine": 241, "endColumn": 18}, {"ruleId": "714", "severity": 2, "message": "739", "line": 238, "column": 17, "nodeType": "716", "endLine": 241, "endColumn": 18}, {"ruleId": "714", "severity": 2, "message": "739", "line": 238, "column": 17, "nodeType": "716", "endLine": 241, "endColumn": 18}, {"ruleId": "714", "severity": 2, "message": "739", "line": 238, "column": 17, "nodeType": "716", "endLine": 241, "endColumn": 18}, {"ruleId": "714", "severity": 2, "message": "739", "line": 238, "column": 17, "nodeType": "716", "endLine": 241, "endColumn": 18}, {"ruleId": "718", "severity": 2, "message": "738", "line": 54, "column": 12, "nodeType": null, "messageId": "720", "endLine": 54, "endColumn": 17}, {"ruleId": "714", "severity": 2, "message": "715", "line": 163, "column": 15, "nodeType": "716", "endLine": 166, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 163, "column": 15, "nodeType": "716", "endLine": 166, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 163, "column": 15, "nodeType": "716", "endLine": 166, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 163, "column": 15, "nodeType": "716", "endLine": 166, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 163, "column": 15, "nodeType": "716", "endLine": 166, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 163, "column": 15, "nodeType": "716", "endLine": 166, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 163, "column": 15, "nodeType": "716", "endLine": 166, "endColumn": 16}, {"ruleId": "718", "severity": 2, "message": "740", "line": 5, "column": 38, "nodeType": null, "messageId": "720", "endLine": 5, "endColumn": 44}, {"ruleId": "714", "severity": 2, "message": "715", "line": 99, "column": 15, "nodeType": "716", "endLine": 102, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 99, "column": 15, "nodeType": "716", "endLine": 102, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 99, "column": 15, "nodeType": "716", "endLine": 102, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 99, "column": 15, "nodeType": "716", "endLine": 102, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 99, "column": 15, "nodeType": "716", "endLine": 102, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 99, "column": 15, "nodeType": "716", "endLine": 102, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 99, "column": 15, "nodeType": "716", "endLine": 102, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "741", "line": 174, "column": 19, "nodeType": "716", "endLine": 174, "endColumn": 80}, {"ruleId": "714", "severity": 2, "message": "741", "line": 174, "column": 19, "nodeType": "716", "endLine": 174, "endColumn": 80}, {"ruleId": "714", "severity": 2, "message": "741", "line": 174, "column": 19, "nodeType": "716", "endLine": 174, "endColumn": 80}, {"ruleId": "714", "severity": 2, "message": "741", "line": 174, "column": 19, "nodeType": "716", "endLine": 174, "endColumn": 80}, {"ruleId": "714", "severity": 2, "message": "741", "line": 174, "column": 19, "nodeType": "716", "endLine": 174, "endColumn": 80}, {"ruleId": "714", "severity": 2, "message": "741", "line": 174, "column": 19, "nodeType": "716", "endLine": 174, "endColumn": 80}, {"ruleId": "714", "severity": 2, "message": "741", "line": 174, "column": 19, "nodeType": "716", "endLine": 174, "endColumn": 80}, {"ruleId": "718", "severity": 2, "message": "724", "line": 23, "column": 7, "nodeType": null, "messageId": "720", "endLine": 23, "endColumn": 21}, {"ruleId": "718", "severity": 2, "message": "729", "line": 100, "column": 14, "nodeType": null, "messageId": "720", "endLine": 100, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "742", "line": 4, "column": 10, "nodeType": null, "messageId": "720", "endLine": 4, "endColumn": 25}, {"ruleId": "718", "severity": 2, "message": "738", "line": 81, "column": 12, "nodeType": null, "messageId": "720", "endLine": 81, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "743", "line": 19, "column": 17, "nodeType": null, "messageId": "720", "endLine": 19, "endColumn": 24}, {"ruleId": "744", "severity": 2, "message": "745", "line": 20, "column": 38, "nodeType": "746", "messageId": "747", "endLine": 20, "endColumn": 41, "suggestions": "748"}, {"ruleId": "725", "severity": 1, "message": "749", "line": 36, "column": 6, "nodeType": "727", "endLine": 36, "endColumn": 23, "suggestions": "750"}, {"ruleId": "718", "severity": 2, "message": "729", "line": 62, "column": 14, "nodeType": null, "messageId": "720", "endLine": 62, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "729", "line": 86, "column": 14, "nodeType": null, "messageId": "720", "endLine": 86, "endColumn": 17}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "717", "line": 225, "column": 22, "nodeType": "716", "endLine": 225, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 225, "column": 22, "nodeType": "716", "endLine": 225, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 225, "column": 22, "nodeType": "716", "endLine": 225, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 225, "column": 22, "nodeType": "716", "endLine": 225, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 225, "column": 22, "nodeType": "716", "endLine": 225, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 225, "column": 22, "nodeType": "716", "endLine": 225, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 225, "column": 22, "nodeType": "716", "endLine": 225, "endColumn": 41}, {"ruleId": "725", "severity": 1, "message": "751", "line": 46, "column": 6, "nodeType": "727", "endLine": 46, "endColumn": 49, "suggestions": "752"}, {"ruleId": "718", "severity": 2, "message": "729", "line": 61, "column": 14, "nodeType": null, "messageId": "720", "endLine": 61, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "753", "line": 11, "column": 3, "nodeType": null, "messageId": "720", "endLine": 11, "endColumn": 7}, {"ruleId": "718", "severity": 2, "message": "754", "line": 18, "column": 3, "nodeType": null, "messageId": "720", "endLine": 18, "endColumn": 7}, {"ruleId": "736", "severity": 1, "message": "737", "line": 97, "column": 19, "nodeType": "716", "endLine": 101, "endColumn": 21}, {"ruleId": "718", "severity": 2, "message": "738", "line": 104, "column": 14, "nodeType": null, "messageId": "720", "endLine": 104, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "755", "line": 12, "column": 3, "nodeType": null, "messageId": "720", "endLine": 12, "endColumn": 7}, {"ruleId": "718", "severity": 2, "message": "733", "line": 15, "column": 3, "nodeType": null, "messageId": "720", "endLine": 15, "endColumn": 6}, {"ruleId": "718", "severity": 2, "message": "729", "line": 94, "column": 14, "nodeType": null, "messageId": "720", "endLine": 94, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "729", "line": 111, "column": 14, "nodeType": null, "messageId": "720", "endLine": 111, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "729", "line": 128, "column": 14, "nodeType": null, "messageId": "720", "endLine": 128, "endColumn": 17}, {"ruleId": "736", "severity": 1, "message": "737", "line": 197, "column": 23, "nodeType": "716", "endLine": 201, "endColumn": 25}, {"ruleId": "736", "severity": 1, "message": "737", "line": 483, "column": 19, "nodeType": "716", "endLine": 487, "endColumn": 21}, {"ruleId": "744", "severity": 2, "message": "745", "line": 16, "column": 36, "nodeType": "746", "messageId": "747", "endLine": 16, "endColumn": 39, "suggestions": "756"}, {"ruleId": "725", "severity": 1, "message": "757", "line": 32, "column": 6, "nodeType": "727", "endLine": 32, "endColumn": 22, "suggestions": "758"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 18, "column": 36, "nodeType": "746", "messageId": "747", "endLine": 18, "endColumn": 39, "suggestions": "759"}, {"ruleId": "725", "severity": 1, "message": "757", "line": 32, "column": 6, "nodeType": "727", "endLine": 32, "endColumn": 22, "suggestions": "760"}, {"ruleId": "736", "severity": 1, "message": "737", "line": 289, "column": 21, "nodeType": "716", "endLine": 293, "endColumn": 23}, {"ruleId": "718", "severity": 2, "message": "743", "line": 12, "column": 17, "nodeType": null, "messageId": "720", "endLine": 12, "endColumn": 24}, {"ruleId": "744", "severity": 2, "message": "745", "line": 13, "column": 36, "nodeType": "746", "messageId": "747", "endLine": 13, "endColumn": 39, "suggestions": "761"}, {"ruleId": "725", "severity": 1, "message": "757", "line": 28, "column": 6, "nodeType": "727", "endLine": 28, "endColumn": 22, "suggestions": "762"}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 66, "column": 15, "nodeType": "716", "endLine": 69, "endColumn": 16}, {"ruleId": "763", "severity": 2, "message": "764", "line": 127, "column": 25, "nodeType": "765", "messageId": "766", "suggestions": "767"}, {"ruleId": "763", "severity": 2, "message": "764", "line": 127, "column": 28, "nodeType": "765", "messageId": "766", "suggestions": "768"}, {"ruleId": "763", "severity": 2, "message": "764", "line": 127, "column": 30, "nodeType": "765", "messageId": "766", "suggestions": "769"}, {"ruleId": "763", "severity": 2, "message": "764", "line": 127, "column": 34, "nodeType": "765", "messageId": "766", "suggestions": "770"}, {"ruleId": "763", "severity": 2, "message": "764", "line": 219, "column": 23, "nodeType": "765", "messageId": "766", "suggestions": "771"}, {"ruleId": "763", "severity": 2, "message": "764", "line": 219, "column": 26, "nodeType": "765", "messageId": "766", "suggestions": "772"}, {"ruleId": "714", "severity": 2, "message": "717", "line": 274, "column": 22, "nodeType": "716", "endLine": 274, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 274, "column": 22, "nodeType": "716", "endLine": 274, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 274, "column": 22, "nodeType": "716", "endLine": 274, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 274, "column": 22, "nodeType": "716", "endLine": 274, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 274, "column": 22, "nodeType": "716", "endLine": 274, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 274, "column": 22, "nodeType": "716", "endLine": 274, "endColumn": 41}, {"ruleId": "714", "severity": 2, "message": "717", "line": 274, "column": 22, "nodeType": "716", "endLine": 274, "endColumn": 41}, {"ruleId": "718", "severity": 2, "message": "738", "line": 27, "column": 14, "nodeType": null, "messageId": "720", "endLine": 27, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "738", "line": 46, "column": 14, "nodeType": null, "messageId": "720", "endLine": 46, "endColumn": 19}, {"ruleId": "744", "severity": 2, "message": "745", "line": 63, "column": 42, "nodeType": "746", "messageId": "747", "endLine": 63, "endColumn": 45, "suggestions": "773"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 64, "column": 42, "nodeType": "746", "messageId": "747", "endLine": 64, "endColumn": 45, "suggestions": "774"}, {"ruleId": "718", "severity": 2, "message": "738", "line": 81, "column": 14, "nodeType": null, "messageId": "720", "endLine": 81, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "738", "line": 69, "column": 12, "nodeType": null, "messageId": "720", "endLine": 69, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "738", "line": 55, "column": 12, "nodeType": null, "messageId": "720", "endLine": 55, "endColumn": 17}, {"ruleId": "714", "severity": 2, "message": "715", "line": 126, "column": 15, "nodeType": "716", "endLine": 129, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 126, "column": 15, "nodeType": "716", "endLine": 129, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 126, "column": 15, "nodeType": "716", "endLine": 129, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 126, "column": 15, "nodeType": "716", "endLine": 129, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 126, "column": 15, "nodeType": "716", "endLine": 129, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 126, "column": 15, "nodeType": "716", "endLine": 129, "endColumn": 16}, {"ruleId": "714", "severity": 2, "message": "715", "line": 126, "column": 15, "nodeType": "716", "endLine": 129, "endColumn": 16}, {"ruleId": "744", "severity": 2, "message": "745", "line": 19, "column": 18, "nodeType": "746", "messageId": "747", "endLine": 19, "endColumn": 21, "suggestions": "775"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 55, "column": 22, "nodeType": "746", "messageId": "747", "endLine": 55, "endColumn": 25, "suggestions": "776"}, {"ruleId": "718", "severity": 2, "message": "777", "line": 8, "column": 27, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 34}, {"ruleId": "744", "severity": 2, "message": "745", "line": 96, "column": 23, "nodeType": "746", "messageId": "747", "endLine": 96, "endColumn": 26, "suggestions": "778"}, {"ruleId": "718", "severity": 2, "message": "779", "line": 1, "column": 10, "nodeType": null, "messageId": "720", "endLine": 1, "endColumn": 21}, {"ruleId": "718", "severity": 2, "message": "780", "line": 27, "column": 13, "nodeType": null, "messageId": "720", "endLine": 27, "endColumn": 26}, {"ruleId": "718", "severity": 2, "message": "781", "line": 5, "column": 8, "nodeType": null, "messageId": "720", "endLine": 5, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "782", "line": 89, "column": 11, "nodeType": null, "messageId": "720", "endLine": 89, "endColumn": 14}, {"ruleId": "744", "severity": 2, "message": "745", "line": 159, "column": 25, "nodeType": "746", "messageId": "747", "endLine": 159, "endColumn": 28, "suggestions": "783"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 179, "column": 20, "nodeType": "746", "messageId": "747", "endLine": 179, "endColumn": 23, "suggestions": "784"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 217, "column": 70, "nodeType": "746", "messageId": "747", "endLine": 217, "endColumn": 73, "suggestions": "785"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 22, "column": 18, "nodeType": "746", "messageId": "747", "endLine": 22, "endColumn": 21, "suggestions": "786"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 66, "column": 22, "nodeType": "746", "messageId": "747", "endLine": 66, "endColumn": 25, "suggestions": "787"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 168, "column": 70, "nodeType": "746", "messageId": "747", "endLine": 168, "endColumn": 73, "suggestions": "788"}, {"ruleId": "718", "severity": 2, "message": "738", "line": 56, "column": 14, "nodeType": null, "messageId": "720", "endLine": 56, "endColumn": 19}, {"ruleId": "744", "severity": 2, "message": "745", "line": 38, "column": 18, "nodeType": "746", "messageId": "747", "endLine": 38, "endColumn": 21, "suggestions": "789"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 51, "column": 22, "nodeType": "746", "messageId": "747", "endLine": 51, "endColumn": 25, "suggestions": "790"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 88, "column": 52, "nodeType": "746", "messageId": "747", "endLine": 88, "endColumn": 55, "suggestions": "791"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 89, "column": 52, "nodeType": "746", "messageId": "747", "endLine": 89, "endColumn": 55, "suggestions": "792"}, {"ruleId": "718", "severity": 2, "message": "729", "line": 55, "column": 14, "nodeType": null, "messageId": "720", "endLine": 55, "endColumn": 17}, {"ruleId": "718", "severity": 2, "message": "738", "line": 44, "column": 14, "nodeType": null, "messageId": "720", "endLine": 44, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "738", "line": 84, "column": 14, "nodeType": null, "messageId": "720", "endLine": 84, "endColumn": 19}, {"ruleId": "718", "severity": 2, "message": "738", "line": 111, "column": 14, "nodeType": null, "messageId": "720", "endLine": 111, "endColumn": 19}, {"ruleId": "736", "severity": 1, "message": "737", "line": 61, "column": 13, "nodeType": "716", "endLine": 65, "endColumn": 15}, {"ruleId": "736", "severity": 1, "message": "737", "line": 93, "column": 21, "nodeType": "716", "endLine": 97, "endColumn": 23}, {"ruleId": "714", "severity": 2, "message": "793", "line": 113, "column": 11, "nodeType": "716", "endLine": 116, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "793", "line": 113, "column": 11, "nodeType": "716", "endLine": 116, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "793", "line": 113, "column": 11, "nodeType": "716", "endLine": 116, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "793", "line": 113, "column": 11, "nodeType": "716", "endLine": 116, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "793", "line": 113, "column": 11, "nodeType": "716", "endLine": 116, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "793", "line": 113, "column": 11, "nodeType": "716", "endLine": 116, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "793", "line": 113, "column": 11, "nodeType": "716", "endLine": 116, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "717", "line": 119, "column": 11, "nodeType": "716", "endLine": 122, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "717", "line": 119, "column": 11, "nodeType": "716", "endLine": 122, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "717", "line": 119, "column": 11, "nodeType": "716", "endLine": 122, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "717", "line": 119, "column": 11, "nodeType": "716", "endLine": 122, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "717", "line": 119, "column": 11, "nodeType": "716", "endLine": 122, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "717", "line": 119, "column": 11, "nodeType": "716", "endLine": 122, "endColumn": 12}, {"ruleId": "714", "severity": 2, "message": "717", "line": 119, "column": 11, "nodeType": "716", "endLine": 122, "endColumn": 12}, {"ruleId": "718", "severity": 2, "message": "781", "line": 7, "column": 10, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 14}, {"ruleId": "718", "severity": 2, "message": "794", "line": 8, "column": 18, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 24}, {"ruleId": "718", "severity": 2, "message": "795", "line": 8, "column": 26, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 30}, {"ruleId": "718", "severity": 2, "message": "796", "line": 8, "column": 32, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 36}, {"ruleId": "718", "severity": 2, "message": "797", "line": 8, "column": 38, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 45}, {"ruleId": "718", "severity": 2, "message": "798", "line": 8, "column": 47, "nodeType": null, "messageId": "720", "endLine": 8, "endColumn": 55}, {"ruleId": "718", "severity": 2, "message": "799", "line": 33, "column": 28, "nodeType": null, "messageId": "720", "endLine": 33, "endColumn": 47}, {"ruleId": "718", "severity": 2, "message": "800", "line": 34, "column": 18, "nodeType": null, "messageId": "720", "endLine": 34, "endColumn": 27}, {"ruleId": "718", "severity": 2, "message": "801", "line": 35, "column": 20, "nodeType": null, "messageId": "720", "endLine": 35, "endColumn": 31}, {"ruleId": "763", "severity": 2, "message": "764", "line": 127, "column": 18, "nodeType": "765", "messageId": "766", "suggestions": "802"}, {"ruleId": "763", "severity": 2, "message": "764", "line": 127, "column": 31, "nodeType": "765", "messageId": "766", "suggestions": "803"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 50, "column": 33, "nodeType": "746", "messageId": "747", "endLine": 50, "endColumn": 36, "suggestions": "804"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 63, "column": 33, "nodeType": "746", "messageId": "747", "endLine": 63, "endColumn": 36, "suggestions": "805"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 185, "column": 22, "nodeType": "746", "messageId": "747", "endLine": 185, "endColumn": 25, "suggestions": "806"}, {"ruleId": "718", "severity": 2, "message": "807", "line": 5, "column": 27, "nodeType": null, "messageId": "720", "endLine": 5, "endColumn": 34}, {"ruleId": "718", "severity": 2, "message": "808", "line": 5, "column": 36, "nodeType": null, "messageId": "720", "endLine": 5, "endColumn": 46}, {"ruleId": "725", "severity": 1, "message": "809", "line": 55, "column": 6, "nodeType": "727", "endLine": 55, "endColumn": 14, "suggestions": "810"}, {"ruleId": "736", "severity": 1, "message": "737", "line": 156, "column": 13, "nodeType": "716", "endLine": 160, "endColumn": 15}, {"ruleId": "725", "severity": 1, "message": "811", "line": 38, "column": 6, "nodeType": "727", "endLine": 38, "endColumn": 42, "suggestions": "812"}, {"ruleId": "718", "severity": 2, "message": "813", "line": 25, "column": 16, "nodeType": null, "messageId": "720", "endLine": 25, "endColumn": 23}, {"ruleId": "725", "severity": 1, "message": "814", "line": 31, "column": 6, "nodeType": "727", "endLine": 31, "endColumn": 21, "suggestions": "815"}, {"ruleId": "718", "severity": 2, "message": "729", "line": 46, "column": 14, "nodeType": null, "messageId": "720", "endLine": 46, "endColumn": 17}, {"ruleId": "736", "severity": 1, "message": "737", "line": 61, "column": 19, "nodeType": "716", "endLine": 65, "endColumn": 21}, {"ruleId": "736", "severity": 1, "message": "737", "line": 190, "column": 27, "nodeType": "716", "endLine": 194, "endColumn": 29}, {"ruleId": "736", "severity": 1, "message": "737", "line": 180, "column": 7, "nodeType": "716", "endLine": 189, "endColumn": 9}, {"ruleId": "718", "severity": 2, "message": "816", "line": 37, "column": 10, "nodeType": null, "messageId": "720", "endLine": 37, "endColumn": 18}, {"ruleId": "817", "severity": 1, "message": "818", "line": 78, "column": 9, "nodeType": "716", "endLine": 82, "endColumn": 11}, {"ruleId": "817", "severity": 1, "message": "818", "line": 92, "column": 7, "nodeType": "716", "endLine": 96, "endColumn": 9}, {"ruleId": "725", "severity": 1, "message": "819", "line": 176, "column": 6, "nodeType": "727", "endLine": 176, "endColumn": 15, "suggestions": "820"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 6, "column": 34, "nodeType": "746", "messageId": "747", "endLine": 6, "endColumn": 37, "suggestions": "821"}, {"ruleId": "718", "severity": 2, "message": "822", "line": 7, "column": 10, "nodeType": null, "messageId": "720", "endLine": 7, "endColumn": 24}, {"ruleId": "744", "severity": 2, "message": "745", "line": 75, "column": 60, "nodeType": "746", "messageId": "747", "endLine": 75, "endColumn": 63, "suggestions": "823"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 155, "column": 31, "nodeType": "746", "messageId": "747", "endLine": 155, "endColumn": 34, "suggestions": "824"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 161, "column": 26, "nodeType": "746", "messageId": "747", "endLine": 161, "endColumn": 29, "suggestions": "825"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 162, "column": 26, "nodeType": "746", "messageId": "747", "endLine": 162, "endColumn": 29, "suggestions": "826"}, {"ruleId": "744", "severity": 2, "message": "745", "line": 85, "column": 35, "nodeType": "746", "messageId": "747", "endLine": 85, "endColumn": 38, "suggestions": "827"}, {"ruleId": "718", "severity": 2, "message": "828", "line": 1, "column": 8, "nodeType": null, "messageId": "720", "endLine": 1, "endColumn": 16}, "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "Do not use an `<a>` element to navigate to `/contact/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["829"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'Eye' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["830"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "'error' is defined but never used.", "Do not use an `<a>` element to navigate to `/categories/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'MapPin' is defined but never used.", "Do not use an `<a>` element to navigate to `/faq/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'useTranslations' is defined but never used.", "'session' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["831", "832"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["833"], "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["834"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", ["835", "836"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["837"], ["838", "839"], ["840"], ["841", "842"], ["843"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["844", "845", "846", "847"], ["848", "849", "850", "851"], ["852", "853", "854", "855"], ["856", "857", "858", "859"], ["860", "861", "862", "863"], ["864", "865", "866", "867"], ["868", "869"], ["870", "871"], ["872", "873"], ["874", "875"], "'request' is defined but never used.", ["876", "877"], "'NextRequest' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'now' is assigned a value but never used.", ["878", "879"], ["880", "881"], ["882", "883"], ["884", "885"], ["886", "887"], ["888", "889"], ["890", "891"], ["892", "893"], ["894", "895"], ["896", "897"], "Do not use an `<a>` element to navigate to `/submit/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'Filter' is defined but never used.", "'Grid' is defined but never used.", "'List' is defined but never used.", "'SortAsc' is defined but never used.", "'SortDesc' is defined but never used.", "'setSelectedCategory' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", ["898", "899", "900", "901"], ["902", "903", "904", "905"], ["906", "907"], ["908", "909"], ["910", "911"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["912"], "React Hook useEffect has a missing dependency: 'initializeToolState'. Either include it or remove the dependency array.", ["913"], "'setTool' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRelatedTools'. Either include it or remove the dependency array.", ["914"], "'hasError' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "React Hook useEffect has missing dependencies: 'refreshToolState' and 'toolStates'. Either include them or remove the dependency array.", ["915"], ["916", "917"], "'getNextAuthUrl' is defined but never used.", ["918", "919"], ["920", "921"], ["922", "923"], ["924", "925"], ["926", "927"], "'mongoose' is defined but never used.", {"desc": "928", "fix": "929"}, {"desc": "930", "fix": "931"}, {"messageId": "932", "fix": "933", "desc": "934"}, {"messageId": "935", "fix": "936", "desc": "937"}, {"desc": "938", "fix": "939"}, {"desc": "940", "fix": "941"}, {"messageId": "932", "fix": "942", "desc": "934"}, {"messageId": "935", "fix": "943", "desc": "937"}, {"desc": "944", "fix": "945"}, {"messageId": "932", "fix": "946", "desc": "934"}, {"messageId": "935", "fix": "947", "desc": "937"}, {"desc": "944", "fix": "948"}, {"messageId": "932", "fix": "949", "desc": "934"}, {"messageId": "935", "fix": "950", "desc": "937"}, {"desc": "944", "fix": "951"}, {"messageId": "952", "data": "953", "fix": "954", "desc": "955"}, {"messageId": "952", "data": "956", "fix": "957", "desc": "958"}, {"messageId": "952", "data": "959", "fix": "960", "desc": "961"}, {"messageId": "952", "data": "962", "fix": "963", "desc": "964"}, {"messageId": "952", "data": "965", "fix": "966", "desc": "955"}, {"messageId": "952", "data": "967", "fix": "968", "desc": "958"}, {"messageId": "952", "data": "969", "fix": "970", "desc": "961"}, {"messageId": "952", "data": "971", "fix": "972", "desc": "964"}, {"messageId": "952", "data": "973", "fix": "974", "desc": "955"}, {"messageId": "952", "data": "975", "fix": "976", "desc": "958"}, {"messageId": "952", "data": "977", "fix": "978", "desc": "961"}, {"messageId": "952", "data": "979", "fix": "980", "desc": "964"}, {"messageId": "952", "data": "981", "fix": "982", "desc": "955"}, {"messageId": "952", "data": "983", "fix": "984", "desc": "958"}, {"messageId": "952", "data": "985", "fix": "986", "desc": "961"}, {"messageId": "952", "data": "987", "fix": "988", "desc": "964"}, {"messageId": "952", "data": "989", "fix": "990", "desc": "955"}, {"messageId": "952", "data": "991", "fix": "992", "desc": "958"}, {"messageId": "952", "data": "993", "fix": "994", "desc": "961"}, {"messageId": "952", "data": "995", "fix": "996", "desc": "964"}, {"messageId": "952", "data": "997", "fix": "998", "desc": "955"}, {"messageId": "952", "data": "999", "fix": "1000", "desc": "958"}, {"messageId": "952", "data": "1001", "fix": "1002", "desc": "961"}, {"messageId": "952", "data": "1003", "fix": "1004", "desc": "964"}, {"messageId": "932", "fix": "1005", "desc": "934"}, {"messageId": "935", "fix": "1006", "desc": "937"}, {"messageId": "932", "fix": "1007", "desc": "934"}, {"messageId": "935", "fix": "1008", "desc": "937"}, {"messageId": "932", "fix": "1009", "desc": "934"}, {"messageId": "935", "fix": "1010", "desc": "937"}, {"messageId": "932", "fix": "1011", "desc": "934"}, {"messageId": "935", "fix": "1012", "desc": "937"}, {"messageId": "932", "fix": "1013", "desc": "934"}, {"messageId": "935", "fix": "1014", "desc": "937"}, {"messageId": "932", "fix": "1015", "desc": "934"}, {"messageId": "935", "fix": "1016", "desc": "937"}, {"messageId": "932", "fix": "1017", "desc": "934"}, {"messageId": "935", "fix": "1018", "desc": "937"}, {"messageId": "932", "fix": "1019", "desc": "934"}, {"messageId": "935", "fix": "1020", "desc": "937"}, {"messageId": "932", "fix": "1021", "desc": "934"}, {"messageId": "935", "fix": "1022", "desc": "937"}, {"messageId": "932", "fix": "1023", "desc": "934"}, {"messageId": "935", "fix": "1024", "desc": "937"}, {"messageId": "932", "fix": "1025", "desc": "934"}, {"messageId": "935", "fix": "1026", "desc": "937"}, {"messageId": "932", "fix": "1027", "desc": "934"}, {"messageId": "935", "fix": "1028", "desc": "937"}, {"messageId": "932", "fix": "1029", "desc": "934"}, {"messageId": "935", "fix": "1030", "desc": "937"}, {"messageId": "932", "fix": "1031", "desc": "934"}, {"messageId": "935", "fix": "1032", "desc": "937"}, {"messageId": "932", "fix": "1033", "desc": "934"}, {"messageId": "935", "fix": "1034", "desc": "937"}, {"messageId": "952", "data": "1035", "fix": "1036", "desc": "955"}, {"messageId": "952", "data": "1037", "fix": "1038", "desc": "958"}, {"messageId": "952", "data": "1039", "fix": "1040", "desc": "961"}, {"messageId": "952", "data": "1041", "fix": "1042", "desc": "964"}, {"messageId": "952", "data": "1043", "fix": "1044", "desc": "955"}, {"messageId": "952", "data": "1045", "fix": "1046", "desc": "958"}, {"messageId": "952", "data": "1047", "fix": "1048", "desc": "961"}, {"messageId": "952", "data": "1049", "fix": "1050", "desc": "964"}, {"messageId": "932", "fix": "1051", "desc": "934"}, {"messageId": "935", "fix": "1052", "desc": "937"}, {"messageId": "932", "fix": "1053", "desc": "934"}, {"messageId": "935", "fix": "1054", "desc": "937"}, {"messageId": "932", "fix": "1055", "desc": "934"}, {"messageId": "935", "fix": "1056", "desc": "937"}, {"desc": "1057", "fix": "1058"}, {"desc": "1059", "fix": "1060"}, {"desc": "1061", "fix": "1062"}, {"desc": "1063", "fix": "1064"}, {"messageId": "932", "fix": "1065", "desc": "934"}, {"messageId": "935", "fix": "1066", "desc": "937"}, {"messageId": "932", "fix": "1067", "desc": "934"}, {"messageId": "935", "fix": "1068", "desc": "937"}, {"messageId": "932", "fix": "1069", "desc": "934"}, {"messageId": "935", "fix": "1070", "desc": "937"}, {"messageId": "932", "fix": "1071", "desc": "934"}, {"messageId": "935", "fix": "1072", "desc": "937"}, {"messageId": "932", "fix": "1073", "desc": "934"}, {"messageId": "935", "fix": "1074", "desc": "937"}, {"messageId": "932", "fix": "1075", "desc": "934"}, {"messageId": "935", "fix": "1076", "desc": "937"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "1077", "text": "1078"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "1079", "text": "1080"}, "suggestUnknown", {"range": "1081", "text": "1082"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1083", "text": "1084"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "1085", "text": "1086"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "1087", "text": "1088"}, {"range": "1089", "text": "1082"}, {"range": "1090", "text": "1084"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "1091", "text": "1092"}, {"range": "1093", "text": "1082"}, {"range": "1094", "text": "1084"}, {"range": "1095", "text": "1092"}, {"range": "1096", "text": "1082"}, {"range": "1097", "text": "1084"}, {"range": "1098", "text": "1092"}, "replaceWithAlt", {"alt": "1099"}, {"range": "1100", "text": "1101"}, "Replace with `&quot;`.", {"alt": "1102"}, {"range": "1103", "text": "1104"}, "Replace with `&ldquo;`.", {"alt": "1105"}, {"range": "1106", "text": "1107"}, "Replace with `&#34;`.", {"alt": "1108"}, {"range": "1109", "text": "1110"}, "Replace with `&rdquo;`.", {"alt": "1099"}, {"range": "1111", "text": "1112"}, {"alt": "1102"}, {"range": "1113", "text": "1114"}, {"alt": "1105"}, {"range": "1115", "text": "1116"}, {"alt": "1108"}, {"range": "1117", "text": "1118"}, {"alt": "1099"}, {"range": "1119", "text": "1120"}, {"alt": "1102"}, {"range": "1121", "text": "1122"}, {"alt": "1105"}, {"range": "1123", "text": "1124"}, {"alt": "1108"}, {"range": "1125", "text": "1126"}, {"alt": "1099"}, {"range": "1127", "text": "1128"}, {"alt": "1102"}, {"range": "1129", "text": "1130"}, {"alt": "1105"}, {"range": "1131", "text": "1132"}, {"alt": "1108"}, {"range": "1133", "text": "1134"}, {"alt": "1099"}, {"range": "1135", "text": "1136"}, {"alt": "1102"}, {"range": "1137", "text": "1138"}, {"alt": "1105"}, {"range": "1139", "text": "1140"}, {"alt": "1108"}, {"range": "1141", "text": "1142"}, {"alt": "1099"}, {"range": "1143", "text": "1144"}, {"alt": "1102"}, {"range": "1145", "text": "1146"}, {"alt": "1105"}, {"range": "1147", "text": "1148"}, {"alt": "1108"}, {"range": "1149", "text": "1150"}, {"range": "1151", "text": "1082"}, {"range": "1152", "text": "1084"}, {"range": "1153", "text": "1082"}, {"range": "1154", "text": "1084"}, {"range": "1155", "text": "1082"}, {"range": "1156", "text": "1084"}, {"range": "1157", "text": "1082"}, {"range": "1158", "text": "1084"}, {"range": "1159", "text": "1082"}, {"range": "1160", "text": "1084"}, {"range": "1161", "text": "1082"}, {"range": "1162", "text": "1084"}, {"range": "1163", "text": "1082"}, {"range": "1164", "text": "1084"}, {"range": "1165", "text": "1082"}, {"range": "1166", "text": "1084"}, {"range": "1167", "text": "1082"}, {"range": "1168", "text": "1084"}, {"range": "1169", "text": "1082"}, {"range": "1170", "text": "1084"}, {"range": "1171", "text": "1082"}, {"range": "1172", "text": "1084"}, {"range": "1173", "text": "1082"}, {"range": "1174", "text": "1084"}, {"range": "1175", "text": "1082"}, {"range": "1176", "text": "1084"}, {"range": "1177", "text": "1082"}, {"range": "1178", "text": "1084"}, {"range": "1179", "text": "1082"}, {"range": "1180", "text": "1084"}, {"alt": "1099"}, {"range": "1181", "text": "1182"}, {"alt": "1102"}, {"range": "1183", "text": "1184"}, {"alt": "1105"}, {"range": "1185", "text": "1186"}, {"alt": "1108"}, {"range": "1187", "text": "1188"}, {"alt": "1099"}, {"range": "1189", "text": "1190"}, {"alt": "1102"}, {"range": "1191", "text": "1192"}, {"alt": "1105"}, {"range": "1193", "text": "1194"}, {"alt": "1108"}, {"range": "1195", "text": "1196"}, {"range": "1197", "text": "1082"}, {"range": "1198", "text": "1084"}, {"range": "1199", "text": "1082"}, {"range": "1200", "text": "1084"}, {"range": "1201", "text": "1082"}, {"range": "1202", "text": "1084"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "1203", "text": "1204"}, "Update the dependencies array to be: [toolId, initialLikes, initialLiked, initializeToolState]", {"range": "1205", "text": "1206"}, "Update the dependencies array to be: [fetchRelatedTools, tool.category]", {"range": "1207", "text": "1208"}, "Update the dependencies array to be: [refreshToolState, session, toolStates]", {"range": "1209", "text": "1210"}, {"range": "1211", "text": "1082"}, {"range": "1212", "text": "1084"}, {"range": "1213", "text": "1082"}, {"range": "1214", "text": "1084"}, {"range": "1215", "text": "1082"}, {"range": "1216", "text": "1084"}, {"range": "1217", "text": "1082"}, {"range": "1218", "text": "1084"}, {"range": "1219", "text": "1082"}, {"range": "1220", "text": "1084"}, {"range": "1221", "text": "1082"}, {"range": "1222", "text": "1084"}, [1125, 1136], "[fetchStats, timeRange]", [1707, 1721], "[fetchTools, statusFilter]", [809, 812], "unknown", [809, 812], "never", [1213, 1230], "[status, orderId, router, fetchOrderInfo]", [1329, 1372], "[filterTools, likedTools, searchQuery, selectedCategory]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875], "&quot;", [4472, 4520], "AI工具导航（以下简称&quot;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&ldquo;", [4472, 4520], "AI工具导航（以下简称&ldquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&#34;", [4472, 4520], "AI工具导航（以下简称&#34;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&rdquo;", [4472, 4520], "AI工具导航（以下简称&rdquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&quot;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&ldquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&#34;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&rdquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&quot;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&ldquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&#34;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&rdquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&quot;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&ldquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&#34;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&rdquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [6921, 6947], "我们的服务按&quot;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&ldquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&#34;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&rdquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&quot;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&ldquo;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&#34;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&rdquo;提供，不提供任何明示或暗示的保证", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [672, 675], [672, 675], [1551, 1554], [1551, 1554], [2591, 2594], [2591, 2594], [4266, 4269], [4266, 4269], [4541, 4544], [4541, 4544], [5532, 5535], [5532, 5535], [802, 805], [802, 805], [1909, 1912], [1909, 1912], [4431, 4434], [4431, 4434], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [3592, 3611], "\n              搜索 &quot;", [3592, 3611], "\n              搜索 &ldquo;", [3592, 3611], "\n              搜索 &#34;", [3592, 3611], "\n              搜索 &rdquo;", [3623, 3643], "&quot; 的结果\n              ", [3623, 3643], "&ldquo; 的结果\n              ", [3623, 3643], "&#34; 的结果\n              ", [3623, 3643], "&rdquo; 的结果\n              ", [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768], [1439, 1447], "[fetchComments, toolId]", [945, 981], "[toolId, initialLikes, initialLiked, initializeToolState]", [908, 923], "[fetchRelatedTools, tool.category]", [4405, 4414], "[refreshToolState, session, toolStates]", [125, 128], [125, 128], [2315, 2318], [2315, 2318], [4647, 4650], [4647, 4650], [4803, 4806], [4803, 4806], [4862, 4865], [4862, 4865], [1724, 1727], [1724, 1727]]