{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "3a971f448f42fbe4e8435a537be0727e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "af624238b053323c8414c4211f9be4cdd3bcb81bc1e3919d07f4960fc613133e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1572829eb73501bec73f820c64913821b70b09cd64a8641962c0520a8f84a2b6"}}}, "sortedMiddleware": ["/"], "functions": {}}