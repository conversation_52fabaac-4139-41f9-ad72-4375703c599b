{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "d907cca3d3e89238db4a8bdf9a4e53b0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d9d274b3a665771a617b78960de029bcf636b2c5423905774ec3b5a161a4aafc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3c4a285cf414da62033afbf2334890451f1aa095273b32f3715ad3509661ffc3"}}}, "sortedMiddleware": ["/"], "functions": {}}