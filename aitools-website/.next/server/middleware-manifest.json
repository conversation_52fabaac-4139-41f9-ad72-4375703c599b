{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "d14dccf4ac5718a17ba6be189258dca0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b1ed2119696e9a3a292825f9123e3d3aeef0abf6f1d60449a4b335ff16951220", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b53fb96446bae555f74048fbccea7f7b535d336fd800c5914e1a445342aab367"}}}, "sortedMiddleware": ["/"], "functions": {}}