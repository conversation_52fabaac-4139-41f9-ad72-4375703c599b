{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4946fe53._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_3d5cd307.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "gdnsvdj5MBZRV476HB5HyWA7AxL3wQ+HHgWRgj0xhxo=", "__NEXT_PREVIEW_MODE_ID": "43274735101a6074f495ef387d67df08", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aa222cf0696953cde8a8045ade0dc17c9c4c0c08e5b9d3510ebe563c38ab4cac", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "867351c43a667380958d069ebe33c30d0b14e0d791746e2054774fc8e4956a21"}}}, "sortedMiddleware": ["/"], "functions": {}}