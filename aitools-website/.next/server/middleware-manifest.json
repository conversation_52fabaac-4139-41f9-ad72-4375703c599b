{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ouU6NN9NZqUn6vOGSXjYHvm6HT4QoytOQr2hzKb9/Hk=", "__NEXT_PREVIEW_MODE_ID": "b28fa5804ce6586f0c7ea77faa4487b6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f787fe67a5323b9544060dcf5caa5b01d34430f55f3ef3c450a3298d861c5a8e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4963d7d5e6e480167b2a59043f6c32aff58e4fbae34c5e90bc5ec6e0e2874a1e"}}}, "sortedMiddleware": ["/"], "functions": {}}