{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4946fe53._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_3d5cd307.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "gdnsvdj5MBZRV476HB5HyWA7AxL3wQ+HHgWRgj0xhxo=", "__NEXT_PREVIEW_MODE_ID": "525e443c1020692ae57c80ea083be622", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3652cbdd8a16116b607d1691818049d763964010714bf9f0cae593a0fa64d8f5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "250553c7f19b59707758cc0bcfa9aa387e5cac43deaaeeaedca701c315117d08"}}}, "sortedMiddleware": ["/"], "functions": {}}