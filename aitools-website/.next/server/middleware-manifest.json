{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4946fe53._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_3d5cd307.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "e6ecf750e30548a23960aa1222cffb50", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "12e4939586dc0fc1ed90c1e983335b60a0973027a1c560accee2df7ffe02222b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "eb360082a21dc822c22e3c76c490da6e33f37beff0e06f0a2e7bb647d751b431"}}}, "sortedMiddleware": ["/"], "functions": {}}