{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\n// 支持的语言列表\nexport const locales = ['zh', 'en'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'zh';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n\n// Next-intl 配置\nexport default getRequestConfig(async ({ locale }) => {\n  // 验证传入的语言是否有效\n  if (!isValidLocale(locale)) {\n    notFound();\n  }\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B;uCAGe,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,cAAc;IACd,IAAI,CAAC,cAAc,SAAS;QAC1B,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,OAAO;QACL,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport { locales, defaultLocale } from './i18n/config';\n\nexport default createMiddleware({\n  // 支持的语言列表\n  locales,\n  \n  // 默认语言\n  defaultLocale,\n  \n  // 语言检测策略\n  localeDetection: true,\n  \n  // 路径前缀策略 - 默认语言不显示前缀\n  localePrefix: 'as-needed',\n  \n  // 备用语言\n  alternateLinks: true,\n});\n\nexport const config = {\n  // 匹配所有路径，除了以下路径：\n  // - API 路由 (/api)\n  // - 静态文件 (_next/static)\n  // - 图片文件 (_next/image)\n  // - favicon.ico\n  // - robots.txt\n  // - sitemap.xml\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE;IAC9B,UAAU;IACV,SAAA,6HAAA,CAAA,UAAO;IAEP,OAAO;IACP,eAAA,6HAAA,CAAA,gBAAa;IAEb,SAAS;IACT,iBAAiB;IAEjB,qBAAqB;IACrB,cAAc;IAEd,OAAO;IACP,gBAAgB;AAClB;AAEO,MAAM,SAAS;IACpB,iBAAiB;IACjB,kBAAkB;IAClB,wBAAwB;IACxB,uBAAuB;IACvB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,SAAS;QACP;KACD;AACH"}}]}