"use strict";exports.id=6804,exports.ids=[6804],exports.modules={5973:(e,t,l)=>{function s(){return null}l.d(t,{default:()=>s}),l(43210)},7485:(e,t,l)=>{l.d(t,{A:()=>n});var s=l(60687);l(43210);var a=l(82136),r=l(23877),i=l(2328);function n({toolId:e,initialLikes:t=0,initialLiked:l=!1,onLoginRequired:n,onUnlike:o,isInLikedPage:c=!1,showCount:d=!0,size:u="md"}){let{data:x}=(0,a.useSession)(),{getToolState:m,initializeToolState:p,toggleLike:b}=(0,i.X)(),h=m(e),g=async()=>{if(!x)return void n?.();if(h.loading)return;let t=h.liked;await b(e,c)&&c&&t&&o&&o(e)},v=(()=>{switch(u){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,s.jsxs)("button",{onClick:g,disabled:h.loading,className:`
        ${v.button}
        inline-flex items-center space-x-1
        ${h.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"}
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
      `,title:h.liked?"取消点赞":"点赞",children:[h.loading?(0,s.jsx)("div",{className:`${v.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`}):h.liked?(0,s.jsx)(r.Mbv,{className:v.icon}):(0,s.jsx)(r.sOK,{className:v.icon}),d&&(0,s.jsx)("span",{className:`${v.text} font-medium`,children:h.likes})]})}},73899:(e,t,l)=>{l.d(t,{default:()=>h});var s=l(60687),a=l(43210),r=l(85814),i=l.n(r),n=l(25334),o=l(13861),c=l(67760),d=l(7485),u=l(30474);function x({src:e,alt:t,width:l,height:r,className:i="",priority:n=!1,fill:o=!1,sizes:c,placeholder:d="empty",blurDataURL:x,fallbackSrc:m="/images/placeholder.svg",onError:p}){let[b,h]=(0,a.useState)(e),[g,v]=(0,a.useState)(!0),[f,E]=(0,a.useState)(!1),N={src:b,alt:t,className:`${i} ${g?"opacity-0":"opacity-100"} transition-opacity duration-300`,onError:()=>{E(!0),v(!1),h(m),p?.()},onLoad:()=>{v(!1)},priority:n,placeholder:"blur"===d?"blur":"empty",blurDataURL:x||("blur"===d?((e=10,t=10)=>{let l=document.createElement("canvas");l.width=e,l.height=t;let s=l.getContext("2d");return s&&(s.fillStyle="#f3f4f6",s.fillRect(0,0,e,t)),l.toDataURL()})():void 0),sizes:c||(o?"100vw":void 0)};return o?(0,s.jsxs)("div",{className:"relative overflow-hidden",children:[(0,s.jsx)(u.default,{...N,fill:!0,style:{objectFit:"cover"}}),g&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]}):(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.default,{...N,width:l,height:r}),g&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse",style:{width:l,height:r}})]})}let m={toolLogo:{width:64,height:64}},p={toolLogo:"64px"};var b=l(94865);let h=({tool:e,onLoginRequired:t,onUnlike:l,isInLikedPage:a=!1})=>(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,s.jsx)(x,{src:e.logo,alt:`${e.name} logo`,width:m.toolLogo.width,height:m.toolLogo.height,className:"rounded-lg object-cover",sizes:p.toolLogo,placeholder:"blur"}):(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,s.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,b.Ef)(e.pricing)}`,children:(0,b.mV)(e.pricing)})]})]}),(0,s.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map((e,t)=>(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),e.tags.length>3&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",e.tags.length-3]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.views})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.likes})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{toolId:e._id,initialLikes:e.likes,initialLiked:a,onLoginRequired:t,onUnlike:l,isInLikedPage:a}),(0,s.jsx)(i(),{href:`/tools/${e._id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:"查看详情"})]})]})]})})},94865:(e,t,l)=>{l.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>n,kX:()=>s,mV:()=>c,tF:()=>u,v4:()=>i,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],n=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],o=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=e=>0===e?"免费":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};