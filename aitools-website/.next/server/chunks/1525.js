exports.id=1525,exports.ids=[1525],exports.modules={2328:(e,t,s)=>{"use strict";s.d(t,{LikeProvider:()=>n,X:()=>c});var r=s(60687),o=s(43210),a=s(82136);let i={liked:!1,likes:0,loading:!1},l=(0,o.createContext)(null);function n({children:e}){let{data:t}=(0,a.useSession)(),[s,n]=(0,o.useState)({}),c=(0,o.useCallback)(e=>s[e]||i,[s]),d=(0,o.useCallback)((e,t,s=!1)=>{n(r=>r[e]?r:{...r,[e]:{liked:s,likes:t,loading:!1}})},[]),m=(0,o.useCallback)(async e=>{if(t)try{let t=await fetch(`/api/tools/${e}/like`);if(t.ok){let s=await t.json();s.success&&n(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[t]),x=(0,o.useCallback)(async(e,s=!1)=>{if(!t)return!1;n(t=>({...t,[e]:{...t[e]||i,loading:!0}}));try{let t=await fetch(`/api/tools/${e}/like`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s?{forceUnlike:!0}:{})});if(t.ok){let s=await t.json();if(s.success)return n(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}})),!0}return n(t=>({...t,[e]:{...t[e]||i,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),n(t=>({...t,[e]:{...t[e]||i,loading:!1}})),!1}},[t]);return(0,r.jsx)(l.Provider,{value:{toolStates:s,toggleLike:x,getToolState:c,initializeToolState:d,refreshToolState:m},children:e})}function c(){let e=(0,o.useContext)(l);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},2401:(e,t,s)=>{Promise.resolve().then(s.bind(s,80994)),Promise.resolve().then(s.bind(s,68926)),Promise.resolve().then(s.bind(s,23440)),Promise.resolve().then(s.bind(s,50290))},3845:(e,t,s)=>{var r={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function o(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],o=t[0];return s.e(t[1]).then(()=>s.t(o,19))}o.keys=()=>Object.keys(r),o.id=3845,e.exports=o},4591:(e,t,s)=>{"use strict";s.d(t,{default:()=>f});var r=s(60687),o=s(43210),a=s(85814),i=s.n(a),l=s(16189),n=s(77618),c=s(23877),d=s(82136),m=s(48577);function x(){let{data:e,status:t}=(0,d.useSession)(),s=(0,l.useRouter)(),[a,i]=(0,o.useState)(!1),[n,x]=(0,o.useState)(!1),u=async()=>{await (0,d.signOut)({callbackUrl:"/"})},h=e=>{x(!1),s.push(e)};return"loading"===t?(0,r.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:"加载中..."}):e?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>x(!n),children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsx)("span",{className:"text-sm hidden md:block",children:e.user?.name}),(0,r.jsx)(c.Vr3,{className:`text-xs transition-transform ${n?"rotate-180":""}`})]}),n&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>x(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-lg font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.user?.name}),(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:e.user?.email}),e.user?.role==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"})]})]})}),(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/profile"),children:[(0,r.jsx)(c.x$1,{}),"个人资料"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/profile/submitted"),children:[(0,r.jsx)(c.svy,{}),"我提交的工具"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/profile/liked"),children:[(0,r.jsx)(c.Mbv,{}),"我的收藏"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/submit"),children:[(0,r.jsx)(c.OiG,{}),"提交工具"]})]}),e.user?.role==="admin"&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"border-t py-2",children:(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/admin"),children:[(0,r.jsx)(c.Pcn,{}),"管理后台"]})})}),(0,r.jsxs)("div",{className:"border-t py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>h("/settings"),children:[(0,r.jsx)(c.Pcn,{}),"设置"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",onClick:u,children:[(0,r.jsx)(c.axc,{}),"退出登录"]})]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>i(!0),children:[(0,r.jsx)(c.Zu,{}),"登录"]}),(0,r.jsx)(m.A,{isOpen:a,onClose:()=>i(!1)})]})}var u=s(29628);let h=["zh","en"],p={zh:"中文",en:"English"};(0,u.M6)(async({locale:e})=>(h.includes(e)||(0,l.notFound)(),{messages:(await s(16997)(`./${e}.json`)).default}));let g=({children:e,href:t})=>(0,r.jsx)(i(),{href:t,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:e}),b=({currentLocale:e})=>{let[t,s]=(0,o.useState)(!1),a=(0,l.usePathname)(),i=(0,l.useRouter)(),n=e=>{let t=(a||"/").replace(/^\/[a-z]{2}/,"")||"/",r="zh"===e?t:`/${e}${t}`;i.push(r),s(!1)};return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>s(!t),className:"flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors",children:[(0,r.jsx)(c.f35,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:p[e]})]}),t&&(0,r.jsx)("div",{className:"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50",children:h.map(t=>(0,r.jsx)("button",{onClick:()=>n(t),className:`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${t===e?"bg-blue-50 text-blue-600":"text-gray-700"}`,children:p[t]},t))})]})};function f(){let[e,t]=(0,o.useState)(!1),s=(0,l.useRouter)(),a=(0,l.usePathname)(),d=(0,n.c3)("navigation"),m=a?.startsWith("/en")?"en":"zh",u=e=>"zh"===m?e:`/en${e}`,h=[{name:d("home"),href:u("/")},{name:d("tools"),href:u("/tools")},{name:d("categories"),href:u("/categories")},{name:d("submit"),href:u("/submit")}],p=e=>{e.preventDefault();let t=new FormData(e.currentTarget).get("search");t.trim()&&s.push(u(`/search?q=${encodeURIComponent(t.trim())}`))};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("header",{className:"bg-white px-4 shadow-sm border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)(i(),{href:u("/"),className:"flex items-center space-x-2 hover:no-underline",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"zh"===m?"AI工具导航":"AI Tools"})]}),(0,r.jsx)("nav",{className:"hidden md:flex space-x-4",children:h.map(e=>(0,r.jsx)(g,{href:e.href,children:e.name},e.name))})]}),(0,r.jsx)("div",{className:"flex-1 max-w-md mx-8 hidden md:block",children:(0,r.jsx)("form",{onSubmit:p,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:d("search_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(b,{currentLocale:m}),(0,r.jsx)(x,{}),(0,r.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>t(!e),"aria-label":"Open Menu",children:e?(0,r.jsx)(c.QCr,{}):(0,r.jsx)(c.OXb,{})})]})]}),e&&(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("nav",{className:"space-y-4",children:[h.map(e=>(0,r.jsx)(g,{href:e.href,children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)("form",{onSubmit:p,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:d("search_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})})]})})]})})}},11434:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b,generateMetadata:()=>g,generateStaticParams:()=>p});var r=s(37413),o=s(49521),a=s.n(o),i=s(7905),l=s.n(i),n=s(88946),c=s(83930),d=s(39916);s(61135);var m=s(23440),x=s(68926),u=s(50290),h=s(12688);async function p(){return h.IB.map(e=>({locale:e}))}async function g({params:e}){return"zh"===e.locale?{title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",keywords:"AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用",authors:[{name:"AI工具导航团队"}],creator:"AI工具导航",publisher:"AI工具导航",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"zh_CN",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:"AI工具导航",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AI工具导航 - 发现最好的人工智能工具"}]},twitter:{card:"summary_large_image",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。",images:["/og-image.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",languages:{zh:"/",en:"/en"}},verification:{google:"your-google-verification-code"}}:{title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.",keywords:"AI tools,artificial intelligence,AI directory,machine learning tools,deep learning,automation tools,AI applications",authors:[{name:"AI Tools Directory Team"}],creator:"AI Tools Directory",publisher:"AI Tools Directory",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:"AI Tools Directory",title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AI Tools Directory - Discover the Best AI Tools"}]},twitter:{card:"summary_large_image",title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity.",images:["/og-image.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",languages:{zh:"/",en:"/en"}},verification:{google:"your-google-verification-code"}}}async function b({children:e,params:{locale:t}}){h.IB.includes(t)||(0,d.notFound)();let s=await (0,c.A)();return(0,r.jsx)("html",{lang:t,children:(0,r.jsx)("body",{className:`${a().variable} ${l().variable} antialiased`,children:(0,r.jsx)(n.A,{messages:s,children:(0,r.jsx)(m.default,{children:(0,r.jsxs)(u.LikeProvider,{children:[(0,r.jsx)(x.default,{}),(0,r.jsx)("main",{children:e})]})})})})})}},12688:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>i,IB:()=>a});var r=s(35471),o=s(39916);let a=["zh","en"],i=(0,r.A)(async({locale:e})=>(a.includes(e)||(0,o.notFound)(),{messages:(await s(3845)(`./${e}.json`)).default}))},15553:(e,t,s)=>{Promise.resolve().then(s.bind(s,45196)),Promise.resolve().then(s.bind(s,4591)),Promise.resolve().then(s.bind(s,76242)),Promise.resolve().then(s.bind(s,2328))},16997:(e,t,s)=>{var r={"./en.json":[41688,1688],"./zh.json":[57873,7873]};function o(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],o=t[0];return s.e(t[1]).then(()=>s.t(o,19))}o.keys=()=>Object.keys(r),o.id=16997,e.exports=o},23440:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx","default")},25536:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},48577:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(60687),o=s(43210),a=s(82136),i=s(23877);function l({isOpen:e,onClose:t}){let[s,l]=(0,o.useState)("method"),[n,c]=(0,o.useState)(""),[d,m]=(0,o.useState)(""),[x,u]=(0,o.useState)(!1),[h,p]=(0,o.useState)(""),g=(e,t="success")=>{let s=document.createElement("div");s.className=`fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${"success"===t?"bg-green-500":"bg-red-500"}`,s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},b=()=>{l("method"),c(""),m(""),p(""),t()},f=async e=>{try{u(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){g("登录失败，请稍后重试","error")}finally{u(!1)}},v=async()=>{if(!n)return void p("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n))return void p("请输入有效的邮箱地址");p(""),u(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:n})}),t=await e.json();t.success?(m(t.token),l("code"),g("验证码已发送，请查看您的邮箱")):g(t.error||"发送失败，请稍后重试","error")}catch(e){g("网络错误，请检查网络连接","error")}finally{u(!1)}},y=async e=>{if(6===e.length){u(!0);try{let t=await (0,a.signIn)("email-code",{email:n,code:e,token:d,redirect:!1});t?.ok?(g("登录成功，欢迎回来！"),b()):g(t?.error||"验证码错误","error")}catch(e){g("网络错误，请检查网络连接","error")}finally{u(!1)}}},j=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");s[e].value=t,t&&e<5&&s[e+1]?.focus();let r=Array.from(s).map(e=>e.value).join("");6===r.length&&y(r)};return e?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:b}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===s&&"登录 AI Tools Directory","email"===s&&"邮箱登录","code"===s&&"输入验证码"]}),(0,r.jsx)("button",{onClick:b,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(i.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>f("google"),disabled:x,children:[(0,r.jsx)(i.DSS,{}),"使用 Google 登录"]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>f("github"),disabled:x,children:[(0,r.jsx)(i.hL4,{}),"使用 GitHub 登录"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>l("email"),children:[(0,r.jsx)(i.maD,{}),"使用邮箱登录"]})]}),"email"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,r.jsx)("input",{type:"email",value:n,onChange:e=>c(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&v(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),h&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:v,disabled:x,children:x?"发送中...":"发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>l("method"),children:"返回"})]})]}),"code"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",n," 的6位验证码"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:t=>j(e,t.target.value),disabled:x,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>l("email"),children:"重新发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>l("method"),children:"返回"})]})]})]})]})]}):null}},50290:(e,t,s)=>{"use strict";s.d(t,{LikeProvider:()=>o});var r=s(12907);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call LikeProvider() from the server but LikeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","LikeProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useLike() from the server but useLike is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","useLike")},61135:()=>{},61984:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},68926:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx","default")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76242:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687),o=s(82136);function a({children:e}){return(0,r.jsx)(o.SessionProvider,{children:e})}},78335:()=>{},94431:(e,t,s)=>{"use strict";function r({children:e}){return e}s.r(t),s.d(t,{default:()=>r})},96487:()=>{}};