{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation';\nimport { headers } from 'next/headers';\n\n// This is the root layout that redirects to the appropriate locale\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  // Get the pathname from headers\n  const headersList = headers();\n  const pathname = headersList.get('x-pathname') || '/';\n\n  // If we're not already in a locale path, redirect to default locale\n  if (!pathname.startsWith('/en') && !pathname.startsWith('/zh')) {\n    redirect('/');\n  }\n\n  return (\n    <html>\n      <body>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;;AAGe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,gCAAgC;IAChC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,WAAW,YAAY,GAAG,CAAC,iBAAiB;IAElD,oEAAoE;IACpE,IAAI,CAAC,SAAS,UAAU,CAAC,UAAU,CAAC,SAAS,UAAU,CAAC,QAAQ;QAC9D,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT", "debugId": null}}]}