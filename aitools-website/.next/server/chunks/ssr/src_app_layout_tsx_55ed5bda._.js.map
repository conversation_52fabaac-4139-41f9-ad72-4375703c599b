{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"], "sourcesContent": ["// This file only exists to satisfy Next.js requirement for a root layout\n// The actual layout is in [locale]/layout.tsx\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": "AAAA,yEAAyE;AACzE,8CAA8C;;;;AAC/B,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}