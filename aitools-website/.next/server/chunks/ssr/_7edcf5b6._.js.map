{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/categories/CategoriesPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/categories/CategoriesPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/categories/CategoriesPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/categories/CategoriesPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n    \n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n    \n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n    \n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n    \n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AACM,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,wCAAmC;QACjC,aAAa;QACb,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC5C;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE;YAC1C,OAAO,QAAQ,GAAG,CAAC,GAAG;QACxB;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAClC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;QAEA,eAAe;QACf,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,CAAC,OAAO;QAC5B;QAEA,UAAU;QACV,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;QACjC,OAAO,CAAC,iBAAiB,EAAE,MAAM;IACnC;;AAUF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,QAAQ,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,wCAAmC;QACjC,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC7B;;AAIF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,gBAAkB,aAAa;IACpD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe,WAAW,OAAO;IAC7C,QAAQ,GAAG,CAAC,mBAAmB,WAAW,UAAU;IACpD,QAAQ,GAAG,CAAC,mBAAmB,WAAW,WAAW;IACrD,QAAQ,GAAG,CAAC,kBAAkB,WAAW,WAAW;IACpD,QAAQ,GAAG,CAAC,WAAW,WAAW,IAAI;AACxC", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts"], "sourcesContent": ["import { Tool } from '@/lib/api';\n\nconst baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';\n\n// 网站基础结构化数据\nexport function getWebsiteStructuredData() {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    \"name\": \"AI工具导航\",\n    \"description\": \"发现最好的AI工具，提升您的工作效率和创造力\",\n    \"url\": baseUrl,\n    \"potentialAction\": {\n      \"@type\": \"SearchAction\",\n      \"target\": {\n        \"@type\": \"EntryPoint\",\n        \"urlTemplate\": `${baseUrl}/tools?search={search_term_string}`\n      },\n      \"query-input\": \"required name=search_term_string\"\n    },\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"AI工具导航\",\n      \"url\": baseUrl\n    }\n  };\n}\n\n// 组织结构化数据\nexport function getOrganizationStructuredData() {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"AI工具导航\",\n    \"description\": \"专业的AI工具发现和推荐平台\",\n    \"url\": baseUrl,\n    \"logo\": `${baseUrl}/logo.png`,\n    \"sameAs\": [\n      // 可以添加社交媒体链接\n    ]\n  };\n}\n\n// 工具详情页结构化数据\nexport function getToolStructuredData(tool: Tool) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"SoftwareApplication\",\n    \"name\": tool.name,\n    \"description\": tool.description,\n    \"url\": tool.website,\n    \"applicationCategory\": \"AI工具\",\n    \"operatingSystem\": \"Web\",\n    \"offers\": {\n      \"@type\": \"Offer\",\n      \"price\": tool.pricing === 'free' ? \"0\" : undefined,\n      \"priceCurrency\": \"USD\",\n      \"availability\": \"https://schema.org/InStock\"\n    },\n    \"aggregateRating\": tool.likes ? {\n      \"@type\": \"AggregateRating\",\n      \"ratingValue\": Math.min(5, Math.max(1, (tool.likes / 10) + 3)), // 简单的评分算法\n      \"reviewCount\": tool.likes,\n      \"bestRating\": 5,\n      \"worstRating\": 1\n    } : undefined,\n    \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`,\n    \"datePublished\": tool.launchDate,\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"AI工具导航\",\n      \"url\": baseUrl\n    }\n  };\n}\n\n// 面包屑导航结构化数据\nexport function getBreadcrumbStructuredData(items: Array<{name: string, url: string}>) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    \"itemListElement\": items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"name\": item.name,\n      \"item\": `${baseUrl}${item.url}`\n    }))\n  };\n}\n\n// 工具列表页结构化数据\nexport function getToolListStructuredData(tools: Tool[], category?: string) {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ItemList\",\n    \"name\": category ? `${category} AI工具` : \"AI工具列表\",\n    \"description\": category ? `发现最好的${category} AI工具` : \"发现最好的AI工具\",\n    \"numberOfItems\": tools.length,\n    \"itemListElement\": tools.map((tool, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"item\": {\n        \"@type\": \"SoftwareApplication\",\n        \"name\": tool.name,\n        \"description\": tool.description,\n        \"url\": `${baseUrl}/tools/${tool._id}`,\n        \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`\n      }\n    }))\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,oBAAoB,IAAI;AAG7C,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,mBAAmB;YACjB,SAAS;YACT,UAAU;gBACR,SAAS;gBACT,eAAe,GAAG,QAAQ,kCAAkC,CAAC;YAC/D;YACA,eAAe;QACjB;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,OAAO;QACT;IACF;AACF;AAGO,SAAS;IACd,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,OAAO;QACP,QAAQ,GAAG,QAAQ,SAAS,CAAC;QAC7B,UAAU,EAET;IACH;AACF;AAGO,SAAS,sBAAsB,IAAU;IAC9C,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ,KAAK,IAAI;QACjB,eAAe,KAAK,WAAW;QAC/B,OAAO,KAAK,OAAO;QACnB,uBAAuB;QACvB,mBAAmB;QACnB,UAAU;YACR,SAAS;YACT,SAAS,KAAK,OAAO,KAAK,SAAS,MAAM;YACzC,iBAAiB;YACjB,gBAAgB;QAClB;QACA,mBAAmB,KAAK,KAAK,GAAG;YAC9B,SAAS;YACT,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,AAAC,KAAK,KAAK,GAAG,KAAM;YAC3D,eAAe,KAAK,KAAK;YACzB,cAAc;YACd,eAAe;QACjB,IAAI;QACJ,SAAS,KAAK,IAAI,IAAI,GAAG,QAAQ,uBAAuB,CAAC;QACzD,iBAAiB,KAAK,UAAU;QAChC,aAAa;YACX,SAAS;YACT,QAAQ;YACR,OAAO;QACT;IACF;AACF;AAGO,SAAS,4BAA4B,KAAyC;IACnF,OAAO;QACL,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,GAAG,UAAU,KAAK,GAAG,EAAE;YACjC,CAAC;IACH;AACF;AAGO,SAAS,0BAA0B,KAAa,EAAE,QAAiB;IACxE,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ,WAAW,GAAG,SAAS,KAAK,CAAC,GAAG;QACxC,eAAe,WAAW,CAAC,KAAK,EAAE,SAAS,KAAK,CAAC,GAAG;QACpD,iBAAiB,MAAM,MAAM;QAC7B,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ;oBACN,SAAS;oBACT,QAAQ,KAAK,IAAI;oBACjB,eAAe,KAAK,WAAW;oBAC/B,OAAO,GAAG,QAAQ,OAAO,EAAE,KAAK,GAAG,EAAE;oBACrC,SAAS,KAAK,IAAI,IAAI,GAAG,QAAQ,uBAAuB,CAAC;gBAC3D;YACF,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/categories/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { Metadata } from 'next';\nimport { getTranslations } from 'next-intl/server';\nimport CategoriesPageClient from '@/components/categories/CategoriesPageClient';\nimport { CATEGORY_BASE_METADATA } from '@/constants/categories-i18n';\nimport { apiClient } from '@/lib/api';\nimport { getBreadcrumbStructuredData } from '@/lib/seo/structuredData';\nimport { type Locale } from '@/i18n/config';\n\ninterface Props {\n  params: Promise<{ locale: Locale }>;\n}\n\n// 生成动态metadata\nexport async function generateMetadata({ params }: Props): Promise<Metadata> {\n  const { locale } = await params;\n  const t = await getTranslations({ locale, namespace: 'categories' });\n\n  try {\n    // 获取分类数据用于动态描述\n    const response = await apiClient.getCategories();\n\n    const totalCategories = response.success && response.data ? response.data.categories.length : 0;\n    const totalTools = response.success && response.data ? response.data.overview.totalTools : 0;\n\n    const title = t('page_title');\n    const description = t('page_description', { categories: totalCategories, tools: totalTools });\n    const keywords = t('page_keywords');\n\n    return {\n      title,\n      description,\n      keywords,\n      authors: [{ name: locale === 'zh' ? 'AI工具导航团队' : 'AI Tools Directory Team' }],\n      robots: {\n        index: true,\n        follow: true,\n      },\n      openGraph: {\n        type: 'website',\n        locale: locale === 'zh' ? 'zh_CN' : 'en_US',\n        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/categories`,\n        siteName: t('site_name'),\n        title,\n        description,\n        images: [\n          {\n            url: '/og-categories.jpg',\n            width: 1200,\n            height: 630,\n            alt: title,\n          },\n        ],\n      },\n      twitter: {\n        card: 'summary_large_image',\n        title,\n        description,\n        images: ['/og-categories.jpg'],\n      },\n      alternates: {\n        canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/categories`,\n      },\n    };\n  } catch (error) {\n    // 如果获取数据失败，返回默认metadata\n    const fallbackTitle = locale === 'zh' ? 'AI工具分类 - 按功能浏览人工智能工具' : 'AI Tools Categories - Browse AI Tools by Function';\n    const fallbackDescription = locale === 'zh'\n      ? '按功能分类浏览AI工具，包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。'\n      : 'Browse AI tools by function categories, including text generation, image creation, data analysis, automation, audio processing and other AI tool categories.';\n    const fallbackKeywords = locale === 'zh'\n      ? 'AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类'\n      : 'AI tools categories,artificial intelligence categories,AI tool types,machine learning categories,deep learning tool categories,AI application categories,smart tool categories';\n\n    return {\n      title: fallbackTitle,\n      description: fallbackDescription,\n      keywords: fallbackKeywords,\n    };\n  }\n}\n\n// 服务端数据获取函数\nasync function getCategoriesData() {\n  try {\n    const response = await apiClient.getCategories();\n\n    if (response.success && response.data) {\n      // 转换API数据格式为组件期望的格式\n      const transformedCategories = response.data.categories.map((apiCategory: { id: string; name: string; count: number }) => {\n        // 使用统一的分类元数据映射\n        const metadata = CATEGORY_METADATA[apiCategory.id] || {\n          description: '优质AI工具集合',\n          icon: '🔧',\n          color: '#6B7280'\n        };\n\n        return {\n          _id: apiCategory.id,\n          name: apiCategory.name,\n          slug: apiCategory.id,\n          description: metadata.description,\n          icon: metadata.icon,\n          color: metadata.color,\n          toolCount: apiCategory.count\n        };\n      });\n\n      return {\n        categories: transformedCategories,\n        error: null\n      };\n    } else {\n      return {\n        categories: [],\n        error: response.error || 'Failed to fetch categories list'\n      };\n    }\n  } catch (error) {\n    console.error('Failed to fetch categories:', error);\n    return {\n      categories: [],\n      error: 'Failed to fetch categories list, please try again later'\n    };\n  }\n}\n\nexport default async function CategoriesPage({ params }: Props) {\n  const { locale } = await params;\n  const { categories, error } = await getCategoriesData();\n  const t = await getTranslations({ locale, namespace: 'categories' });\n\n  // 生成结构化数据\n  const breadcrumbStructuredData = getBreadcrumbStructuredData([\n    { name: t('breadcrumb_home'), url: `/${locale}` },\n    { name: t('breadcrumb_categories'), url: `/${locale}/categories` }\n  ]);\n\n  // 生成分类列表结构化数据\n  const categoriesStructuredData = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"ItemList\",\n    \"name\": t('structured_data_name'),\n    \"description\": t('structured_data_description'),\n    \"numberOfItems\": categories.length,\n    \"itemListElement\": categories.map((category, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"item\": {\n        \"@type\": \"Thing\",\n        \"name\": category.name,\n        \"description\": category.description,\n        \"url\": `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/${locale}/categories/${category.slug}`,\n        \"additionalProperty\": {\n          \"@type\": \"PropertyValue\",\n          \"name\": t('structured_data_tool_count'),\n          \"value\": category.toolCount\n        }\n      }\n    }))\n  };\n\n  return (\n    <>\n      {/* 结构化数据 */}\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(breadcrumbStructuredData)\n        }}\n      />\n      <script\n        type=\"application/ld+json\"\n        dangerouslySetInnerHTML={{\n          __html: JSON.stringify(categoriesStructuredData)\n        }}\n      />\n\n      {/* 面包屑导航 */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n        <nav className=\"flex\" aria-label={t('breadcrumb_aria_label')}>\n          <ol className=\"inline-flex items-center space-x-1 md:space-x-3\">\n            <li className=\"inline-flex items-center\">\n              <a\n                href={`/${locale}`}\n                className=\"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600\"\n              >\n                {t('breadcrumb_home')}\n              </a>\n            </li>\n            <li>\n              <div className=\"flex items-center\">\n                <svg className=\"w-6 h-6 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                </svg>\n                <span className=\"ml-1 text-sm font-medium text-gray-500 md:ml-2\">{t('breadcrumb_categories')}</span>\n              </div>\n            </li>\n          </ol>\n        </nav>\n      </div>\n\n      <CategoriesPageClient categories={categories} error={error} locale={locale} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;;;;;;AAQO,eAAe,iBAAiB,EAAE,MAAM,EAAS;IACtD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAElE,IAAI;QACF,eAAe;QACf,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,aAAa;QAE9C,MAAM,kBAAkB,SAAS,OAAO,IAAI,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;QAC9F,MAAM,aAAa,SAAS,OAAO,IAAI,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE3F,MAAM,QAAQ,EAAE;QAChB,MAAM,cAAc,EAAE,oBAAoB;YAAE,YAAY;YAAiB,OAAO;QAAW;QAC3F,MAAM,WAAW,EAAE;QAEnB,OAAO;YACL;YACA;YACA;YACA,SAAS;gBAAC;oBAAE,MAAM,WAAW,OAAO,aAAa;gBAA0B;aAAE;YAC7E,QAAQ;gBACN,OAAO;gBACP,QAAQ;YACV;YACA,WAAW;gBACT,MAAM;gBACN,QAAQ,WAAW,OAAO,UAAU;gBACpC,KAAK,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,8BAA8B,CAAC,EAAE,OAAO,WAAW,CAAC;gBAChG,UAAU,EAAE;gBACZ;gBACA;gBACA,QAAQ;oBACN;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,KAAK;oBACP;iBACD;YACH;YACA,SAAS;gBACP,MAAM;gBACN;gBACA;gBACA,QAAQ;oBAAC;iBAAqB;YAChC;YACA,YAAY;gBACV,WAAW,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,8BAA8B,CAAC,EAAE,OAAO,WAAW,CAAC;YACxG;QACF;IACF,EAAE,OAAO,OAAO;QACd,wBAAwB;QACxB,MAAM,gBAAgB,WAAW,OAAO,yBAAyB;QACjE,MAAM,sBAAsB,WAAW,OACnC,sDACA;QACJ,MAAM,mBAAmB,WAAW,OAChC,uDACA;QAEJ,OAAO;YACL,OAAO;YACP,aAAa;YACb,UAAU;QACZ;IACF;AACF;AAEA,YAAY;AACZ,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,aAAa;QAE9C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;YACrC,oBAAoB;YACpB,MAAM,wBAAwB,SAAS,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC1D,eAAe;gBACf,MAAM,WAAW,iBAAiB,CAAC,YAAY,EAAE,CAAC,IAAI;oBACpD,aAAa;oBACb,MAAM;oBACN,OAAO;gBACT;gBAEA,OAAO;oBACL,KAAK,YAAY,EAAE;oBACnB,MAAM,YAAY,IAAI;oBACtB,MAAM,YAAY,EAAE;oBACpB,aAAa,SAAS,WAAW;oBACjC,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,WAAW,YAAY,KAAK;gBAC9B;YACF;YAEA,OAAO;gBACL,YAAY;gBACZ,OAAO;YACT;QACF,OAAO;YACL,OAAO;gBACL,YAAY,EAAE;gBACd,OAAO,SAAS,KAAK,IAAI;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YACL,YAAY,EAAE;YACd,OAAO;QACT;IACF;AACF;AAEe,eAAe,eAAe,EAAE,MAAM,EAAS;IAC5D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM;IACpC,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAElE,UAAU;IACV,MAAM,2BAA2B,CAAA,GAAA,mIAAA,CAAA,8BAA2B,AAAD,EAAE;QAC3D;YAAE,MAAM,EAAE;YAAoB,KAAK,CAAC,CAAC,EAAE,QAAQ;QAAC;QAChD;YAAE,MAAM,EAAE;YAA0B,KAAK,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC;QAAC;KAClE;IAED,cAAc;IACd,MAAM,2BAA2B;QAC/B,YAAY;QACZ,SAAS;QACT,QAAQ,EAAE;QACV,eAAe,EAAE;QACjB,iBAAiB,WAAW,MAAM;QAClC,mBAAmB,WAAW,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;gBACtD,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ;oBACN,SAAS;oBACT,QAAQ,SAAS,IAAI;oBACrB,eAAe,SAAS,WAAW;oBACnC,OAAO,GAAG,QAAQ,GAAG,CAAC,oBAAoB,IAAI,8BAA8B,CAAC,EAAE,OAAO,YAAY,EAAE,SAAS,IAAI,EAAE;oBACnH,sBAAsB;wBACpB,SAAS;wBACT,QAAQ,EAAE;wBACV,SAAS,SAAS,SAAS;oBAC7B;gBACF;YACF,CAAC;IACH;IAEA,qBACE;;0BAEE,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAEF,8OAAC;gBACC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBACzB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAO,cAAY,EAAE;8BAClC,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCACC,MAAM,CAAC,CAAC,EAAE,QAAQ;oCAClB,WAAU;8CAET,EAAE;;;;;;;;;;;0CAGP,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;sDAE3J,8OAAC;4CAAK,WAAU;sDAAkD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9E,8OAAC,wJAAA,CAAA,UAAoB;gBAAC,YAAY;gBAAY,OAAO;gBAAO,QAAQ;;;;;;;;AAG1E", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG;oBAAc,IAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BACNC,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}