{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport default function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    const metrics: PerformanceMetrics = {};\n\n    // 监控 First Contentful Paint (FCP)\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n        if (fcpEntry) {\n          metrics.fcp = fcpEntry.startTime;\n          reportMetric('FCP', fcpEntry.startTime);\n        }\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n\n    // 监控 Largest Contentful Paint (LCP)\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        reportMetric('LCP', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n\n    // 监控 First Input Delay (FID)\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          reportMetric('FID', entry.processingStart - entry.startTime);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n\n    // 监控 Cumulative Layout Shift (CLS)\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        reportMetric('CLS', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n\n    // 监控 Time to First Byte (TTFB)\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        metrics.ttfb = ttfb;\n        reportMetric('TTFB', ttfb);\n      }\n    };\n\n    // 报告性能指标\n    const reportMetric = (name: string, value: number) => {\n      // 在开发环境中输出到控制台\n      if (process.env.NODE_ENV === 'development') {\n        console.log(`Performance Metric - ${name}:`, value);\n      }\n\n      // 在生产环境中可以发送到分析服务\n      // 例如 Google Analytics, Vercel Analytics 等\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', 'web_vitals', {\n          event_category: 'Performance',\n          event_label: name,\n          value: Math.round(value),\n          non_interaction: true,\n        });\n      }\n    };\n\n    // 检查浏览器支持\n    if (typeof PerformanceObserver !== 'undefined') {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n    }\n\n    observeTTFB();\n\n    // 页面卸载时报告最终指标\n    const reportFinalMetrics = () => {\n      if (Object.keys(metrics).length > 0) {\n        // 可以发送到分析服务\n        console.log('Final Performance Metrics:', metrics);\n      }\n    };\n\n    window.addEventListener('beforeunload', reportFinalMetrics);\n\n    return () => {\n      window.removeEventListener('beforeunload', reportFinalMetrics);\n    };\n  }, []);\n\n  return null; // 这是一个无UI的监控组件\n}\n\n// 性能优化建议\nexport const PerformanceOptimizations = {\n  // FCP 优化建议\n  fcp: {\n    good: 1800, // < 1.8s\n    needsImprovement: 3000, // 1.8s - 3s\n    suggestions: [\n      '减少服务器响应时间',\n      '消除阻塞渲染的资源',\n      '压缩CSS和JavaScript',\n      '使用CDN加速资源加载',\n    ],\n  },\n  \n  // LCP 优化建议\n  lcp: {\n    good: 2500, // < 2.5s\n    needsImprovement: 4000, // 2.5s - 4s\n    suggestions: [\n      '优化图片加载',\n      '预加载关键资源',\n      '减少JavaScript执行时间',\n      '使用服务端渲染',\n    ],\n  },\n  \n  // FID 优化建议\n  fid: {\n    good: 100, // < 100ms\n    needsImprovement: 300, // 100ms - 300ms\n    suggestions: [\n      '减少JavaScript执行时间',\n      '分割长任务',\n      '使用Web Workers',\n      '延迟加载非关键JavaScript',\n    ],\n  },\n  \n  // CLS 优化建议\n  cls: {\n    good: 0.1, // < 0.1\n    needsImprovement: 0.25, // 0.1 - 0.25\n    suggestions: [\n      '为图片和视频设置尺寸属性',\n      '避免在现有内容上方插入内容',\n      '使用transform动画而非改变布局的动画',\n      '预留广告位空间',\n    ],\n  },\n};\n\n// 声明全局gtag类型\ndeclare global {\n  interface Window {\n    gtag?: (...args: any[]) => void;\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAYe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;QAChB,wCAA2C;YACzC;QACF;;QAEA,MAAM;QAEN,kCAAkC;QAClC,MAAM;QAYN,oCAAoC;QACpC,MAAM;QAUN,6BAA6B;QAC7B,MAAM;QAWN,mCAAmC;QACnC,MAAM;QAeN,+BAA+B;QAC/B,MAAM;QASN,SAAS;QACT,MAAM;QA4BN,cAAc;QACd,MAAM;IAYR,GAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B;AAGO,MAAM,2BAA2B;IACtC,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport PerformanceMonitor from '@/components/seo/PerformanceMonitor';\nimport { Locale } from '@/i18n/config';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const params = useParams();\n  const locale = params?.locale as Locale || 'zh';\n  const t = useTranslations('layout');\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <PerformanceMonitor />\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                {t('footer.description')}\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                {t('footer.quick_links')}\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href={`/${locale}/tools`} className=\"text-gray-600 hover:text-blue-600\">\n                    {t('footer.tools_directory')}\n                  </Link>\n                </li>\n                <li>\n                  <Link href={`/${locale}/categories`} className=\"text-gray-600 hover:text-blue-600\">\n                    {t('footer.browse_categories')}\n                  </Link>\n                </li>\n                <li>\n                  <Link href={`/${locale}/submit`} className=\"text-gray-600 hover:text-blue-600\">\n                    {t('footer.submit_tool')}\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                {t('footer.support')}\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    {t('footer.help_center')}\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    {t('footer.contact_us')}\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    {t('footer.privacy_policy')}\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              {t('footer.copyright')}\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAaA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,QAAQ,UAAoB;IAC3C,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+IAAA,CAAA,UAAkB;;;;;0BAGnB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;8CAIP,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC;wDAAE,WAAU;kEACvC,EAAE;;;;;;;;;;;8DAGP,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC;wDAAE,WAAU;kEAC5C,EAAE;;;;;;;;;;;8DAGP,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC;wDAAE,WAAU;kEACxC,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAMX,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;sDAEL,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEACnB,EAAE;;;;;;;;;;;8DAGP,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEACnB,EAAE;;;;;;;;;;;8DAGP,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEACnB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;uCAEe", "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\nimport { useLike } from '@/contexts/LikeContext';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean;\n  showCount?: boolean; // 是否显示点赞数量\n  size?: 'sm' | 'md' | 'lg'; // 按钮大小\n}\n\nexport default function LikeButton({\n  toolId,\n  initialLikes = 0,\n  initialLiked = false,\n  onLoginRequired,\n  onUnlike,\n  isInLikedPage = false,\n  showCount = true,\n  size = 'md'\n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const { getToolState, initializeToolState, toggleLike } = useLike();\n\n  // 获取当前工具的状态\n  const toolState = getToolState(toolId);\n\n  // 初始化工具状态\n  useEffect(() => {\n    initializeToolState(toolId, initialLikes, initialLiked);\n  }, [toolId, initialLikes, initialLiked]); // 移除initializeToolState依赖，避免无限循环\n\n  // 处理点赞点击\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (toolState.loading) return;\n\n    // 记录操作前的状态\n    const wasLiked = toolState.liked;\n\n    // 执行点赞操作\n    const success = await toggleLike(toolId, isInLikedPage);\n\n    // 如果是在收藏页面且从已点赞变为未点赞，调用onUnlike回调\n    if (success && isInLikedPage && wasLiked && onUnlike) {\n      onUnlike(toolId);\n    }\n  };\n\n  // 根据size确定样式\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return {\n          button: 'p-1.5',\n          icon: 'h-4 w-4',\n          text: 'text-sm'\n        };\n      case 'lg':\n        return {\n          button: 'p-3',\n          icon: 'h-6 w-6',\n          text: 'text-lg'\n        };\n      default: // md\n        return {\n          button: 'p-2',\n          icon: 'h-5 w-5',\n          text: 'text-base'\n        };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={toolState.loading}\n      className={`\n        ${sizeClasses.button}\n        inline-flex items-center space-x-1\n        ${toolState.liked\n          ? 'text-red-500 hover:text-red-600'\n          : 'text-gray-400 hover:text-red-500'\n        }\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      `}\n      title={toolState.liked ? '取消点赞' : '点赞'}\n    >\n      {toolState.loading ? (\n        <div className={`${sizeClasses.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`} />\n      ) : toolState.liked ? (\n        <FaHeart className={sizeClasses.icon} />\n      ) : (\n        <FaRegHeart className={sizeClasses.icon} />\n      )}\n      {showCount && (\n        <span className={`${sizeClasses.text} font-medium`}>\n          {toolState.likes}\n        </span>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAkBe,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACf,QAAQ,EACR,gBAAgB,KAAK,EACrB,YAAY,IAAI,EAChB,OAAO,IAAI,EACK;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhE,YAAY;IACZ,MAAM,YAAY,aAAa;IAE/B,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB,QAAQ,cAAc;IAC5C,GAAG;QAAC;QAAQ;QAAc;KAAa,GAAG,iCAAiC;IAE3E,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,UAAU,OAAO,EAAE;QAEvB,WAAW;QACX,MAAM,WAAW,UAAU,KAAK;QAEhC,SAAS;QACT,MAAM,UAAU,MAAM,WAAW,QAAQ;QAEzC,kCAAkC;QAClC,IAAI,WAAW,iBAAiB,YAAY,UAAU;YACpD,SAAS;QACX;IACF;IAEA,aAAa;IACb,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF;gBACE,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QACC,SAAS;QACT,UAAU,UAAU,OAAO;QAC3B,WAAW,CAAC;QACV,EAAE,YAAY,MAAM,CAAC;;QAErB,EAAE,UAAU,KAAK,GACb,oCACA,mCACH;;;MAGH,CAAC;QACD,OAAO,UAAU,KAAK,GAAG,SAAS;;YAEjC,UAAU,OAAO,iBAChB,8OAAC;gBAAI,WAAW,GAAG,YAAY,IAAI,CAAC,oEAAoE,CAAC;;;;;uBACvG,UAAU,KAAK,iBACjB,8OAAC,8IAAA,CAAA,UAAO;gBAAC,WAAW,YAAY,IAAI;;;;;qCAEpC,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAW,YAAY,IAAI;;;;;;YAExC,2BACC,8OAAC;gBAAK,WAAW,GAAG,YAAY,IAAI,CAAC,YAAY,CAAC;0BAC/C,UAAU,KAAK;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  sizes?: string;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  fallbackSrc?: string;\n  onError?: () => void;\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  fill = false,\n  sizes,\n  placeholder = 'empty',\n  blurDataURL,\n  fallbackSrc = '/images/placeholder.svg',\n  onError,\n}: OptimizedImageProps) {\n  const [imgSrc, setImgSrc] = useState(src);\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n\n  const handleError = () => {\n    setHasError(true);\n    setIsLoading(false);\n    setImgSrc(fallbackSrc);\n    onError?.();\n  };\n\n  const handleLoad = () => {\n    setIsLoading(false);\n  };\n\n  // 生成模糊占位符\n  const generateBlurDataURL = (w: number = 10, h: number = 10) => {\n    const canvas = document.createElement('canvas');\n    canvas.width = w;\n    canvas.height = h;\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      ctx.fillStyle = '#f3f4f6';\n      ctx.fillRect(0, 0, w, h);\n    }\n    return canvas.toDataURL();\n  };\n\n  const imageProps = {\n    src: imgSrc,\n    alt,\n    className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,\n    onError: handleError,\n    onLoad: handleLoad,\n    priority,\n    placeholder: placeholder === 'blur' ? 'blur' as const : 'empty' as const,\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || (fill ? '100vw' : undefined),\n  };\n\n  if (fill) {\n    return (\n      <div className=\"relative overflow-hidden\">\n        <Image\n          {...imageProps}\n          fill\n          style={{ objectFit: 'cover' }}\n        />\n        {isLoading && (\n          <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      <Image\n        {...imageProps}\n        width={width}\n        height={height}\n      />\n      {isLoading && (\n        <div \n          className=\"absolute inset-0 bg-gray-200 animate-pulse\"\n          style={{ width, height }}\n        />\n      )}\n    </div>\n  );\n}\n\n// 预设的图片尺寸配置\nexport const ImageSizes = {\n  avatar: { width: 40, height: 40 },\n  avatarLarge: { width: 80, height: 80 },\n  toolLogo: { width: 64, height: 64 },\n  toolLogoLarge: { width: 128, height: 128 },\n  thumbnail: { width: 200, height: 150 },\n  card: { width: 300, height: 200 },\n  hero: { width: 1200, height: 600 },\n} as const;\n\n// 响应式图片尺寸字符串\nexport const ResponsiveSizes = {\n  avatar: '40px',\n  toolLogo: '64px',\n  thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  hero: '100vw',\n  full: '100vw',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAoBe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,cAAc,OAAO,EACrB,WAAW,EACX,cAAc,yBAAyB,EACvC,OAAO,EACa;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,YAAY;QACZ,aAAa;QACb,UAAU;QACV;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,UAAU;IACV,MAAM,sBAAsB,CAAC,IAAY,EAAE,EAAE,IAAY,EAAE;QACzD,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,KAAK;YACP,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG;QACxB;QACA,OAAO,OAAO,SAAS;IACzB;IAEA,MAAM,aAAa;QACjB,KAAK;QACL;QACA,WAAW,GAAG,UAAU,CAAC,EAAE,YAAY,cAAc,cAAc,gCAAgC,CAAC;QACpG,SAAS;QACT,QAAQ;QACR;QACA,aAAa,gBAAgB,SAAS,SAAkB;QACxD,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS,CAAC,OAAO,UAAU,SAAS;IAC7C;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6HAAA,CAAA,UAAK;oBACH,GAAG,UAAU;oBACd,IAAI;oBACJ,OAAO;wBAAE,WAAW;oBAAQ;;;;;;gBAE7B,2BACC,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6HAAA,CAAA,UAAK;gBACH,GAAG,UAAU;gBACd,OAAO;gBACP,QAAQ;;;;;;YAET,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE;oBAAO;gBAAO;;;;;;;;;;;;AAKjC;AAGO,MAAM,aAAa;IACxB,QAAQ;QAAE,OAAO;QAAI,QAAQ;IAAG;IAChC,aAAa;QAAE,OAAO;QAAI,QAAQ;IAAG;IACrC,UAAU;QAAE,OAAO;QAAI,QAAQ;IAAG;IAClC,eAAe;QAAE,OAAO;QAAK,QAAQ;IAAI;IACzC,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,MAAM;QAAE,OAAO;QAAK,QAAQ;IAAI;IAChC,MAAM;QAAE,OAAO;QAAM,QAAQ;IAAI;AACnC;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,UAAU;IACV,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport { Eye, Heart, ExternalLink } from 'lucide-react';\nimport LikeButton from './tools/LikeButton';\nimport OptimizedImage, { ImageSizes, ResponsiveSizes } from './ui/OptimizedImage';\nimport { getToolPricingColor, getToolPricingText } from '@/constants/pricing';\nimport { Locale } from '@/i18n/config';\n\ninterface ToolCardProps {\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n    website: string;\n    logo?: string;\n    category: string;\n    tags: string[];\n    pricing: 'free' | 'freemium' | 'paid';\n    views: number;\n    likes: number;\n  };\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean; // 新增：标识是否在liked页面\n}\n\nconst ToolCard: React.FC<ToolCardProps> = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false }) => {\n  const params = useParams();\n  const locale = params?.locale as Locale || 'zh';\n  const t = useTranslations('common');\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            {tool.logo ? (\n              <OptimizedImage\n                src={tool.logo}\n                alt={`${tool.name} logo`}\n                width={ImageSizes.toolLogo.width}\n                height={ImageSizes.toolLogo.height}\n                className=\"rounded-lg object-cover\"\n                sizes={ResponsiveSizes.toolLogo}\n                placeholder=\"blur\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tool.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {tool.name}\n              </h3>\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getToolPricingColor(tool.pricing)}`}>\n                {getToolPricingText(tool.pricing)}\n              </span>\n            </div>\n          </div>\n          \n          <a\n            href={tool.website}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n          >\n            <ExternalLink className=\"h-5 w-5\" />\n          </a>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {tool.description}\n        </p>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {tool.tags.slice(0, 3).map((tag, index) => (\n            <span\n              key={index}\n              className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\"\n            >\n              {tag}\n            </span>\n          ))}\n          {tool.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\">\n              +{tool.tags.length - 3}\n            </span>\n          )}\n        </div>\n\n        {/* Stats and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"h-4 w-4\" />\n              <span>{tool.views}</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Heart className=\"h-4 w-4\" />\n              <span>{tool.likes}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <LikeButton\n              toolId={tool._id}\n              initialLikes={tool.likes}\n              initialLiked={isInLikedPage} // 在收藏页面，所有工具都应该是已点赞状态\n              onLoginRequired={onLoginRequired}\n              onUnlike={onUnlike}\n              isInLikedPage={isInLikedPage}\n            />\n            <Link\n              href={`/${locale}/tools/${tool._id}`}\n              className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\"\n            >\n              {t('view_details')}\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AATA;;;;;;;;;AA8BA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,gBAAgB,KAAK,EAAE;IACnG,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,QAAQ,UAAoB;IAC3C,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,iBACR,8OAAC,0IAAA,CAAA,UAAc;oCACb,KAAK,KAAK,IAAI;oCACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;oCACxB,OAAO,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK;oCAChC,QAAQ,0IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;oCAClC,WAAU;oCACV,OAAO,0IAAA,CAAA,kBAAe,CAAC,QAAQ;oCAC/B,aAAY;;;;;yDAGd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8CAItC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAW,CAAC,wEAAwE,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,OAAO,GAAG;sDAC5H,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,OAAO;;;;;;;;;;;;;;;;;;sCAKtC,8OAAC;4BACC,MAAM,KAAK,OAAO;4BAClB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAE,WAAU;8BACV,KAAK,WAAW;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;wBAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;4BAAK,WAAU;;gCAA8F;gCAC1G,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM3B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yIAAA,CAAA,UAAU;oCACT,QAAQ,KAAK,GAAG;oCAChB,cAAc,KAAK,KAAK;oCACxB,cAAc;oCACd,iBAAiB;oCACjB,UAAU;oCACV,eAAe;;;;;;8CAEjB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG,EAAE;oCACpC,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;uCAEe", "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n    \n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n    \n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n    \n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n    \n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AACM,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,wCAAmC;QACjC,aAAa;QACb,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC5C;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE;YAC1C,OAAO,QAAQ,GAAG,CAAC,GAAG;QACxB;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAClC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;QAEA,eAAe;QACf,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,CAAC,OAAO;QAC5B;QAEA,UAAU;QACV,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;QACjC,OAAO,CAAC,iBAAiB,EAAE,MAAM;IACnC;;AAUF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,QAAQ,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,wCAAmC;QACjC,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC7B;;AAIF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,gBAAkB,aAAa;IACpD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe,WAAW,OAAO;IAC7C,QAAQ,GAAG,CAAC,mBAAmB,WAAW,UAAU;IACpD,QAAQ,GAAG,CAAC,mBAAmB,WAAW,WAAW;IACrD,QAAQ,GAAG,CAAC,kBAAkB,WAAW,WAAW;IACpD,QAAQ,GAAG,CAAC,WAAW,WAAW,IAAI;AACxC", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 1295, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport Layout from '@/components/Layout';\nimport ToolCard from '@/components/ToolCard';\nimport { Tool, ToolsResponse, Category, apiClient } from '@/lib/api';\nimport { Search, Filter, Grid, List, SortAsc, SortDesc } from 'lucide-react';\nimport { type Locale } from '@/i18n/config';\n\ninterface SearchPageClientProps {\n  initialQuery: string;\n  initialResults: ToolsResponse | null;\n  initialCategories: Category[];\n  initialPage?: number;\n  initialCategory?: string;\n  initialSort?: string;\n  locale: Locale;\n}\n\nexport default function SearchPageClient({\n  initialQuery,\n  initialResults,\n  initialCategories,\n  initialPage = 1,\n  initialCategory = '',\n  initialSort = 'createdAt',\n  locale,\n}: SearchPageClientProps) {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const t = useTranslations('search');\n\n  const [searchTerm, setSearchTerm] = useState(initialQuery);\n  const [results, setResults] = useState<ToolsResponse | null>(initialResults);\n  const [categories] = useState<Category[]>(initialCategories);\n  const [selectedCategory, setSelectedCategory] = useState(initialCategory);\n  const [sortBy, setSortBy] = useState(initialSort);\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(initialPage);\n\n  // 更新URL参数\n  const updateURL = (params: Record<string, string>) => {\n    const newSearchParams = new URLSearchParams(searchParams || '');\n\n    Object.entries(params).forEach(([key, value]) => {\n      if (value) {\n        newSearchParams.set(key, value);\n      } else {\n        newSearchParams.delete(key);\n      }\n    });\n\n    router.push(`/search?${newSearchParams.toString()}`);\n  };\n\n  // 执行搜索\n  const performSearch = async (query: string = '', page: number = 1, category: string = '', sort: string = 'createdAt') => {\n    // if (!query.trim()) {\n    //   setResults(null);\n    //   return;\n    // }\n\n    setLoading(true);\n    try {\n      const response = await apiClient.getTools({\n        search: query,\n        page,\n        limit: 12,\n        category: category || undefined,\n        sort,\n        order: 'desc',\n      });\n\n      if (response.success) {\n        setResults(response.data || null);\n      } else {\n        console.error('Search failed:', response.error);\n        setResults(null);\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setResults(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理搜索提交\n  const handleSearch = (e: React.FormEvent) => {\n    e?.preventDefault();\n    // if (searchTerm.trim()) {\n    setCurrentPage(1);\n    updateURL({\n      q: searchTerm?.trim(),\n      page: '1',\n      category: selectedCategory,\n      sort: sortBy,\n    });\n    performSearch(searchTerm?.trim(), 1, selectedCategory, sortBy);\n    // }\n  };\n\n  // 处理分页\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n    updateURL({\n      q: searchTerm,\n      page: page.toString(),\n      category: selectedCategory,\n      sort: sortBy,\n    });\n    performSearch(searchTerm, page, selectedCategory, sortBy);\n  };\n\n  useEffect(() => {\n    if(!searchTerm.trim()) {\n      performSearch('', 1, selectedCategory, sortBy);\n    }\n  }, [searchTerm, selectedCategory, sortBy])\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">搜索 AI 工具</h1>\n          {searchTerm && (\n            <p className=\"text-lg text-gray-600\">\n              搜索 \"{searchTerm}\" 的结果\n              {results && ` - 找到 ${results.pagination.totalItems} 个工具`}\n            </p>\n          )}\n        </div>\n\n        {/* Search Form */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <form onSubmit={handleSearch}>\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"搜索工具名称、描述或标签...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n              <Search className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n              <button\n                type=\"submit\"\n                className=\"absolute right-2 top-2 px-4 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n                style={{\n                  whiteSpace: 'nowrap',\n                  marginLeft: 10\n                }}\n              >\n                {\n                  !searchTerm.trim() && '全部'\n                }\n                {\n                  searchTerm.trim() && '搜索'\n                }\n              </button>\n            </div>\n          </form>\n        </div>\n\n        {/* 如果没有搜索关键词，显示提示 */}\n        {!searchTerm && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <Search className=\"h-12 w-12 mx-auto\" />\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">开始搜索 AI 工具</h3>\n            <p className=\"text-gray-600\">\n              输入关键词来搜索工具名称、描述或标签\n            </p>\n          </div>\n        )}\n\n        {/* 搜索结果 */}\n\n        {/* Filters and Controls */}\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"text-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">{t('searching')}</p>\n          </div>\n        )}\n\n        {/* Results */}\n        {!loading && results && (\n          <>\n            {/* Results Count */}\n            <div className=\"mb-6\">\n              <p className=\"text-gray-600\">\n                {t('results_count', {\n                  showing: results.tools.length,\n                  total: results.pagination.totalItems\n                })}\n                {selectedCategory && ` ${t('in_category', { category: categories.find(c => c.id === selectedCategory)?.name || '' })}`}\n              </p>\n            </div>\n\n            {/* Tools Grid/List */}\n            {results.tools.length > 0 ? (\n              <>\n                <div className={viewMode === 'grid'\n                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8'\n                  : 'space-y-4 mb-8'\n                }>\n                  {results.tools.map((tool) => (\n                    <ToolCard key={tool._id} tool={tool} />\n                  ))}\n                </div>\n\n                {/* Pagination */}\n                {results.pagination.totalPages > 1 && (\n                  <div className=\"flex justify-center\">\n                    <nav className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={() => handlePageChange(currentPage - 1)}\n                        disabled={!results.pagination.hasPrevPage}\n                        className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        上一页\n                      </button>\n\n                      <span className=\"px-3 py-2 text-sm text-gray-700\">\n                        第 {currentPage} 页，共 {results.pagination.totalPages} 页\n                      </span>\n\n                      <button\n                        onClick={() => handlePageChange(currentPage + 1)}\n                        disabled={!results.pagination.hasNextPage}\n                        className=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        下一页\n                      </button>\n                    </nav>\n                  </div>\n                )}\n              </>\n            ) : (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 mb-4\">\n                  <Search className=\"h-12 w-12 mx-auto\" />\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">{t('no_results')}</h3>\n                <p className=\"text-gray-600\">\n                  {t('try_different_keywords')}\n                </p>\n              </div>\n            )}\n          </>\n        )}\n\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAqBe,SAAS,iBAAiB,EACvC,YAAY,EACZ,cAAc,EACd,iBAAiB,EACjB,cAAc,CAAC,EACf,kBAAkB,EAAE,EACpB,cAAc,WAAW,EACzB,MAAM,EACgB;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7D,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,UAAU;IACV,MAAM,YAAY,CAAC;QACjB,MAAM,kBAAkB,IAAI,gBAAgB,gBAAgB;QAE5D,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,OAAO;gBACT,gBAAgB,GAAG,CAAC,KAAK;YAC3B,OAAO;gBACL,gBAAgB,MAAM,CAAC;YACzB;QACF;QAEA,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,gBAAgB,QAAQ,IAAI;IACrD;IAEA,OAAO;IACP,MAAM,gBAAgB,OAAO,QAAgB,EAAE,EAAE,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,OAAe,WAAW;QAClH,uBAAuB;QACvB,sBAAsB;QACtB,YAAY;QACZ,IAAI;QAEJ,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;gBACxC,QAAQ;gBACR;gBACA,OAAO;gBACP,UAAU,YAAY;gBACtB;gBACA,OAAO;YACT;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,WAAW,SAAS,IAAI,IAAI;YAC9B,OAAO;gBACL,QAAQ,KAAK,CAAC,kBAAkB,SAAS,KAAK;gBAC9C,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,GAAG;QACH,2BAA2B;QAC3B,eAAe;QACf,UAAU;YACR,GAAG,YAAY;YACf,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA,cAAc,YAAY,QAAQ,GAAG,kBAAkB;IACvD,IAAI;IACN;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,UAAU;YACR,GAAG;YACH,MAAM,KAAK,QAAQ;YACnB,UAAU;YACV,MAAM;QACR;QACA,cAAc,YAAY,MAAM,kBAAkB;IACpD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAG,CAAC,WAAW,IAAI,IAAI;YACrB,cAAc,IAAI,GAAG,kBAAkB;QACzC;IACF,GAAG;QAAC;QAAY;QAAkB;KAAO;IAEzC,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;wBACrD,4BACC,8OAAC;4BAAE,WAAU;;gCAAwB;gCAC9B;gCAAW;gCACf,WAAW,CAAC,MAAM,EAAE,QAAQ,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC;;;;;;;;;;;;;8BAM9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,YAAY;oCACd;;wCAGE,CAAC,WAAW,IAAI,MAAM;wCAGtB,WAAW,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;gBAQ9B,CAAC,4BACA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAWhC,yBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAsB,EAAE;;;;;;;;;;;;gBAKxC,CAAC,WAAW,yBACX;;sCAEE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCACV,EAAE,iBAAiB;wCAClB,SAAS,QAAQ,KAAK,CAAC,MAAM;wCAC7B,OAAO,QAAQ,UAAU,CAAC,UAAU;oCACtC;oCACC,oBAAoB,CAAC,CAAC,EAAE,EAAE,eAAe;wCAAE,UAAU,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB,QAAQ;oCAAG,IAAI;;;;;;;;;;;;wBAKzH,QAAQ,KAAK,CAAC,MAAM,GAAG,kBACtB;;8CACE,8OAAC;oCAAI,WAAW,aAAa,SACzB,8DACA;8CAED,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,8HAAA,CAAA,UAAQ;4CAAgB,MAAM;2CAAhB,KAAK,GAAG;;;;;;;;;;gCAK1B,QAAQ,UAAU,CAAC,UAAU,GAAG,mBAC/B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc;gDAC9C,UAAU,CAAC,QAAQ,UAAU,CAAC,WAAW;gDACzC,WAAU;0DACX;;;;;;0DAID,8OAAC;gDAAK,WAAU;;oDAAkC;oDAC7C;oDAAY;oDAAM,QAAQ,UAAU,CAAC,UAAU;oDAAC;;;;;;;0DAGrD,8OAAC;gDACC,SAAS,IAAM,iBAAiB,cAAc;gDAC9C,UAAU,CAAC,QAAQ,UAAU,CAAC,WAAW;gDACzC,WAAU;0DACX;;;;;;;;;;;;;;;;;;yDAQT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CAA0C,EAAE;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB", "debugId": null}}]}