{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport default function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    const metrics: PerformanceMetrics = {};\n\n    // 监控 First Contentful Paint (FCP)\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n        if (fcpEntry) {\n          metrics.fcp = fcpEntry.startTime;\n          reportMetric('FCP', fcpEntry.startTime);\n        }\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n\n    // 监控 Largest Contentful Paint (LCP)\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        reportMetric('LCP', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n\n    // 监控 First Input Delay (FID)\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          reportMetric('FID', entry.processingStart - entry.startTime);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n\n    // 监控 Cumulative Layout Shift (CLS)\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        reportMetric('CLS', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n\n    // 监控 Time to First Byte (TTFB)\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        metrics.ttfb = ttfb;\n        reportMetric('TTFB', ttfb);\n      }\n    };\n\n    // 报告性能指标\n    const reportMetric = (name: string, value: number) => {\n      // 在开发环境中输出到控制台\n      if (process.env.NODE_ENV === 'development') {\n        console.log(`Performance Metric - ${name}:`, value);\n      }\n\n      // 在生产环境中可以发送到分析服务\n      // 例如 Google Analytics, Vercel Analytics 等\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', 'web_vitals', {\n          event_category: 'Performance',\n          event_label: name,\n          value: Math.round(value),\n          non_interaction: true,\n        });\n      }\n    };\n\n    // 检查浏览器支持\n    if (typeof PerformanceObserver !== 'undefined') {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n    }\n\n    observeTTFB();\n\n    // 页面卸载时报告最终指标\n    const reportFinalMetrics = () => {\n      if (Object.keys(metrics).length > 0) {\n        // 可以发送到分析服务\n        console.log('Final Performance Metrics:', metrics);\n      }\n    };\n\n    window.addEventListener('beforeunload', reportFinalMetrics);\n\n    return () => {\n      window.removeEventListener('beforeunload', reportFinalMetrics);\n    };\n  }, []);\n\n  return null; // 这是一个无UI的监控组件\n}\n\n// 性能优化建议\nexport const PerformanceOptimizations = {\n  // FCP 优化建议\n  fcp: {\n    good: 1800, // < 1.8s\n    needsImprovement: 3000, // 1.8s - 3s\n    suggestions: [\n      '减少服务器响应时间',\n      '消除阻塞渲染的资源',\n      '压缩CSS和JavaScript',\n      '使用CDN加速资源加载',\n    ],\n  },\n  \n  // LCP 优化建议\n  lcp: {\n    good: 2500, // < 2.5s\n    needsImprovement: 4000, // 2.5s - 4s\n    suggestions: [\n      '优化图片加载',\n      '预加载关键资源',\n      '减少JavaScript执行时间',\n      '使用服务端渲染',\n    ],\n  },\n  \n  // FID 优化建议\n  fid: {\n    good: 100, // < 100ms\n    needsImprovement: 300, // 100ms - 300ms\n    suggestions: [\n      '减少JavaScript执行时间',\n      '分割长任务',\n      '使用Web Workers',\n      '延迟加载非关键JavaScript',\n    ],\n  },\n  \n  // CLS 优化建议\n  cls: {\n    good: 0.1, // < 0.1\n    needsImprovement: 0.25, // 0.1 - 0.25\n    suggestions: [\n      '为图片和视频设置尺寸属性',\n      '避免在现有内容上方插入内容',\n      '使用transform动画而非改变布局的动画',\n      '预留广告位空间',\n    ],\n  },\n};\n\n// 声明全局gtag类型\ndeclare global {\n  interface Window {\n    gtag?: (...args: any[]) => void;\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAYe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;QAChB,wCAA2C;YACzC;QACF;;QAEA,MAAM;QAEN,kCAAkC;QAClC,MAAM;QAYN,oCAAoC;QACpC,MAAM;QAUN,6BAA6B;QAC7B,MAAM;QAWN,mCAAmC;QACnC,MAAM;QAeN,+BAA+B;QAC/B,MAAM;QASN,SAAS;QACT,MAAM;QA4BN,cAAc;QACd,MAAM;IAYR,GAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B;AAGO,MAAM,2BAA2B;IACtC,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\nimport { useLike } from '@/contexts/LikeContext';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean;\n  showCount?: boolean; // 是否显示点赞数量\n  size?: 'sm' | 'md' | 'lg'; // 按钮大小\n}\n\nexport default function LikeButton({\n  toolId,\n  initialLikes = 0,\n  initialLiked = false,\n  onLoginRequired,\n  onUnlike,\n  isInLikedPage = false,\n  showCount = true,\n  size = 'md'\n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const { getToolState, initializeToolState, toggleLike } = useLike();\n\n  // 获取当前工具的状态\n  const toolState = getToolState(toolId);\n\n  // 初始化工具状态\n  useEffect(() => {\n    initializeToolState(toolId, initialLikes, initialLiked);\n  }, [toolId, initialLikes, initialLiked]); // 移除initializeToolState依赖，避免无限循环\n\n  // 处理点赞点击\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (toolState.loading) return;\n\n    // 记录操作前的状态\n    const wasLiked = toolState.liked;\n\n    // 执行点赞操作\n    const success = await toggleLike(toolId, isInLikedPage);\n\n    // 如果是在收藏页面且从已点赞变为未点赞，调用onUnlike回调\n    if (success && isInLikedPage && wasLiked && onUnlike) {\n      onUnlike(toolId);\n    }\n  };\n\n  // 根据size确定样式\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return {\n          button: 'p-1.5',\n          icon: 'h-4 w-4',\n          text: 'text-sm'\n        };\n      case 'lg':\n        return {\n          button: 'p-3',\n          icon: 'h-6 w-6',\n          text: 'text-lg'\n        };\n      default: // md\n        return {\n          button: 'p-2',\n          icon: 'h-5 w-5',\n          text: 'text-base'\n        };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={toolState.loading}\n      className={`\n        ${sizeClasses.button}\n        inline-flex items-center space-x-1\n        ${toolState.liked\n          ? 'text-red-500 hover:text-red-600'\n          : 'text-gray-400 hover:text-red-500'\n        }\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      `}\n      title={toolState.liked ? '取消点赞' : '点赞'}\n    >\n      {toolState.loading ? (\n        <div className={`${sizeClasses.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`} />\n      ) : toolState.liked ? (\n        <FaHeart className={sizeClasses.icon} />\n      ) : (\n        <FaRegHeart className={sizeClasses.icon} />\n      )}\n      {showCount && (\n        <span className={`${sizeClasses.text} font-medium`}>\n          {toolState.likes}\n        </span>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAkBe,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACf,QAAQ,EACR,gBAAgB,KAAK,EACrB,YAAY,IAAI,EAChB,OAAO,IAAI,EACK;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhE,YAAY;IACZ,MAAM,YAAY,aAAa;IAE/B,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB,QAAQ,cAAc;IAC5C,GAAG;QAAC;QAAQ;QAAc;KAAa,GAAG,iCAAiC;IAE3E,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,UAAU,OAAO,EAAE;QAEvB,WAAW;QACX,MAAM,WAAW,UAAU,KAAK;QAEhC,SAAS;QACT,MAAM,UAAU,MAAM,WAAW,QAAQ;QAEzC,kCAAkC;QAClC,IAAI,WAAW,iBAAiB,YAAY,UAAU;YACpD,SAAS;QACX;IACF;IAEA,aAAa;IACb,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF;gBACE,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QACC,SAAS;QACT,UAAU,UAAU,OAAO;QAC3B,WAAW,CAAC;QACV,EAAE,YAAY,MAAM,CAAC;;QAErB,EAAE,UAAU,KAAK,GACb,oCACA,mCACH;;;MAGH,CAAC;QACD,OAAO,UAAU,KAAK,GAAG,SAAS;;YAEjC,UAAU,OAAO,iBAChB,8OAAC;gBAAI,WAAW,GAAG,YAAY,IAAI,CAAC,oEAAoE,CAAC;;;;;uBACvG,UAAU,KAAK,iBACjB,8OAAC,8IAAA,CAAA,UAAO;gBAAC,WAAW,YAAY,IAAI;;;;;qCAEpC,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAW,YAAY,IAAI;;;;;;YAExC,2BACC,8OAAC;gBAAK,WAAW,GAAG,YAAY,IAAI,CAAC,YAAY,CAAC;0BAC/C,UAAU,KAAK;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaU<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaRegHeart } from 'react-icons/fa';\n\ninterface Comment {\n  _id: string;\n  content: string;\n  userId: {\n    _id: string;\n    name: string;\n    email: string;\n    image?: string;\n  };\n  createdAt: string;\n  likes: number;\n  replies?: Comment[];\n}\n\ninterface CommentSectionProps {\n  toolId: string;\n  onLoginRequired?: () => void;\n}\n\nexport default function CommentSection({ toolId, onLoginRequired }: CommentSectionProps) {\n  const { data: session } = useSession();\n  const [comments, setComments] = useState<Comment[]>([]);\n  const [newComment, setNewComment] = useState('');\n  const [replyTo, setReplyTo] = useState<string | null>(null);\n  const [replyContent, setReplyContent] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // 获取评论列表\n  const fetchComments = async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch(`/api/tools/${toolId}/comments`);\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setComments(data.data.comments);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch comments:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchComments();\n  }, [toolId]);\n\n  // 提交评论\n  const handleSubmitComment = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (!newComment.trim()) return;\n\n    setIsSubmitting(true);\n    try {\n      const response = await fetch(`/api/tools/${toolId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: newComment.trim()\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setNewComment('');\n          fetchComments(); // 重新获取评论列表\n        }\n      } else {\n        const errorData = await response.json();\n        console.error('Comment submission failed:', errorData.message);\n      }\n    } catch (error) {\n      console.error('Comment submission error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // 提交回复\n  const handleSubmitReply = async (parentId: string) => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (!replyContent.trim()) return;\n\n    setIsSubmitting(true);\n    try {\n      const response = await fetch(`/api/tools/${toolId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: replyContent.trim(),\n          parentId\n        }),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setReplyContent('');\n          setReplyTo(null);\n          fetchComments(); // 重新获取评论列表\n        }\n      } else {\n        const errorData = await response.json();\n        console.error('Reply submission failed:', errorData.message);\n      }\n    } catch (error) {\n      console.error('Reply submission error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n    \n    if (diffInHours < 1) {\n      return '刚刚';\n    } else if (diffInHours < 24) {\n      return `${diffInHours}小时前`;\n    } else if (diffInHours < 24 * 7) {\n      return `${Math.floor(diffInHours / 24)}天前`;\n    } else {\n      return date.toLocaleDateString('zh-CN');\n    }\n  };\n\n  const CommentItem = ({ comment, isReply = false }: { comment: Comment; isReply?: boolean }) => (\n    <div className={`${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''}`}>\n      <div className=\"flex gap-3\">\n        <div className=\"flex-shrink-0\">\n          {comment.userId.image ? (\n            <img\n              src={comment.userId.image}\n              alt={comment.userId.name}\n              className=\"w-8 h-8 rounded-full\"\n            />\n          ) : (\n            <div className=\"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center\">\n              <FaUser className=\"w-4 h-4 text-gray-600\" />\n            </div>\n          )}\n        </div>\n        \n        <div className=\"flex-1\">\n          <div className=\"flex items-center gap-2 mb-1\">\n            <span className=\"font-medium text-gray-900\">{comment.userId.name}</span>\n            <span className=\"text-sm text-gray-500\">{formatDate(comment.createdAt)}</span>\n          </div>\n          \n          <p className=\"text-gray-700 mb-2\">{comment.content}</p>\n          \n          <div className=\"flex items-center gap-4\">\n            {!isReply && (\n              <button\n                onClick={() => setReplyTo(replyTo === comment._id ? null : comment._id)}\n                className=\"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1\"\n              >\n                <FaReply className=\"w-3 h-3\" />\n                回复\n              </button>\n            )}\n          </div>\n          \n          {/* 回复输入框 */}\n          {replyTo === comment._id && (\n            <div className=\"mt-3\">\n              <textarea\n                value={replyContent}\n                onChange={(e) => setReplyContent(e.target.value)}\n                placeholder=\"写下你的回复...\"\n                className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                rows={3}\n                maxLength={1000}\n              />\n              <div className=\"flex justify-end gap-2 mt-2\">\n                <button\n                  onClick={() => {\n                    setReplyTo(null);\n                    setReplyContent('');\n                  }}\n                  className=\"px-4 py-2 text-gray-600 hover:text-gray-800\"\n                >\n                  取消\n                </button>\n                <button\n                  onClick={() => handleSubmitReply(comment._id)}\n                  disabled={isSubmitting || !replyContent.trim()}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isSubmitting ? '发送中...' : '发送'}\n                </button>\n              </div>\n            </div>\n          )}\n          \n          {/* 回复列表 */}\n          {comment.replies && comment.replies.length > 0 && (\n            <div className=\"mt-4 space-y-4\">\n              {comment.replies.map((reply) => (\n                <CommentItem key={reply._id} comment={reply} isReply={true} />\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900\">\n        评论 ({comments.length})\n      </h3>\n      \n      {/* 评论输入框 */}\n      <div className=\"space-y-3\">\n        <textarea\n          value={newComment}\n          onChange={(e) => setNewComment(e.target.value)}\n          placeholder={session ? \"写下你的评论...\" : \"请先登录后评论\"}\n          className=\"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          rows={4}\n          maxLength={1000}\n          disabled={!session}\n        />\n        <div className=\"flex justify-between items-center\">\n          <span className=\"text-sm text-gray-500\">\n            {newComment.length}/1000\n          </span>\n          <button\n            onClick={handleSubmitComment}\n            disabled={isSubmitting || !newComment.trim() || !session}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSubmitting ? '发送中...' : '发表评论'}\n          </button>\n        </div>\n      </div>\n      \n      {/* 评论列表 */}\n      {isLoading ? (\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"text-gray-500 mt-2\">加载评论中...</p>\n        </div>\n      ) : comments.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500\">暂无评论，来发表第一条评论吧！</p>\n        </div>\n      ) : (\n        <div className=\"space-y-6\">\n          {comments.map((comment) => (\n            <CommentItem key={comment._id} comment={comment} />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAyBe,SAAS,eAAe,EAAE,MAAM,EAAE,eAAe,EAAuB;IACrF,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,SAAS;IACT,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC;YAC5D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI,CAAC,QAAQ;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,OAAO;IACP,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,WAAW,IAAI;gBAC1B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc;oBACd,iBAAiB,WAAW;gBAC9B;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,8BAA8B,UAAU,OAAO;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,OAAO;IACP,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,CAAC,aAAa,IAAI,IAAI;QAE1B,gBAAgB;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,SAAS,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,aAAa,IAAI;oBAC1B;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB;oBAChB,WAAW;oBACX,iBAAiB,WAAW;gBAC9B;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,4BAA4B,UAAU,OAAO;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,cAAc,GAAG;YACnB,OAAO;QACT,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,GAAG,YAAY,GAAG,CAAC;QAC5B,OAAO,IAAI,cAAc,KAAK,GAAG;YAC/B,OAAO,GAAG,KAAK,KAAK,CAAC,cAAc,IAAI,EAAE,CAAC;QAC5C,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,UAAU,KAAK,EAA2C,iBACxF,8OAAC;YAAI,WAAW,GAAG,UAAU,yCAAyC,IAAI;sBACxE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,MAAM,CAAC,KAAK,iBACnB,8OAAC;4BACC,KAAK,QAAQ,MAAM,CAAC,KAAK;4BACzB,KAAK,QAAQ,MAAM,CAAC,IAAI;4BACxB,WAAU;;;;;iDAGZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAKxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA6B,QAAQ,MAAM,CAAC,IAAI;;;;;;kDAChE,8OAAC;wCAAK,WAAU;kDAAyB,WAAW,QAAQ,SAAS;;;;;;;;;;;;0CAGvE,8OAAC;gCAAE,WAAU;0CAAsB,QAAQ,OAAO;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,CAAC,yBACA,8OAAC;oCACC,SAAS,IAAM,WAAW,YAAY,QAAQ,GAAG,GAAG,OAAO,QAAQ,GAAG;oCACtE,WAAU;;sDAEV,8OAAC,8IAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;4BAOpC,YAAY,QAAQ,GAAG,kBACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,aAAY;wCACZ,WAAU;wCACV,MAAM;wCACN,WAAW;;;;;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;oDACP,WAAW;oDACX,gBAAgB;gDAClB;gDACA,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,kBAAkB,QAAQ,GAAG;gDAC5C,UAAU,gBAAgB,CAAC,aAAa,IAAI;gDAC5C,WAAU;0DAET,eAAe,WAAW;;;;;;;;;;;;;;;;;;4BAOlC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,mBAC3C,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,sBACpB,8OAAC;wCAA4B,SAAS;wCAAO,SAAS;uCAApC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;IASzC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;oBAAsC;oBAC7C,SAAS,MAAM;oBAAC;;;;;;;0BAIvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,aAAa,UAAU,cAAc;wBACrC,WAAU;wBACV,MAAM;wBACN,WAAW;wBACX,UAAU,CAAC;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCACb,WAAW,MAAM;oCAAC;;;;;;;0CAErB,8OAAC;gCACC,SAAS;gCACT,UAAU,gBAAgB,CAAC,WAAW,IAAI,MAAM,CAAC;gCACjD,WAAU;0CAET,eAAe,WAAW;;;;;;;;;;;;;;;;;;YAMhC,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;uBAElC,SAAS,MAAM,KAAK,kBACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;qCAG/B,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wBAA8B,SAAS;uBAAtB,QAAQ,GAAG;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts"], "sourcesContent": ["/**\n * 动态环境配置工具\n * 根据运行时环境自动判断URL配置，而不是在配置文件中写死\n */\n\n/**\n * 获取当前运行环境的基础URL\n * 支持开发环境、生产环境和部署平台的自动检测\n */\nexport function getBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_APP_URL) {\n    return process.env.NEXT_PUBLIC_APP_URL;\n  }\n\n  // 2. 在服务器端运行时\n  if (typeof window === 'undefined') {\n    // Vercel部署环境\n    if (process.env.VERCEL_URL) {\n      return `https://${process.env.VERCEL_URL}`;\n    }\n    \n    // Netlify部署环境\n    if (process.env.NETLIFY && process.env.URL) {\n      return process.env.URL;\n    }\n    \n    // Railway部署环境\n    if (process.env.RAILWAY_STATIC_URL) {\n      return process.env.RAILWAY_STATIC_URL;\n    }\n    \n    // 其他云平台的通用环境变量\n    if (process.env.APP_URL) {\n      return process.env.APP_URL;\n    }\n    \n    // 开发环境默认值\n    const port = process.env.PORT || '3001';\n    return `http://localhost:${port}`;\n  }\n\n  // 3. 在客户端运行时\n  if (typeof window !== 'undefined') {\n    const { protocol, hostname, port } = window.location;\n    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;\n  }\n\n  // 4. 兜底默认值\n  return 'http://localhost:3001';\n}\n\n/**\n * 获取API基础URL\n */\nexport function getApiBaseUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXT_PUBLIC_API_BASE_URL) {\n    return process.env.NEXT_PUBLIC_API_BASE_URL;\n  }\n\n  // 2. 基于基础URL构建API URL\n  const baseUrl = getBaseUrl();\n  return `${baseUrl}/api`;\n}\n\n/**\n * 获取NextAuth URL\n * NextAuth需要这个URL来处理回调和重定向\n */\nexport function getNextAuthUrl(): string {\n  // 1. 优先使用明确设置的环境变量\n  if (process.env.NEXTAUTH_URL) {\n    return process.env.NEXTAUTH_URL;\n  }\n\n  // 2. 基于基础URL构建\n  return getBaseUrl();\n}\n\n/**\n * 获取当前运行环境\n */\nexport function getEnvironment(): 'development' | 'production' | 'test' {\n  return (process.env.NODE_ENV as any) || 'development';\n}\n\n/**\n * 检查是否在开发环境\n */\nexport function isDevelopment(): boolean {\n  return getEnvironment() === 'development';\n}\n\n/**\n * 检查是否在生产环境\n */\nexport function isProduction(): boolean {\n  return getEnvironment() === 'production';\n}\n\n/**\n * 获取当前端口号\n */\nexport function getCurrentPort(): string {\n  // 服务器端\n  if (typeof window === 'undefined') {\n    return process.env.PORT || '3001';\n  }\n  \n  // 客户端\n  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');\n}\n\n/**\n * 动态环境配置对象\n * 可以在应用中直接使用这些配置\n */\nexport const dynamicEnv = {\n  baseUrl: getBaseUrl(),\n  apiBaseUrl: getApiBaseUrl(),\n  nextAuthUrl: getNextAuthUrl(),\n  environment: getEnvironment(),\n  isDevelopment: isDevelopment(),\n  isProduction: isProduction(),\n  port: getCurrentPort(),\n};\n\n/**\n * 在开发环境中打印配置信息（用于调试）\n */\nif (isDevelopment() && typeof window === 'undefined') {\n  console.log('🔧 Dynamic Environment Configuration:');\n  console.log('  Base URL:', dynamicEnv.baseUrl);\n  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);\n  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);\n  console.log('  Environment:', dynamicEnv.environment);\n  console.log('  Port:', dynamicEnv.port);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;AACM,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE;QACnC,OAAO,QAAQ,GAAG,CAAC,mBAAmB;IACxC;IAEA,cAAc;IACd,wCAAmC;QACjC,aAAa;QACb,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE;YAC1B,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;QAC5C;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,GAAG,EAAE;YAC1C,OAAO,QAAQ,GAAG,CAAC,GAAG;QACxB;QAEA,cAAc;QACd,IAAI,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAClC,OAAO,QAAQ,GAAG,CAAC,kBAAkB;QACvC;QAEA,eAAe;QACf,IAAI,QAAQ,GAAG,CAAC,OAAO,EAAE;YACvB,OAAO,QAAQ,GAAG,CAAC,OAAO;QAC5B;QAEA,UAAU;QACV,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;QACjC,OAAO,CAAC,iBAAiB,EAAE,MAAM;IACnC;;AAUF;AAKO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,wBAAwB,EAAE;QACxC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;IAC7C;IAEA,sBAAsB;IACtB,MAAM,UAAU;IAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;AACzB;AAMO,SAAS;IACd,mBAAmB;IACnB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,QAAQ,GAAG,CAAC,YAAY;IACjC;IAEA,eAAe;IACf,OAAO;AACT;AAKO,SAAS;IACd,OAAO,mDAAiC;AAC1C;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAKO,SAAS;IACd,OAAO;IACP,wCAAmC;QACjC,OAAO,QAAQ,GAAG,CAAC,IAAI,IAAI;IAC7B;;AAIF;AAMO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,aAAa;IACb,aAAa;IACb,eAAe;IACf,cAAc;IACd,MAAM;AACR;AAEA;;CAEC,GACD,IAAI,mBAAmB,gBAAkB,aAAa;IACpD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,eAAe,WAAW,OAAO;IAC7C,QAAQ,GAAG,CAAC,mBAAmB,WAAW,UAAU;IACpD,QAAQ,GAAG,CAAC,mBAAmB,WAAW,WAAW;IACrD,QAAQ,GAAG,CAAC,kBAAkB,WAAW,WAAW;IACpD,QAAQ,GAAG,CAAC,WAAW,WAAW,IAAI;AACxC", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts"], "sourcesContent": ["// API客户端工具类\nimport { getApiBaseUrl } from './env';\n\nconst API_BASE_URL = getApiBaseUrl();\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n  details?: string[];\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  hasNextPage: boolean;\n  hasPrevPage: boolean;\n}\n\nexport interface Tool {\n  _id: string;\n  name: string;\n  tagline?: string;\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string;\n  submittedAt: string;\n  launchDate?: string; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string;\n  reviewedAt?: string;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean;\n  selectedLaunchDate?: string;\n  launchOption?: 'free' | 'paid';\n\n  // 付费相关\n  paymentRequired?: boolean;\n  paymentAmount?: number;\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded';\n  orderId?: string;\n  paymentMethod?: string;\n  paidAt?: string;\n\n  views: number;\n  likes: number;\n  isActive: boolean;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface ToolsResponse {\n  tools: Tool[];\n  pagination: PaginationInfo;\n  stats?: {\n    total: number;\n    pending: number;\n    approved: number;\n    rejected: number;\n  };\n}\n\nexport interface AdminStats {\n  overview: {\n    totalTools: number;\n    pendingTools: number;\n    approvedTools: number;\n    rejectedTools: number;\n    totalViews: number;\n    totalLikes: number;\n    recentSubmissions: number;\n    recentApprovals: number;\n    recentRejections: number;\n    avgReviewTime: number;\n  };\n  categoryStats: Array<{\n    _id: string;\n    count: number;\n    totalViews: number;\n    totalLikes: number;\n  }>;\n  topTools: Array<{\n    _id: string;\n    name: string;\n    category: string;\n    views: number;\n    likes: number;\n  }>;\n  recentActivity: Array<{\n    _id: string;\n    name: string;\n    submittedBy: string;\n    submittedAt: string;\n    status: string;\n    reviewedAt?: string;\n    reviewedBy?: string;\n  }>;\n  dailyStats: Array<{\n    date: string;\n    day: string;\n    submissions: number;\n    approvals: number;\n    rejections: number;\n  }>;\n  timeRange: string;\n}\n\nexport interface Category {\n  id: string;\n  name: string;\n  count: number;\n  totalViews: number;\n  totalLikes: number;\n}\n\nexport interface CategoriesResponse {\n  categories: Category[];\n  overview: {\n    totalTools: number;\n    totalViews: number;\n    totalLikes: number;\n  };\n}\n\nclass ApiClient {\n  private baseURL: string;\n\n  constructor(baseURL: string = API_BASE_URL) {\n    this.baseURL = baseURL;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseURL}${endpoint}`;\n      const config: RequestInit = {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      };\n\n      const response = await fetch(url, config);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || `HTTP error! status: ${response.status}`);\n      }\n\n      return data;\n    } catch (error) {\n      console.error('API request failed:', error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '请求失败',\n      };\n    }\n  }\n\n  // 工具相关API\n  async getTools(params?: {\n    page?: number;\n    limit?: number;\n    category?: string;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/tools${query ? `?${query}` : ''}`);\n  }\n\n  async getTool(id: string): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`);\n  }\n\n  async createTool(toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>('/tools', {\n      method: 'POST',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async updateTool(id: string, toolData: Partial<Tool>): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/tools/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(toolData),\n    });\n  }\n\n  async deleteTool(id: string): Promise<ApiResponse<void>> {\n    return this.request<void>(`/tools/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  async getLikedTools(params?: {\n    page?: number;\n    limit?: number;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/user/liked-tools${query ? `?${query}` : ''}`);\n  }\n\n  // 管理员API\n  async getAdminTools(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    search?: string;\n    sort?: string;\n    order?: string;\n  }): Promise<ApiResponse<ToolsResponse>> {\n    const searchParams = new URLSearchParams();\n    if (params) {\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined) {\n          searchParams.append(key, value.toString());\n        }\n      });\n    }\n    const query = searchParams.toString();\n    return this.request<ToolsResponse>(`/admin/tools${query ? `?${query}` : ''}`);\n  }\n\n  async approveTool(id: string, data: {\n    reviewedBy?: string;\n    reviewNotes?: string;\n    launchDate?: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/approve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async rejectTool(id: string, data: {\n    reviewedBy?: string;\n    rejectReason: string;\n  }): Promise<ApiResponse<Tool>> {\n    return this.request<Tool>(`/admin/tools/${id}/reject`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async getAdminStats(timeRange?: string): Promise<ApiResponse<AdminStats>> {\n    const query = timeRange ? `?timeRange=${timeRange}` : '';\n    return this.request<AdminStats>(`/admin/stats${query}`);\n  }\n\n  // 分类API\n  async getCategories(): Promise<ApiResponse<CategoriesResponse>> {\n    return this.request<CategoriesResponse>('/categories');\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient();\n\n// 导出API客户端类以便在需要时创建新实例\nexport { ApiClient };\n"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;;AAEA,MAAM,eAAe,CAAA,GAAA,iHAAA,CAAA,gBAAa,AAAD;AAqIjC,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;YACxC,MAAM,SAAsB;gBAC1B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;YAClC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACxE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACV,MAAM,SAAS,MAUd,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACxE;IAEA,MAAM,QAAQ,EAAU,EAA8B;QACpD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI;IAC1C;IAEA,MAAM,WAAW,QAAuB,EAA8B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAO,UAAU;YAClC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAAuB,EAA8B;QAChF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAA8B;QACvD,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,MAGnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnF;IAEA,SAAS;IACT,MAAM,cAAc,MAOnB,EAAuC;QACtC,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,WAAW;oBACvB,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;gBACzC;YACF;QACF;QACA,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9E;IAEA,MAAM,YAAY,EAAU,EAAE,IAI7B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,EAAE;YACtD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAG5B,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YACrD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,SAAkB,EAAoC;QACxE,MAAM,QAAQ,YAAY,CAAC,WAAW,EAAE,WAAW,GAAG;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,YAAY,EAAE,OAAO;IACxD;IAEA,QAAQ;IACR,MAAM,gBAA0D;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAqB;IAC1C;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport LikeButton from '@/components/tools/LikeButton';\nimport CommentSection from '@/components/tools/CommentSection';\nimport LoginModal from '@/components/auth/LoginModal';\nimport { apiClient, Tool } from '@/lib/api';\nimport { getToolPricingColor, getToolPricingText } from '@/constants/pricing';\nimport {\n  ExternalLink,\n  Heart,\n  Eye,\n  Tag,\n  DollarSign,\n  Share2\n} from 'lucide-react';\n\ninterface ToolDetailClientProps {\n  initialTool: Tool;\n  toolId: string;\n}\n\nexport default function ToolDetailClient({ initialTool, toolId }: ToolDetailClientProps) {\n  const [tool, setTool] = useState<Tool>(initialTool);\n  const [relatedTools, setRelatedTools] = useState<Tool[]>([]);\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n\n  useEffect(() => {\n    fetchRelatedTools(tool.category);\n  }, [tool.category]);\n\n  const fetchRelatedTools = async (category: string) => {\n    try {\n      const response = await apiClient.getTools({\n        category,\n        status: 'published',\n        limit: 3\n      });\n\n      if (response.success && response.data) {\n        // 排除当前工具\n        const filtered = response.data.tools.filter(t => t._id !== toolId);\n        setRelatedTools(filtered.slice(0, 3));\n      }\n    } catch (err) {\n      // 静默失败，相关工具不是必需的\n    }\n  };\n\n  return (\n    <>\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Main Content */}\n        <div className=\"lg:col-span-2\">\n          {/* Tool Header */}\n          <article className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n            <header className=\"flex items-start justify-between mb-6\">\n              <div className=\"flex items-center space-x-4\">\n                {tool.logo ? (\n                  <img\n                    src={tool.logo}\n                    alt={`${tool.name} logo`}\n                    className=\"w-16 h-16 rounded-lg object-cover\"\n                  />\n                ) : (\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-2xl\">\n                      {tool.name.charAt(0).toUpperCase()}\n                    </span>\n                  </div>\n                )}\n                <div>\n                  <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                    {tool.name}\n                  </h1>\n                  {tool.tagline && (\n                    <p className=\"text-lg text-gray-600 mb-3\">\n                      {tool.tagline}\n                    </p>\n                  )}\n                  <div className=\"flex items-center space-x-4\">\n                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getToolPricingColor(tool.pricing)}`}>\n                      <DollarSign className=\"mr-1 h-4 w-4\" />\n                      {getToolPricingText(tool.pricing)}\n                    </span>\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Eye className=\"h-4 w-4\" />\n                        <span>{tool.views || 0} 浏览</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <Heart className=\"h-4 w-4\" />\n                        <span>{tool.likes || 0} 喜欢</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-2\">\n                <LikeButton\n                  toolId={tool._id}\n                  initialLikes={tool.likes}\n                  onLoginRequired={() => setIsLoginModalOpen(true)}\n                />\n                <button className=\"p-2 text-gray-400 hover:text-blue-500 transition-colors\">\n                  <Share2 className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </header>\n\n            {/* Description */}\n            <div className=\"mb-6\">\n              <p className=\"text-gray-600 text-lg leading-relaxed\">\n                {tool.description}\n              </p>\n            </div>\n\n            {/* Tags */}\n            {tool.tags && tool.tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-6\">\n                {tool.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer\"\n                  >\n                    <Tag className=\"mr-1 h-3 w-3\" />\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            )}\n\n            {/* CTA Button */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <a\n                href={tool.website}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\"\n              >\n                <ExternalLink className=\"mr-2 h-5 w-5\" />\n                访问 {tool.name}\n              </a>\n            </div>\n          </article>\n        </div>\n\n        {/* Sidebar */}\n        <aside className=\"lg:col-span-1\">\n          {/* Tool Info */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">工具信息</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-gray-600\">分类</span>\n                <span className=\"text-gray-900 font-medium\">{tool.category}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-gray-600\">价格模式</span>\n                <span className={`px-2 py-1 rounded text-sm font-medium ${getToolPricingColor(tool.pricing)}`}>\n                  {getToolPricingText(tool.pricing)}\n                </span>\n              </div>\n              {tool.launchDate && (\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-600\">发布日期</span>\n                  <span className=\"text-gray-900 font-medium\">\n                    {new Date(tool.launchDate).toLocaleDateString('zh-CN')}\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Related Tools */}\n          {relatedTools.length > 0 && (\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">相关工具</h3>\n              <div className=\"space-y-4\">\n                {relatedTools.map((relatedTool) => (\n                  <div key={relatedTool._id}>\n                    <Link\n                      href={`/tools/${relatedTool._id}`}\n                      className=\"block p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        {relatedTool.logo ? (\n                          <img\n                            src={relatedTool.logo}\n                            alt={relatedTool.name}\n                            className=\"w-10 h-10 rounded object-cover\"\n                          />\n                        ) : (\n                          <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center\">\n                            <span className=\"text-white font-bold text-sm\">\n                              {relatedTool.name.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                        )}\n                        <div className=\"flex-1 min-w-0\">\n                          <h4 className=\"text-sm font-medium text-gray-900 truncate\">\n                            {relatedTool.name}\n                          </h4>\n                          <div className=\"flex items-center justify-between text-xs text-gray-500 mt-1\">\n                            <span className={`px-2 py-1 rounded ${getToolPricingColor(relatedTool.pricing)}`}>\n                              {getToolPricingText(relatedTool.pricing)}\n                            </span>\n                            <div className=\"flex items-center space-x-2\">\n                              <span>{relatedTool.views || 0} 浏览</span>\n                              <span>{relatedTool.likes || 0} 喜欢</span>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </Link>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </aside>\n      </div>\n\n      {/* Comments Section */}\n      <div className=\"mt-12\">\n        <CommentSection\n          toolId={tool._id}\n          onLoginRequired={() => setIsLoginModalOpen(true)}\n        />\n      </div>\n\n      {/* Login Modal */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAuBe,SAAS,iBAAiB,EAAE,WAAW,EAAE,MAAM,EAAyB;IACrF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB,KAAK,QAAQ;IACjC,GAAG;QAAC,KAAK,QAAQ;KAAC;IAElB,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;gBACxC;gBACA,QAAQ;gBACR,OAAO;YACT;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,SAAS;gBACT,MAAM,WAAW,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;gBAC3D,gBAAgB,SAAS,KAAK,CAAC,GAAG;YACpC;QACF,EAAE,OAAO,KAAK;QACZ,iBAAiB;QACnB;IACF;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,iBACR,8OAAC;oDACC,KAAK,KAAK,IAAI;oDACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;oDACxB,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8DAItC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,IAAI;;;;;;wDAEX,KAAK,OAAO,kBACX,8OAAC;4DAAE,WAAU;sEACV,KAAK,OAAO;;;;;;sEAGjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAW,CAAC,oEAAoE,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,OAAO,GAAG;;sFACzH,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEACrB,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,OAAO;;;;;;;8EAElC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,gMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;8FACf,8OAAC;;wFAAM,KAAK,KAAK,IAAI;wFAAE;;;;;;;;;;;;;sFAEzB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,8OAAC;;wFAAM,KAAK,KAAK,IAAI;wFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,yIAAA,CAAA,UAAU;oDACT,QAAQ,KAAK,GAAG;oDAChB,cAAc,KAAK,KAAK;oDACxB,iBAAiB,IAAM,oBAAoB;;;;;;8DAE7C,8OAAC;oDAAO,WAAU;8DAChB,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;gCAKpB,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd;;2CAJI;;;;;;;;;;8CAWb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAM,KAAK,OAAO;wCAClB,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;4CACrC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAOrB,8OAAC;wBAAM,WAAU;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAA6B,KAAK,QAAQ;;;;;;;;;;;;0DAE5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAW,CAAC,sCAAsC,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,OAAO,GAAG;kEAC1F,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,OAAO;;;;;;;;;;;;4CAGnC,KAAK,UAAU,kBACd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;4BAQvD,aAAa,MAAM,GAAG,mBACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,OAAO,EAAE,YAAY,GAAG,EAAE;oDACjC,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,YAAY,IAAI,iBACf,8OAAC;gEACC,KAAK,YAAY,IAAI;gEACrB,KAAK,YAAY,IAAI;gEACrB,WAAU;;;;;qFAGZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EACb,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;0EAI7C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,YAAY,IAAI;;;;;;kFAEnB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAW,CAAC,kBAAkB,EAAE,CAAA,GAAA,2HAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,OAAO,GAAG;0FAC7E,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,OAAO;;;;;;0FAEzC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;;4FAAM,YAAY,KAAK,IAAI;4FAAE;;;;;;;kGAC9B,8OAAC;;4FAAM,YAAY,KAAK,IAAI;4FAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CA7BhC,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4CrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6IAAA,CAAA,UAAc;oBACb,QAAQ,KAAK,GAAG;oBAChB,iBAAiB,IAAM,oBAAoB;;;;;;;;;;;0BAK/C,8OAAC,wIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;AAI3C", "debugId": null}}]}