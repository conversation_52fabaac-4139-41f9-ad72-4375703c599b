module.exports = {

"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// This file only exists to satisfy Next.js requirement for a root layout
// The actual layout is in [locale]/layout.tsx
__turbopack_context__.s({
    "default": (()=>RootLayout)
});
function RootLayout({ children }) {
    return children;
}
}}),

};

//# sourceMappingURL=src_app_layout_tsx_55ed5bda._.js.map