{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckCircle, X } from 'lucide-react';\n\ninterface SuccessMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function SuccessMessage({ message, onClose, className = '' }: SuccessMessageProps) {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-green-400 hover:text-green-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAuB;IAC9F,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;gBAExC,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts"], "sourcesContent": ["// 国际化分类配置文件\n// 支持多语言的AI工具分类配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 分类的基础配置（不包含翻译文本）\nexport const CATEGORY_BASE_CONFIGS = [\n  {\n    slug: 'text-generation',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'data-analysis',\n    icon: '📊',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'audio-processing',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'education',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'marketing',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'productivity',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'customer-service',\n    icon: '🎧',\n    color: '#F59E0B'\n  }\n];\n\n// 客户端钩子：获取国际化的分类配置\nexport function useCategoryConfigs(): CategoryConfig[] {\n  const t = useTranslations('categories');\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 客户端钩子：获取分类选项（用于下拉框等）\nexport function useCategoryOptions(): CategoryOption[] {\n  const configs = useCategoryConfigs();\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 客户端钩子：获取包含\"所有分类\"选项的分类选项\nexport function useCategoryOptionsWithAll(): CategoryOption[] {\n  const t = useTranslations('categories');\n  const options = useCategoryOptions();\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 客户端钩子：获取分类名称\nexport function useCategoryName(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 客户端钩子：获取分类描述\nexport function useCategoryDescription(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取国际化的分类配置\nexport async function getCategoryConfigs(locale?: string): Promise<CategoryConfig[]> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 服务器端函数：获取分类选项\nexport async function getCategoryOptions(locale?: string): Promise<CategoryOption[]> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 服务器端函数：获取包含\"所有分类\"选项的分类选项\nexport async function getCategoryOptionsWithAll(locale?: string): Promise<CategoryOption[]> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  const options = await getCategoryOptions(locale);\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 服务器端函数：获取分类名称\nexport async function getCategoryName(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 服务器端函数：获取分类描述\nexport async function getCategoryDescription(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取分类配置\nexport async function getCategoryConfig(slug: string, locale?: string): Promise<CategoryConfig | undefined> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.find(config => config.slug === slug);\n}\n\n// 验证分类是否存在的辅助函数\nexport function isValidCategory(slug: string): boolean {\n  return CATEGORY_BASE_CONFIGS.some(config => config.slug === slug);\n}\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map(config => config.slug);\n\n// 分类元数据映射（slug -> 基础配置）\nexport const CATEGORY_BASE_METADATA: Record<string, typeof CATEGORY_BASE_CONFIGS[0]> = \n  CATEGORY_BASE_CONFIGS.reduce((acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  }, {} as Record<string, typeof CATEGORY_BASE_CONFIGS[0]>);\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;;;;;;AAEjB;AACA;;;AAgBO,MAAM,wBAAwB;IACnC;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,UAAU;IAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU;IAEhB,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,SAAS,gBAAgB,IAAY;IAC1C,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,SAAS,uBAAuB,IAAY;IACjD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAElE,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,eAAe,0BAA0B,MAAe;IAC7D,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,MAAM,UAAU,MAAM,mBAAmB;IAEzC,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,eAAe,gBAAgB,IAAY,EAAE,MAAe;IACjE,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,eAAe,uBAAuB,IAAY,EAAE,MAAe;IACxE,MAAM,IAAI,MAAM,CAAA,GAAA,gMAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,kBAAkB,IAAY,EAAE,MAAe;IACnE,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAGO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC9D;AAGO,MAAM,iBAAiB,sBAAsB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;AAGtE,MAAM,yBACX,sBAAsB,MAAM,CAAC,CAAC,KAAK;IACjC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts"], "sourcesContent": ["// AI工具应用的预定义标签列表 - 精选最流行的50个标签\nexport const AVAILABLE_TAGS = [\n  // 核心AI功能\n  'AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型',\n\n  // 内容创作\n  '写作助手', '内容生成', '文案创作', '博客写作', '营销文案',\n\n  // 图像处理\n  '图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除',\n\n  // 视频处理\n  '视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕',\n\n  // 音频处理\n  '语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音',\n\n  // 代码开发\n  '代码生成', '代码补全', '代码审查', '开发助手', '低代码平台',\n\n  // 数据分析\n  '数据分析', '数据可视化', '商业智能', '机器学习', '深度学习',\n\n  // 办公效率\n  '办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具',\n\n  // 设计工具\n  'UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计',\n\n  // 营销工具\n  'SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析',\n\n  // 翻译工具\n  '机器翻译', '实时翻译', '文档翻译', '语音翻译'\n];\n\n// 标签的最大选择数量\nexport const MAX_TAGS_COUNT = 3;\n\n// 按分类组织的标签（用于更好的用户体验）\nexport const TAGS_BY_CATEGORY = {\n  '核心AI功能': ['AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型'],\n  '内容创作': ['写作助手', '内容生成', '文案创作', '博客写作', '营销文案'],\n  '图像处理': ['图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除'],\n  '视频处理': ['视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕'],\n  '音频处理': ['语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音'],\n  '代码开发': ['代码生成', '代码补全', '代码审查', '开发助手', '低代码平台'],\n  '数据分析': ['数据分析', '数据可视化', '商业智能', '机器学习', '深度学习'],\n  '办公效率': ['办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具'],\n  '设计工具': ['UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计'],\n  '营销工具': ['SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析'],\n  '翻译工具': ['机器翻译', '实时翻译', '文档翻译', '语音翻译']\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;AACxB,MAAM,iBAAiB;IAC5B,SAAS;IACT;IAAQ;IAAW;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAS;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAS;IAAQ;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAQ;IAAU;IAAQ;IAAQ;IAElC,OAAO;IACP;IAAS;IAAU;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;CACzB;AAGM,MAAM,iBAAiB;AAGvB,MAAM,mBAAmB;IAC9B,UAAU;QAAC;QAAQ;QAAW;QAAQ;QAAQ;KAAO;IACrD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAQ;IAClD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IACjD,QAAQ;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAS;QAAQ;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAU;QAAQ;QAAQ;KAAO;IAClD,QAAQ;QAAC;QAAS;QAAU;QAAQ;QAAQ;KAAO;IACnD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;KAAO;AAC1C", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport { Tag, Search, X } from 'lucide-react';\nimport { AVAILABLE_TAGS, MAX_TAGS_COUNT } from '@/constants/tags';\nimport { Locale } from '@/i18n/config';\n\ninterface TagSelectorProps {\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  maxTags?: number;\n}\n\nexport default function TagSelector({\n  selectedTags,\n  onTagsChange,\n  maxTags = MAX_TAGS_COUNT\n}: TagSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n\n  const pathname = usePathname();\n  const t = useTranslations('common');\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const toggleTag = (tag: string) => {\n    if (selectedTags.includes(tag)) {\n      onTagsChange(selectedTags.filter(t => t !== tag));\n    } else if (selectedTags.length < maxTags) {\n      onTagsChange([...selectedTags, tag]);\n    }\n  };\n\n  const removeTag = (tag: string) => {\n    onTagsChange(selectedTags.filter(t => t !== tag));\n  };\n\n  // 过滤标签：根据搜索词过滤，并排除已选择的标签\n  const filteredTags = AVAILABLE_TAGS.filter(tag =>\n    tag.toLowerCase().includes(searchTerm.toLowerCase()) &&\n    !selectedTags.includes(tag)\n  );\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 标题和计数器 */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{t('select_tags')}</h3>\n        <span className=\"text-sm text-gray-500\">\n          {t('selected_count', { count: selectedTags.length, max: maxTags })}\n        </span>\n      </div>\n\n      {/* 已选择的标签 */}\n      {selectedTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <h4 className=\"text-sm font-medium text-gray-700\">{t('selected_tags')}</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedTags.map((tag) => (\n              <span\n                key={tag}\n                className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n              >\n                {tag}\n                <button\n                  type=\"button\"\n                  onClick={() => removeTag(tag)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"h-3 w-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 标签选择器 */}\n      <div className=\"space-y-3\">\n        <div className=\"relative\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {t('select_tags_max', { max: maxTags })}\n          </label>\n\n          {/* 搜索框 */}\n          <div className=\"relative mb-3\">\n            <input\n              type=\"text\"\n              placeholder={t('search_tags')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onFocus={() => setIsOpen(true)}\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n          </div>\n\n          {/* 标签选择下拉框 */}\n          {(isOpen || searchTerm) && (\n            <div className=\"relative\">\n              <div className=\"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                {filteredTags.length > 0 ? (\n                  <div className=\"p-2\">\n                    <div className=\"grid grid-cols-1 gap-1\">\n                      {filteredTags.map((tag) => {\n                        const isDisabled = selectedTags.length >= maxTags;\n\n                        return (\n                          <button\n                            key={tag}\n                            type=\"button\"\n                            onClick={() => {\n                              toggleTag(tag);\n                              setSearchTerm('');\n                              setIsOpen(false);\n                            }}\n                            disabled={isDisabled}\n                            className={`\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ${isDisabled\n                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                                : 'hover:bg-blue-50 text-gray-700'\n                              }\n                            `}\n                          >\n                            <div className=\"flex items-center\">\n                              <Tag className=\"h-3 w-3 mr-2 text-gray-400\" />\n                              {tag}\n                            </div>\n                          </button>\n                        );\n                      })}\n                    </div>\n                    {filteredTags.length > 50 && (\n                      <p className=\"text-xs text-gray-500 mt-2 px-3\">\n                        {t('found_tags', { count: filteredTags.length })}\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"p-4 text-center text-gray-500 text-sm\">\n                    {searchTerm ? t('no_tags_found') : t('start_typing')}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭下拉框 */}\n      {(isOpen || searchTerm) && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => {\n            setIsOpen(false);\n            setSearchTerm('');\n          }}\n        />\n      )}\n\n      {/* 提示信息 */}\n      {selectedTags.length >= maxTags && (\n        <p className=\"text-sm text-amber-600\">\n          {t('max_tags_limit', { max: maxTags })}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAee,SAAS,YAAY,EAClC,YAAY,EACZ,YAAY,EACZ,UAAU,wHAAA,CAAA,iBAAc,EACP;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,YAAY,CAAC;QACjB,IAAI,aAAa,QAAQ,CAAC,MAAM;YAC9B,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO,IAAI,aAAa,MAAM,GAAG,SAAS;YACxC,aAAa;mBAAI;gBAAc;aAAI;QACrC;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9C;IAEA,yBAAyB;IACzB,MAAM,eAAe,wHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,MACzC,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjD,CAAC,aAAa,QAAQ,CAAC;IAGzB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,8OAAC;wBAAK,WAAU;kCACb,EAAE,kBAAkB;4BAAE,OAAO,aAAa,MAAM;4BAAE,KAAK;wBAAQ;;;;;;;;;;;;YAKnE,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,EAAE;;;;;;kCACrD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;gCAEC,WAAU;;oCAET;kDACD,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BATV;;;;;;;;;;;;;;;;0BAkBf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCACd,EAAE,mBAAmB;gCAAE,KAAK;4BAAQ;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAa,EAAE;oCACf,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,SAAS,IAAM,UAAU;oCACzB,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;wBAInB,CAAC,UAAU,UAAU,mBACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,aAAa,MAAM,GAAG,kBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC;gDACjB,MAAM,aAAa,aAAa,MAAM,IAAI;gDAE1C,qBACE,8OAAC;oDAEC,MAAK;oDACL,SAAS;wDACP,UAAU;wDACV,cAAc;wDACd,UAAU;oDACZ;oDACA,UAAU;oDACV,WAAW,CAAC;;8BAEV,EAAE,aACE,iDACA,iCACH;4BACH,CAAC;8DAED,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd;;;;;;;mDAlBE;;;;;4CAsBX;;;;;;wCAED,aAAa,MAAM,GAAG,oBACrB,8OAAC;4CAAE,WAAU;sDACV,EAAE,cAAc;gDAAE,OAAO,aAAa,MAAM;4CAAC;;;;;;;;;;;yDAKpD,8OAAC;oCAAI,WAAU;8CACZ,aAAa,EAAE,mBAAmB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlD,CAAC,UAAU,UAAU,mBACpB,8OAAC;gBACC,WAAU;gBACV,SAAS;oBACP,UAAU;oBACV,cAAc;gBAChB;;;;;;YAKH,aAAa,MAAM,IAAI,yBACtB,8OAAC;gBAAE,WAAU;0BACV,EAAE,kBAAkB;oBAAE,KAAK;gBAAQ;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, Fragment } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport SuccessMessage from '@/components/SuccessMessage';\nimport LoginModal from '@/components/auth/LoginModal';\nimport { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';\nimport { useCategoryOptions } from '@/constants/categories-i18n';\nimport {\n  Upload,\n  Link as LinkIcon,\n  Info\n} from 'lucide-react';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport TagSelector from '@/components/TagSelector';\n\ninterface SubmitFormProps {\n  // Props can be added here if needed\n}\n\nexport default function SubmitForm({}: SubmitFormProps) {\n  const t = useTranslations('submit');\n  const { data: session } = useSession();\n  const router = useRouter();\n\n  // 使用国际化的分类选项\n  const categoryOptions = useCategoryOptions();\n\n  const [formData, setFormData] = useState({\n    name: '',\n    tagline: '',\n    description: '',\n    websiteUrl: '',\n    logoFile: null as File | null,\n    category: '',\n    tags: [] as string[],\n    pricingModel: ''\n  });\n\n  const [logoPreview, setLogoPreview] = useState<string | null>(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      // Validate file type\n      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\n      if (!allowedTypes.includes(file.type)) {\n        alert('Please upload a valid image file (JPEG, PNG, GIF, or WebP)');\n        return;\n      }\n\n      // Validate file size (5MB)\n      const maxSize = 5 * 1024 * 1024;\n      if (file.size > maxSize) {\n        alert('File size must be less than 5MB');\n        return;\n      }\n\n      setFormData(prev => ({\n        ...prev,\n        logoFile: file\n      }));\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setLogoPreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleTagsChange = (selectedTags: string[]) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: selectedTags\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!session) {\n      setIsLoginModalOpen(true);\n      return;\n    }\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      const submitData = new FormData();\n      submitData.append('name', formData.name);\n      submitData.append('tagline', formData.tagline);\n      submitData.append('description', formData.description);\n      submitData.append('websiteUrl', formData.websiteUrl);\n      submitData.append('category', formData.category);\n      submitData.append('tags', JSON.stringify(formData.tags));\n      submitData.append('pricingModel', formData.pricingModel);\n      \n      if (formData.logoFile) {\n        submitData.append('logo', formData.logoFile);\n      }\n\n      const response = await fetch('/api/tools/submit', {\n        method: 'POST',\n        body: submitData,\n      });\n\n      if (response.ok) {\n        setSubmitStatus('success');\n        // Reset form\n        setFormData({\n          name: '',\n          tagline: '',\n          description: '',\n          websiteUrl: '',\n          logoFile: null,\n          category: '',\n          tags: [],\n          pricingModel: ''\n        });\n        setLogoPreview(null);\n        \n        // Redirect to profile after a delay\n        setTimeout(() => {\n          router.push('/profile/submitted');\n        }, 2000);\n      } else {\n        setSubmitStatus('error');\n      }\n    } catch (error) {\n      console.error('Submit error:', error);\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <Fragment>\n      {/* Status Messages */}\n      {submitStatus === 'success' && (\n        <SuccessMessage message={t('form.success_message')} />\n      )}\n      \n      {submitStatus === 'error' && (\n        <ErrorMessage message={t('form.error_message')} />\n      )}\n\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Basic Information Section */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-6\">{t('form.basic_info')}</h2>\n          \n          <div className=\"space-y-6\">\n            {/* Tool Name */}\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.tool_name')} *\n              </label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                name=\"name\"\n                value={formData.name}\n                onChange={handleInputChange}\n                placeholder={t('form.tool_name_placeholder')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              />\n            </div>\n\n            {/* Tagline */}\n            <div>\n              <label htmlFor=\"tagline\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.tagline')}\n              </label>\n              <input\n                type=\"text\"\n                id=\"tagline\"\n                name=\"tagline\"\n                value={formData.tagline}\n                onChange={handleInputChange}\n                placeholder={t('form.tagline_placeholder')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n\n            {/* Website URL */}\n            <div>\n              <label htmlFor=\"websiteUrl\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.website_url')} *\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <LinkIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"url\"\n                  id=\"websiteUrl\"\n                  name=\"websiteUrl\"\n                  value={formData.websiteUrl}\n                  onChange={handleInputChange}\n                  placeholder={t('form.website_url_placeholder')}\n                  className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Description */}\n            <div>\n              <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.description')} *\n              </label>\n              <textarea\n                id=\"description\"\n                name=\"description\"\n                value={formData.description}\n                onChange={handleInputChange}\n                placeholder={t('form.description_placeholder')}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              />\n            </div>\n\n            {/* Logo Upload */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.logo_upload')}\n              </label>\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-1\">\n                  <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors\">\n                    <input\n                      type=\"file\"\n                      id=\"logo\"\n                      accept=\"image/jpeg,image/png,image/gif,image/webp\"\n                      onChange={handleLogoUpload}\n                      className=\"hidden\"\n                    />\n                    <label htmlFor=\"logo\" className=\"cursor-pointer\">\n                      <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <div className=\"mt-2\">\n                        <span className=\"text-sm text-blue-600 hover:text-blue-500\">\n                          Click to upload\n                        </span>\n                        <p className=\"text-xs text-gray-500 mt-1\">\n                          {t('form.logo_upload_hint')}\n                        </p>\n                      </div>\n                    </label>\n                  </div>\n                </div>\n                \n                {logoPreview && (\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-24 h-24 border border-gray-300 rounded-lg overflow-hidden\">\n                      <img\n                        src={logoPreview}\n                        alt={t('form.logo_preview')}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Category and Pricing Section */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-6\">{t('form.category_and_pricing')}</h2>\n          \n          <div className=\"space-y-6\">\n            {/* Category */}\n            <div>\n              <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.category')} *\n              </label>\n              <select\n                id=\"category\"\n                name=\"category\"\n                value={formData.category}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              >\n                <option value=\"\">{t('form.category_placeholder')}</option>\n                {categoryOptions.map((category) => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Tags */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.tags')}\n              </label>\n              <TagSelector\n                selectedTags={formData.tags}\n                onTagsChange={handleTagsChange}\n                maxTags={MAX_TAGS_COUNT}\n                placeholder={t('form.tags_placeholder')}\n              />\n            </div>\n\n            {/* Pricing Model */}\n            <div>\n              <label htmlFor=\"pricingModel\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('form.pricing_model')} *\n              </label>\n              <select\n                id=\"pricingModel\"\n                name=\"pricingModel\"\n                value={formData.pricingModel}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              >\n                <option value=\"\">{t('form.pricing_placeholder')}</option>\n                {TOOL_PRICING_FORM_OPTIONS.map((option) => (\n                  <option key={option.value} value={option.value}>\n                    {t(`form.${option.value}`)}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* User Info Display */}\n        {session && (\n          <div className=\"mb-8 bg-green-50 border border-green-200 rounded-lg p-4\">\n            <h3 className=\"text-sm font-medium text-green-800 mb-2\">{t('form.submitter_info')}</h3>\n            <p className=\"text-sm text-green-700\">\n              {t('form.submitter')}: {session.user?.name || session.user?.email}\n            </p>\n            <p className=\"text-sm text-green-700\">\n              {t('form.email')}: {session.user?.email}\n            </p>\n          </div>\n        )}\n\n        {/* Guidelines */}\n        <div className=\"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6\">\n          <div className=\"flex items-start\">\n            <Info className=\"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n            <div>\n              <h3 className=\"text-sm font-medium text-blue-900 mb-2\">{t('form.guidelines_title')}</h3>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• {t('form.guideline_1')}</li>\n                <li>• {t('form.guideline_2')}</li>\n                <li>• {t('form.guideline_3')}</li>\n                <li>• {t('form.guideline_4')}</li>\n                <li>• {t('form.guideline_5')}</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end\">\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className={`px-8 py-3 rounded-lg font-medium transition-colors ${\n              isSubmitting\n                ? 'bg-gray-400 text-gray-700 cursor-not-allowed'\n                : 'bg-blue-600 text-white hover:bg-blue-700'\n            }`}\n          >\n            {isSubmitting ? (\n              <div className=\"flex items-center\">\n                <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                {t('form.submitting')}\n              </div>\n            ) : (\n              t('form.submit_button')\n            )}\n          </button>\n        </div>\n      </form>\n\n      {/* Login Modal */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </Fragment>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AACA;AAlBA;;;;;;;;;;;;;;;AAwBe,SAAS,WAAW,EAAmB;IACpD,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,aAAa;IACb,MAAM,kBAAkB,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD;IAEzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,aAAa;QACb,YAAY;QACZ,UAAU;QACV,UAAU;QACV,MAAM,EAAE;QACR,cAAc;IAChB;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,qBAAqB;YACrB,MAAM,eAAe;gBAAC;gBAAc;gBAAa;gBAAa;aAAa;YAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,MAAM;gBACN;YACF;YAEA,2BAA2B;YAC3B,MAAM,UAAU,IAAI,OAAO;YAC3B,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,MAAM;gBACN;YACF;YAEA,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,UAAU;gBACZ,CAAC;YAED,iBAAiB;YACjB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,eAAe,EAAE,MAAM,EAAE;YAC3B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,MAAM;YACR,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS;YACZ,oBAAoB;YACpB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,MAAM,aAAa,IAAI;YACvB,WAAW,MAAM,CAAC,QAAQ,SAAS,IAAI;YACvC,WAAW,MAAM,CAAC,WAAW,SAAS,OAAO;YAC7C,WAAW,MAAM,CAAC,eAAe,SAAS,WAAW;YACrD,WAAW,MAAM,CAAC,cAAc,SAAS,UAAU;YACnD,WAAW,MAAM,CAAC,YAAY,SAAS,QAAQ;YAC/C,WAAW,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YACtD,WAAW,MAAM,CAAC,gBAAgB,SAAS,YAAY;YAEvD,IAAI,SAAS,QAAQ,EAAE;gBACrB,WAAW,MAAM,CAAC,QAAQ,SAAS,QAAQ;YAC7C;YAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,gBAAgB;gBAChB,aAAa;gBACb,YAAY;oBACV,MAAM;oBACN,SAAS;oBACT,aAAa;oBACb,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,MAAM,EAAE;oBACR,cAAc;gBAChB;gBACA,eAAe;gBAEf,oCAAoC;gBACpC,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,qMAAA,CAAA,WAAQ;;YAEN,iBAAiB,2BAChB,8OAAC,oIAAA,CAAA,UAAc;gBAAC,SAAS,EAAE;;;;;;YAG5B,iBAAiB,yBAChB,8OAAC,kIAAA,CAAA,UAAY;gBAAC,SAAS,EAAE;;;;;;0BAG3B,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C,EAAE;;;;;;0CAE5D,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;;oDAC7B,EAAE;oDAAkB;;;;;;;0DAEvB,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,aAAa,EAAE;gDACf,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAChC,EAAE;;;;;;0DAEL,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU;gDACV,aAAa,EAAE;gDACf,WAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAa,WAAU;;oDACnC,EAAE;oDAAoB;;;;;;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,UAAU;wDAC1B,UAAU;wDACV,aAAa,EAAE;wDACf,WAAU;wDACV,QAAQ;;;;;;;;;;;;;;;;;;kDAMd,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;;oDACpC,EAAE;oDAAoB;;;;;;;0DAEzB,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,aAAa,EAAE;gDACf,MAAM;gDACN,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DACd,EAAE;;;;;;0DAEL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,IAAG;oEACH,QAAO;oEACP,UAAU;oEACV,WAAU;;;;;;8EAEZ,8OAAC;oEAAM,SAAQ;oEAAO,WAAU;;sFAC9B,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAA4C;;;;;;8FAG5D,8OAAC;oFAAE,WAAU;8FACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAOZ,6BACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,KAAK;gEACL,KAAK,EAAE;gEACP,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C,EAAE;;;;;;0CAE5D,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAW,WAAU;;oDACjC,EAAE;oDAAiB;;;;;;;0DAEtB,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,WAAU;gDACV,QAAQ;;kEAER,8OAAC;wDAAO,OAAM;kEAAI,EAAE;;;;;;oDACnB,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC;4DAA4B,OAAO,SAAS,KAAK;sEAC/C,SAAS,KAAK;2DADJ,SAAS,KAAK;;;;;;;;;;;;;;;;;kDAQjC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DACd,EAAE;;;;;;0DAEL,8OAAC,iIAAA,CAAA,UAAW;gDACV,cAAc,SAAS,IAAI;gDAC3B,cAAc;gDACd,SAAS,wHAAA,CAAA,iBAAc;gDACvB,aAAa,EAAE;;;;;;;;;;;;kDAKnB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAe,WAAU;;oDACrC,EAAE;oDAAsB;;;;;;;0DAE3B,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU;gDACV,WAAU;gDACV,QAAQ;;kEAER,8OAAC;wDAAO,OAAM;kEAAI,EAAE;;;;;;oDACnB,2HAAA,CAAA,4BAAyB,CAAC,GAAG,CAAC,CAAC,uBAC9B,8OAAC;4DAA0B,OAAO,OAAO,KAAK;sEAC3C,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;2DADd,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUlC,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C,EAAE;;;;;;0CAC3D,8OAAC;gCAAE,WAAU;;oCACV,EAAE;oCAAkB;oCAAG,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI,EAAE;;;;;;;0CAE9D,8OAAC;gCAAE,WAAU;;oCACV,EAAE;oCAAc;oCAAG,QAAQ,IAAI,EAAE;;;;;;;;;;;;;kCAMxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0C,EAAE;;;;;;sDAC1D,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;;wDAAG;wDAAG,EAAE;;;;;;;8DACT,8OAAC;;wDAAG;wDAAG,EAAE;;;;;;;8DACT,8OAAC;;wDAAG;wDAAG,EAAE;;;;;;;8DACT,8OAAC;;wDAAG;wDAAG,EAAE;;;;;;;8DACT,8OAAC;;wDAAG;wDAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAW,CAAC,mDAAmD,EAC7D,eACI,iDACA,4CACJ;sCAED,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,UAAc;wCAAC,MAAK;wCAAK,WAAU;;;;;;oCACnC,EAAE;;;;;;uCAGL,EAAE;;;;;;;;;;;;;;;;;0BAOV,8OAAC,wIAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C", "debugId": null}}]}