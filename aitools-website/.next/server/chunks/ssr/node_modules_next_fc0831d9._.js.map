{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "REDIRECT_ERROR_CODE", "digest", "getStore", "isAction", "RedirectType", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at"], "mappings": ";;;;;;;;;;;;;;;;;;;IAegBA,gBAAgB,EAAA;eAAhBA;;IA6EAC,8BAA8B,EAAA;eAA9BA;;IARAC,wBAAwB,EAAA;eAAxBA;;IARAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,iBAAiB,EAAA;eAAjBA;;IAvBAC,QAAQ,EAAA;eAARA;;;oCArCmB;+BAM5B;AAEP,MAAMC,qBACJ,OAAOC,WAAW,qBAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAAST,iBACdU,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCC,oBAAAA,kBAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMC,eAAAA,mBAAmB,GAA7B,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CF,MAAMG,MAAM,GAAMD,eAAAA,mBAAmB,GAAC,MAAGN,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOG;AACT;AAcO,SAASV,SACd,2BAA2B,GAC3BK,GAAW,EACXC,IAAmB;QAEVL;IAATK,QAAAA,OAAAA,OAAAA,OAASL,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,IAC7CC,eAAAA,YAAY,CAACC,IAAI,GACjBD,eAAAA,YAAY,CAACE,OAAO;IAExB,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACC,iBAAiB;AACxE;AAaO,SAASV,kBACd,2BAA2B,GAC3BM,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBU,eAAAA,YAAY,CAACE,OAAO;IAEzC,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACW,iBAAiB;AACxE;AAUO,SAASrB,wBAAwBY,KAAc;IACpD,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAAS1B,yBAAyBa,KAAoB;IAC3D,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASzB,+BAA+Bc,KAAoB;IACjE,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOa,OAAOd,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["notFound", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "error", "Error", "digest"], "mappings": ";;;;+BAsBgBA,YAAAA;;;eAAAA;;;oCAnBT;AAEP;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,4CAA4C;IAC5C,MAAMG,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["forbidden", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAqBgBA,aAAAA;;;eAAAA;;;oCAlBT;AAEP,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["unauthorized", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAsBgBA,gBAAAA;;;eAAAA;;;oCAnBT;AAEP,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IAgCAC,kBAAkB,EAAA;eAAlBA;;;AAhCT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AAGA,MAAMK,yBAAyB,IAAIC;AAS5B,SAAST,mBACdU,MAAmB,EACnBH,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIT,6BAA6BG;IACzD,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIb,6BAA6BG;YAEnC,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAAWE;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC,OAAO;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAAWG;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicExpression = expression\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n        if (prerenderStore.validating === true) {\n          // We always log Request Access in dev at the point of calling the function\n          // So we mark the dynamic validation as not requiring it to be printed\n          dynamicTracking.syncDynamicLogged = true\n        }\n      }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoVeA,QAAQ,EAAA;eAARA;;IA3CAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IAuKAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IAhXAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IAtWAC,qBAAqB,EAAA;eAArBA;;IAgSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IA3TAC,yBAAyB,EAAA;eAAzBA;;IAuPAC,oBAAoB,EAAA;eAApBA;;IAgSAC,wBAAwB,EAAA;eAAxBA;;IAvcAC,gCAAgC,EAAA;eAAhCA;;IA6ZAC,yBAAyB,EAAA;eAAzBA;;IApYAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IAmDHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DA9hBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACAR,oCAAoChB,OAAOR,YAAYoB;AACzD;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASvE,4CACd6C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMe,kBAAkBf,eAAeQ,UAAU,CAACQ,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1B,MAAM5B,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBlB,qBAAqB,GAAGS;gBACxCS,gBAAgBhB,yBAAyB,GAAGuC;gBAC5C,IAAIZ,eAAekB,UAAU,KAAK,MAAM;oBACtC,2EAA2E;oBAC3E,sEAAsE;oBACtE7B,gBAAgB8B,iBAAiB,GAAG;gBACtC;YACF;QACF;QACAf,oCAAoChB,OAAOR,YAAYoB;IACzD;IACA,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASrB,SAAS,EAAE+D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C+B;IACA,IAAI/B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACqD,qBAAqBjC,OAAOR;AACtD;AAEA,SAASyC,qBAAqBjC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY+B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBhC,IAAY+B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBlB,MAAc;IAC7C,OACEA,OAAOmB,QAAQ,CACb,sEAEFnB,OAAOmB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIV,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMc,6BAA6B;AAEnC,SAASlB,gCAAgCe,OAAe;IACtD,MAAMhB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BhB,MAAcoB,MAAM,GAAGD;IACzB,OAAOnB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcoB,MAAM,KAAKD,8BAC1B,UAAUnB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASlE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgByD,MAAM,GAAG;AAClC;AAEO,SAAShF,qBACdiF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAc1D,eAAe,CAACwC,IAAI,IAAImB,cAAc3D,eAAe;IACnE,OAAO0D,cAAc1D,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJ4D,MAAM,CACL,CAACC,SACC,OAAOA,OAAOpC,KAAK,KAAK,YAAYoC,OAAOpC,KAAK,CAACgC,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEpD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLsC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAExD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASyB;IACP,IAAI,CAACtD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI6C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDe;IACA,MAAMZ,aAAa,IAAI6B;IACvB,qFAAqF;IACrF,IAAI;QACFtE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAOiC,GAAY;QACnB9B,WAAWC,KAAK,CAAC6B;IACnB;IACA,OAAO9B,WAAWQ,MAAM;AAC1B;AAOO,SAASlE,8BACdgC,aAAmC;IAEnC,MAAM0B,aAAa,IAAI6B;IAEvB,IAAIvD,cAAcyD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCzD,cAAcyD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1CjC,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1DiC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMlC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAWQ,MAAM;AAC1B;AAEO,SAAStE,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,MAAM+D,YAAYC,0BAAAA,gBAAgB,CAAC1C,QAAQ;IAE3C,IACEyC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAMjE,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDhB,OAAAA,OAAK,CAACiF,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACnE,cAAcoE,YAAY,EAAEtE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9B1B,qBACEsF,UAAUvD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDxB,iCAAiCqB,YAAY+D,WAAW7D;YAC1D;QACF;IACF;AACF;AAEA,MAAMqE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASlG,0BACd4B,KAAa,EACbuE,cAAsB,EACtBC,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI4B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBrF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIgF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI2E,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBtF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLsD,cAAcvD,yBAAyB,IACvCwD,cAAcxD,yBAAyB,EACvC;QACAuF,kBAAkBnF,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM6C,UAAU,CAAC,OAAO,EAAElC,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQwD,8BAA8BxC,SAASqC;QACrDC,kBAAkBlF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASwD,8BACPxC,OAAe,EACfqC,cAAsB;IAEtB,MAAMrD,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMW,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BhB,MAAMX,KAAK,GAAG,YAAY2B,UAAUqC;IACpC,OAAOrD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbwE,iBAAyC,EACzChC,aAAmC,EACnCC,aAAmC;IAEnC,IAAIkC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIrC,cAAcvD,yBAAyB,EAAE;QAC3C0F,YAAYnC,cAAcvD,yBAAyB;QACnD2F,iBAAiBpC,cAAczD,qBAAqB;QACpD8F,aAAarC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcxD,yBAAyB,EAAE;QAClD0F,YAAYlC,cAAcxD,yBAAyB;QACnD2F,iBAAiBnC,cAAc1D,qBAAqB;QACpD8F,aAAapC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL4C,YAAY;QACZC,iBAAiB5F;QACjB6F,aAAa;IACf;IAEA,IAAIL,kBAAkBnF,oBAAoB,IAAIsF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQ5D,KAAK,CAACyD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAI5E,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgBkF,kBAAkBlF,aAAa;IACrD,IAAIA,cAAciD,MAAM,EAAE;QACxB,IAAK,IAAIwC,IAAI,GAAGA,IAAIzF,cAAciD,MAAM,EAAEwC,IAAK;YAC7CD,QAAQ5D,KAAK,CAAC5B,aAAa,CAACyF,EAAE;QAChC;QAEA,MAAM,IAAIhF,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACyE,kBAAkBtF,mBAAmB,EAAE;QAC1C,IAAIsF,kBAAkBrF,kBAAkB,EAAE;YACxC,IAAIwF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIwE,kBAAkBpF,kBAAkB,EAAE;YAC/C,IAAIuF,WAAW;gBACbG,QAAQ5D,KAAK,CAACyD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI5E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE4E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI7E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "Error", "cause"], "mappings": ";;;;+BAOg<PERSON>,oBAAAA;;;eAAAA;;;uCAP+B;4BACpB;8BACS;mCACF;kCACA;oCACG;AAE9B,SAASA,iBAAiBC,KAAc;IAC7C,IACEC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAClBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,UACpBG,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACH,UACrBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ,UAClBK,CAAAA,GAAAA,YAAAA,UAAU,EAACL,UACXM,CAAAA,GAAAA,uBAAAA,8BAA8B,EAACN,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBO,SAAS,WAAWP,OAAO;QAC9CD,iBAAiBC,MAAMQ,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACY<PERSON>,oBAAAA;;;eAAAA;;;AAAN,MAAMA,mBACX,OAAOC,WAAW,qBAEZC,QAAQ,wHACRF,gBAAgB,GAClB,AACEE,QAAQ,8BACRF,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc,GAAA;;;;;;;;;;;;;;;;;;;;;IAkCLA,uBAAuB,EAAA;eAAvBA;;IALAC,YAAY,EAAA;eAAZA,eAAAA,YAAY;;IAEZC,SAAS,EAAA;eAATA,WAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAFEC,iBAAiB,EAAA;eAAjBA,UAAAA,iBAAiB;;IAA3BC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAIRC,YAAY,EAAA;eAAZA,cAAAA,YAAY;;IACZC,gBAAgB,EAAA;eAAhBA,iBAAAA,gBAAgB;;;0BALmB;+BACf;0BACJ;2BACC;8BACG;iCACI;AAhCjC,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMV,gCAAgCW;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/api/navigation.react-server.ts"], "sourcesContent": ["export * from '../client/components/navigation.react-server'\n"], "names": [], "mappings": ";AAAA,cAAc,+CAA8C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/web/spec-extension/cookies.ts"], "sourcesContent": ["export {\n  RequestCookies,\n  ResponseCookies,\n  stringifyCookie,\n} from 'next/dist/compiled/@edge-runtime/cookies'\n"], "names": ["RequestCookies", "ResponseCookies", "string<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;IACEA,cAAc,EAAA;eAAdA,SAAAA,cAAc;;IACdC,eAAe,EAAA;eAAfA,SAAAA,eAAe;;IACfC,eAAe,EAAA;eAAfA,SAAAA,eAAe;;;yBACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1525, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/web/spec-extension/adapters/request-cookies.ts"], "sourcesContent": ["import { RequestCookies } from '../cookies'\n\nimport { ResponseCookies } from '../cookies'\nimport { ReflectAdapter } from './reflect'\nimport { workAsyncStorage } from '../../../app-render/work-async-storage.external'\nimport {\n  getExpectedRequestStore,\n  type RequestStore,\n} from '../../../app-render/work-unit-async-storage.external'\n\n/**\n * @internal\n */\nexport class ReadonlyRequestCookiesError extends Error {\n  constructor() {\n    super(\n      'Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\n// We use this to type some APIs but we don't construct instances directly\nexport type { ResponseCookies }\n\n// The `cookies()` API is a mix of request and response cookies. For `.get()` methods,\n// we want to return the request cookie if it exists. For mutative methods like `.set()`,\n// we want to return the response cookie.\nexport type ReadonlyRequestCookies = Omit<\n  RequestCookies,\n  'set' | 'clear' | 'delete'\n> &\n  Pick<ResponseCookies, 'set' | 'delete'>\n\nexport class RequestCookiesAdapter {\n  public static seal(cookies: RequestCookies): ReadonlyRequestCookies {\n    return new Proxy(cookies as any, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'clear':\n          case 'delete':\n          case 'set':\n            return ReadonlyRequestCookiesError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n}\n\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies')\n\nexport function getModifiedCookieValues(\n  cookies: ResponseCookies\n): ResponseCookie[] {\n  const modified: ResponseCookie[] | undefined = (cookies as unknown as any)[\n    SYMBOL_MODIFY_COOKIE_VALUES\n  ]\n  if (!modified || !Array.isArray(modified) || modified.length === 0) {\n    return []\n  }\n\n  return modified\n}\n\ntype SetCookieArgs =\n  | [key: string, value: string, cookie?: Partial<ResponseCookie>]\n  | [options: ResponseCookie]\n\nexport function appendMutableCookies(\n  headers: Headers,\n  mutableCookies: ResponseCookies\n): boolean {\n  const modifiedCookieValues = getModifiedCookieValues(mutableCookies)\n  if (modifiedCookieValues.length === 0) {\n    return false\n  }\n\n  // Return a new response that extends the response with\n  // the modified cookies as fallbacks. `res` cookies\n  // will still take precedence.\n  const resCookies = new ResponseCookies(headers)\n  const returnedCookies = resCookies.getAll()\n\n  // Set the modified cookies as fallbacks.\n  for (const cookie of modifiedCookieValues) {\n    resCookies.set(cookie)\n  }\n\n  // Set the original cookies as the final values.\n  for (const cookie of returnedCookies) {\n    resCookies.set(cookie)\n  }\n\n  return true\n}\n\ntype ResponseCookie = NonNullable<\n  ReturnType<InstanceType<typeof ResponseCookies>['get']>\n>\n\nexport class MutableRequestCookiesAdapter {\n  public static wrap(\n    cookies: RequestCookies,\n    onUpdateCookies?: (cookies: string[]) => void\n  ): ResponseCookies {\n    const responseCookies = new ResponseCookies(new Headers())\n    for (const cookie of cookies.getAll()) {\n      responseCookies.set(cookie)\n    }\n\n    let modifiedValues: ResponseCookie[] = []\n    const modifiedCookies = new Set<string>()\n    const updateResponseCookies = () => {\n      // TODO-APP: change method of getting workStore\n      const workStore = workAsyncStorage.getStore()\n      if (workStore) {\n        workStore.pathWasRevalidated = true\n      }\n\n      const allCookies = responseCookies.getAll()\n      modifiedValues = allCookies.filter((c) => modifiedCookies.has(c.name))\n      if (onUpdateCookies) {\n        const serializedCookies: string[] = []\n        for (const cookie of modifiedValues) {\n          const tempCookies = new ResponseCookies(new Headers())\n          tempCookies.set(cookie)\n          serializedCookies.push(tempCookies.toString())\n        }\n\n        onUpdateCookies(serializedCookies)\n      }\n    }\n\n    const wrappedCookies = new Proxy(responseCookies, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          // A special symbol to get the modified cookie values\n          case SYMBOL_MODIFY_COOKIE_VALUES:\n            return modifiedValues\n\n          // TODO: Throw error if trying to set a cookie after the response\n          // headers have been set.\n          case 'delete':\n            return function (...args: [string] | [ResponseCookie]) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.delete(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n          case 'set':\n            return function (...args: SetCookieArgs) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.set(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n\n    return wrappedCookies\n  }\n}\n\nexport function wrapWithMutableAccessCheck(\n  responseCookies: ResponseCookies\n): ResponseCookies {\n  const wrappedCookies = new Proxy(responseCookies, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'delete':\n          return function (...args: [string] | [ResponseCookie]) {\n            ensureCookiesAreStillMutable('cookies().delete')\n            target.delete(...args)\n            return wrappedCookies\n          }\n        case 'set':\n          return function (...args: SetCookieArgs) {\n            ensureCookiesAreStillMutable('cookies().set')\n            target.set(...args)\n            return wrappedCookies\n          }\n\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  })\n  return wrappedCookies\n}\n\nexport function areCookiesMutableInCurrentPhase(requestStore: RequestStore) {\n  return requestStore.phase === 'action'\n}\n\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */\nfunction ensureCookiesAreStillMutable(callingExpression: string) {\n  const requestStore = getExpectedRequestStore(callingExpression)\n  if (!areCookiesMutableInCurrentPhase(requestStore)) {\n    // TODO: maybe we can give a more precise error message based on callingExpression?\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\nexport function responseCookiesToRequestCookies(\n  responseCookies: ResponseCookies\n): RequestCookies {\n  const requestCookies = new RequestCookies(new Headers())\n  for (const cookie of responseCookies.getAll()) {\n    requestCookies.set(cookie)\n  }\n  return requestCookies\n}\n"], "names": ["MutableRequestCookiesAdapter", "ReadonlyRequestCookiesError", "RequestCookiesAdapter", "appendMutableCookies", "areCookiesMutableInCurrentPhase", "getModifiedCookieValues", "responseCookiesToRequestCookies", "wrapWithMutableAccessCheck", "Error", "constructor", "callable", "seal", "cookies", "Proxy", "get", "target", "prop", "receiver", "ReflectAdapter", "SYMBOL_MODIFY_COOKIE_VALUES", "Symbol", "for", "modified", "Array", "isArray", "length", "headers", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resCookies", "ResponseCookies", "returnedCookies", "getAll", "cookie", "set", "wrap", "onUpdateCookies", "responseCookies", "Headers", "modifiedV<PERSON>ues", "modifiedCookies", "Set", "updateResponseCookies", "workStore", "workAsyncStorage", "getStore", "pathWasRevalidated", "allCookies", "filter", "c", "has", "name", "serializedCookies", "tempCookies", "push", "toString", "wrappedCookies", "args", "add", "delete", "ensureCookiesAreStillMutable", "requestStore", "phase", "callingExpression", "getExpectedRequestStore", "requestCookies", "RequestCookies"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAyGaA,4BAA4B,EAAA;eAA5BA;;IA5FAC,2BAA2B,EAAA;eAA3BA;;IAwBAC,qBAAqB,EAAA;eAArBA;;IAoCGC,oBAAoB,EAAA;eAApBA;;IAwIAC,+BAA+B,EAAA;eAA/BA;;IAzJAC,uBAAuB,EAAA;eAAvBA;;IA4KAC,+BAA+B,EAAA;eAA/BA;;IA9CAC,0BAA0B,EAAA;eAA1BA;;;yBAtLe;yBAGA;0CACE;8CAI1B;AAKA,MAAMN,oCAAoCO;IAC/CC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIT;IACZ;AACF;AAcO,MAAMC;IACX,OAAcS,KAAKC,OAAuB,EAA0B;QAClE,OAAO,IAAIC,MAAMD,SAAgB;YAC/BE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOf,4BAA4BS,QAAQ;oBAC7C;wBACE,OAAOQ,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;AACF;AAEA,MAAME,8BAA8BC,OAAOC,GAAG,CAAC;AAExC,SAAShB,wBACdO,OAAwB;IAExB,MAAMU,WAA0CV,OAA0B,CACxEO,4BACD;IACD,IAAI,CAACG,YAAY,CAACC,MAAMC,OAAO,CAACF,aAAaA,SAASG,MAAM,KAAK,GAAG;QAClE,OAAO,EAAE;IACX;IAEA,OAAOH;AACT;AAMO,SAASnB,qBACduB,OAAgB,EAChBC,cAA+B;IAE/B,MAAMC,uBAAuBvB,wBAAwBsB;IACrD,IAAIC,qBAAqBH,MAAM,KAAK,GAAG;QACrC,OAAO;IACT;IAEA,uDAAuD;IACvD,mDAAmD;IACnD,8BAA8B;IAC9B,MAAMI,aAAa,IAAIC,SAAAA,eAAe,CAACJ;IACvC,MAAMK,kBAAkBF,WAAWG,MAAM;IAEzC,yCAAyC;IACzC,KAAK,MAAMC,UAAUL,qBAAsB;QACzCC,WAAWK,GAAG,CAACD;IACjB;IAEA,gDAAgD;IAChD,KAAK,MAAMA,UAAUF,gBAAiB;QACpCF,WAAWK,GAAG,CAACD;IACjB;IAEA,OAAO;AACT;AAMO,MAAMjC;IACX,OAAcmC,KACZvB,OAAuB,EACvBwB,eAA6C,EAC5B;QACjB,MAAMC,kBAAkB,IAAIP,SAAAA,eAAe,CAAC,IAAIQ;QAChD,KAAK,MAAML,UAAUrB,QAAQoB,MAAM,GAAI;YACrCK,gBAAgBH,GAAG,CAACD;QACtB;QAEA,IAAIM,iBAAmC,EAAE;QACzC,MAAMC,kBAAkB,IAAIC;QAC5B,MAAMC,wBAAwB;YAC5B,+CAA+C;YAC/C,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;YAC3C,IAAIF,WAAW;gBACbA,UAAUG,kBAAkB,GAAG;YACjC;YAEA,MAAMC,aAAaV,gBAAgBL,MAAM;YACzCO,iBAAiBQ,WAAWC,MAAM,CAAC,CAACC,IAAMT,gBAAgBU,GAAG,CAACD,EAAEE,IAAI;YACpE,IAAIf,iBAAiB;gBACnB,MAAMgB,oBAA8B,EAAE;gBACtC,KAAK,MAAMnB,UAAUM,eAAgB;oBACnC,MAAMc,cAAc,IAAIvB,SAAAA,eAAe,CAAC,IAAIQ;oBAC5Ce,YAAYnB,GAAG,CAACD;oBAChBmB,kBAAkBE,IAAI,CAACD,YAAYE,QAAQ;gBAC7C;gBAEAnB,gBAAgBgB;YAClB;QACF;QAEA,MAAMI,iBAAiB,IAAI3C,MAAMwB,iBAAiB;YAChDvB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,qDAAqD;oBACrD,KAAKG;wBACH,OAAOoB;oBAET,iEAAiE;oBACjE,yBAAyB;oBACzB,KAAK;wBACH,OAAO,SAAU,GAAGkB,IAAiC;4BACnDjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACN,IAAI;4BAEtD,IAAI;gCACFpC,OAAO4C,MAAM,IAAIF;gCACjB,OAAOD;4BACT,SAAU;gCACRd;4BACF;wBACF;oBACF,KAAK;wBACH,OAAO,SAAU,GAAGe,IAAmB;4BACrCjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACN,IAAI;4BAEtD,IAAI;gCACFpC,OAAOmB,GAAG,IAAIuB;gCACd,OAAOD;4BACT,SAAU;gCACRd;4BACF;wBACF;oBAEF;wBACE,OAAOxB,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;QAEA,OAAOuC;IACT;AACF;AAEO,SAASjD,2BACd8B,eAAgC;IAEhC,MAAMmB,iBAAiB,IAAI3C,MAAMwB,iBAAiB;QAChDvB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAK;oBACH,OAAO,SAAU,GAAGyC,IAAiC;wBACnDG,6BAA6B;wBAC7B7C,OAAO4C,MAAM,IAAIF;wBACjB,OAAOD;oBACT;gBACF,KAAK;oBACH,OAAO,SAAU,GAAGC,IAAmB;wBACrCG,6BAA6B;wBAC7B7C,OAAOmB,GAAG,IAAIuB;wBACd,OAAOD;oBACT;gBAEF;oBACE,OAAOtC,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;YAC5C;QACF;IACF;IACA,OAAOuC;AACT;AAEO,SAASpD,gCAAgCyD,YAA0B;IACxE,OAAOA,aAAaC,KAAK,KAAK;AAChC;AAEA;;;;;;GAMG,GACH,SAASF,6BAA6BG,iBAAyB;IAC7D,MAAMF,eAAeG,CAAAA,GAAAA,8BAAAA,uBAAuB,EAACD;IAC7C,IAAI,CAAC3D,gCAAgCyD,eAAe;QAClD,mFAAmF;QACnF,MAAM,IAAI5D;IACZ;AACF;AAEO,SAASK,gCACd+B,eAAgC;IAEhC,MAAM4B,iBAAiB,IAAIC,SAAAA,cAAc,CAAC,IAAI5B;IAC9C,KAAK,MAAML,UAAUI,gBAAgBL,MAAM,GAAI;QAC7CiC,eAAe/B,GAAG,CAACD;IACrB;IACA,OAAOgC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "React", "fn", "logErrorOrWarn", "process", "env", "__NEXT_DYNAMIC_IO", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": ";;;;+BAyCgBA,+CAAAA;;;eAAAA;;;+DAzCO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOC,OAAMD,KAAK,KAAK,aACnBC,OAAMD,KAAK,GACX,CAACE,KAA+BA;AAEtC,qEAAqE;AACrE,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,iBAAiBC,QAAQC,GAAG,CAACC,iBAAiB,GAChDC,QAAQC,KAAK,gCACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBV,MAC7B,AACA,CAACW,yEADyE;IAExE,IAAI;QACFR,eAAeL,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASF,4CACde,UAAoC;IAEpC,OAAO,SAASC,gBAAgB,GAAGC,IAAU;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIV,QAAQC,GAAG,CAACW,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DnB,eAAeY;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAMJ,MAAMM,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBL,uBAAuBC;YACzB;QACF,OAAO;;QAEP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  workStore.invalidUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["isRequestAPICallableInsideAfter", "throwForSearchParamsAccessInUseCache", "throwWithStaticGenerationBailoutError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "StaticGenBailoutError", "workStore", "error", "Error", "invalidUsageError", "afterTaskStore", "afterTaskAsyncStorage", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;;;;;;;;;;IAkCgBA,+BAA+B,EAAA;eAA/BA;;IAZAC,oCAAoC,EAAA;eAApCA;;IAlBAC,qCAAqC,EAAA;eAArCA;;IASAC,qDAAqD,EAAA;eAArDA;;;yCAbsB;+CACA;AAG/B,SAASD,sCACdE,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,MAAM,iDAAiD,EAAEC,WAAW,0HAA0H,CAAC,GADpM,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASF,sDACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,MAAM,4EAA4E,EAAEC,WAAW,0HAA0H,CAAC,GAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASJ,qCACdM,SAAoB;IAEpB,MAAMC,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,CAAC,MAAM,EAAEF,UAAUH,KAAK,CAAC,oVAAoV,CAAC,GADlW,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAG,UAAUG,iBAAiB,KAAKF;IAEhC,MAAMA;AACR;AAEO,SAASR;IACd,MAAMW,iBAAiBC,+BAAAA,qBAAqB,CAACC,QAAQ;IACrD,OAAOF,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBG,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1935, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/request/cookies.ts"], "sourcesContent": ["import {\n  type ReadonlyRequestCookies,\n  type ResponseCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * In this version of Next.js `cookies()` returns a Promise however you can still reference the properties of the underlying cookies object\n * synchronously to facilitate migration. The `UnsafeUnwrappedCookies` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `cookies()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedCookies` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `cookies()` value can be awaited or you should call `cookies()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedCookies` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `cookies()` will only return a Promise and you will not be able to access the underlying cookies object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedCookies` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedCookies = ReadonlyRequestCookies\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedExoticCookies(underlyingCookies)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We don't track dynamic access here because access will be tracked when you access\n        // one of the properties of the cookies object.\n        return makeDynamicallyTrackedExoticCookies(\n          workStore.route,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We are prerendering with PPR. We need track dynamic access here eagerly\n        // to keep continuity with how cookies has worked in PPR without dynamicIO.\n        postponeWithTracking(\n          workStore.route,\n          callingExpression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We track dynamic access here so we don't need to wrap the cookies in\n        // individual property access tracking.\n        throwToInterruptStaticGeneration(\n          callingExpression,\n          workStore,\n          workUnitStore\n        )\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using cookies inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  // cookies is being called in a dynamic context\n\n  const requestStore = getExpectedRequestStore(callingExpression)\n\n  let underlyingCookies: ReadonlyRequestCookies\n\n  if (areCookiesMutableInCurrentPhase(requestStore)) {\n    // We can't conditionally return different types here based on the context.\n    // To avoid confusion, we always return the readonly type here.\n    underlyingCookies =\n      requestStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n  } else {\n    underlyingCookies = requestStore.cookies\n  }\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    return makeUntrackedExoticCookiesWithDevWarnings(\n      underlyingCookies,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticCookies(underlyingCookies)\n  }\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeDynamicallyTrackedExoticCookies(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`cookies()[Symbol.iterator]()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    size: {\n      get() {\n        const expression = '`cookies().size`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    has: {\n      value: function has() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: underlyingCookies[Symbol.iterator]\n        ? underlyingCookies[Symbol.iterator].bind(underlyingCookies)\n        : // TODO this is a polyfill for when the underlying type is ResponseCookies\n          // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n          // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n          // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n          // has extra properties not available on RequestCookie instances.\n          polyfilledResponseCookiesIterator.bind(underlyingCookies),\n    },\n    size: {\n      get(): number {\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: underlyingCookies.get.bind(underlyingCookies),\n    },\n    getAll: {\n      value: underlyingCookies.getAll.bind(underlyingCookies),\n    },\n    has: {\n      value: underlyingCookies.has.bind(underlyingCookies),\n    },\n    set: {\n      value: underlyingCookies.set.bind(underlyingCookies),\n    },\n    delete: {\n      value: underlyingCookies.delete.bind(underlyingCookies),\n    },\n    clear: {\n      value:\n        // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n        typeof underlyingCookies.clear === 'function'\n          ? // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.bind(underlyingCookies)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise),\n    },\n    toString: {\n      value: underlyingCookies.toString.bind(underlyingCookies),\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = new Promise<ReadonlyRequestCookies>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingCookies))\n  )\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...cookies()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingCookies[Symbol.iterator]\n          ? underlyingCookies[Symbol.iterator].apply(\n              underlyingCookies,\n              arguments as any\n            )\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.call(underlyingCookies)\n      },\n      writable: false,\n    },\n    size: {\n      get(): number {\n        const expression = '`cookies().size`'\n        syncIODev(route, expression)\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.get.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.getAll.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    has: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.has.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.set.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.delete.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        syncIODev(route, expression)\n        // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n        return typeof underlyingCookies.clear === 'function'\n          ? // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.apply(underlyingCookies, arguments)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.call(underlyingCookies, promise)\n      },\n      writable: false,\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()` or implicit casting'\n        syncIODev(route, expression)\n        return underlyingCookies.toString.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'object' &&\n    arg !== null &&\n    typeof (arg as any).name === 'string'\n    ? `'${(arg as any).name}'`\n    : typeof arg === 'string'\n      ? `'${arg}'`\n      : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction polyfilledResponseCookiesIterator(\n  this: ResponseCookies\n): ReturnType<ReadonlyRequestCookies[typeof Symbol.iterator]> {\n  return this.getAll()\n    .map((c) => [c.name, c] as [string, any])\n    .values()\n}\n\nfunction polyfilledResponseCookiesClear(\n  this: ResponseCookies,\n  returnable: Promise<ReadonlyRequestCookies>\n): typeof returnable {\n  for (const cookie of this.getAll()) {\n    this.delete(cookie.name)\n  }\n  return returnable\n}\n\ntype CookieExtensions = {\n  [K in keyof ReadonlyRequestCookies | 'clear']: unknown\n}\n"], "names": ["cookies", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedExoticCookies", "type", "dynamicShouldError", "StaticGenBailoutError", "makeDynamicallyTrackedExoticCookies", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "requestStore", "getExpectedRequestStore", "areCookiesMutableInCurrentPhase", "userspaceMutableCookies", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeUntrackedExoticCookiesWithDevWarnings", "RequestCookiesAdapter", "seal", "RequestCookies", "Headers", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "get", "promise", "makeHangingPromise", "renderSignal", "set", "Object", "defineProperties", "Symbol", "iterator", "value", "expression", "error", "createCookiesAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "size", "arguments", "length", "describeNameArg", "getAll", "has", "arg", "delete", "clear", "toString", "cachedCookies", "Promise", "resolve", "bind", "polyfilledResponseCookiesIterator", "polyfilledResponseCookiesClear", "scheduleImmediate", "syncIODev", "apply", "call", "writable", "name", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "prefix", "map", "c", "values", "returnable", "cookie"], "mappings": ";;;;+BAiDg<PERSON>,WAAAA;;;eAAAA;;;gCA5CT;yBACwB;0CACE;8CAI1B;kCAOA;yCAE+B;uCACH;0DACyB;2BAC1B;uBACc;AAyBzC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,CAAAA,GAAAA,OAAAA,+BAA+B,KAChC;YACA,MAAM,OAAA,cAGL,CAHK,IAAIC,MAER,AADA,CACC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,+BAD+B,0MAC0M,CAAC,GAF/P,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC;YAC1B,OAAOC,2BAA2BF;QACpC;QAEA,IAAIP,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,SAAS;gBAClC,MAAM,OAAA,cAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIL,cAAcU,IAAI,KAAK,kBAAkB;gBAClD,MAAM,OAAA,cAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIR,UAAUc,kBAAkB,EAAE;YAChC,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEf,UAAUQ,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,oFAAoF;gBACpF,+CAA+C;gBAC/C,OAAOG,oCACLhB,UAAUQ,KAAK,EACfL;YAEJ,OAAO,IAAIA,cAAcU,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,0EAA0E;gBAC1E,2EAA2E;gBAC3EI,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjB,UAAUQ,KAAK,EACfT,mBACAI,cAAce,eAAe;YAEjC,OAAO,IAAIf,cAAcU,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,uEAAuE;gBACvE,uCAAuC;gBACvCM,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BpB,mBACAC,WACAG;YAEJ;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFiB,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACpB,WAAWG;IAC7C;IAEA,+CAA+C;IAE/C,MAAMkB,eAAeC,CAAAA,GAAAA,8BAAAA,uBAAuB,EAACvB;IAE7C,IAAIW;IAEJ,IAAIa,CAAAA,GAAAA,gBAAAA,+BAA+B,EAACF,eAAe;QACjD,2EAA2E;QAC3E,+DAA+D;QAC/DX,oBACEW,aAAaG,uBAAuB;IACxC,OAAO;QACLd,oBAAoBW,aAAavB,OAAO;IAC1C;IAEA,IAAI2B,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiB,CAAA,CAAC3B,aAAAA,OAAAA,KAAAA,IAAAA,UAAW4B,iBAAiB,GAAE;QAC3E,OAAOC,0CACLnB,mBACAV,aAAAA,OAAAA,KAAAA,IAAAA,UAAWQ,KAAK;IAEpB,OAAO;QACL,OAAOI,2BAA2BF;IACpC;AACF;AAEA,SAASC;IACP,OAAOmB,gBAAAA,qBAAqB,CAACC,IAAI,CAAC,IAAIC,SAAAA,cAAc,CAAC,IAAIC,QAAQ,CAAC;AACpE;AAGA,MAAMC,gBAAgB,IAAIC;AAK1B,SAASnB,oCACPR,KAAa,EACb4B,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCJ,eAAeK,YAAY,EAC3B;IAEFP,cAAcQ,GAAG,CAACN,gBAAgBG;IAElCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAgB,MAAM;YACJd;gBACE,MAAMU,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAE,KAAK;YACHS,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAoB,QAAQ;YACNT,OAAO,SAASS;gBACd,IAAIR;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAqB,KAAK;YACHV,OAAO,SAASU;gBACd,IAAIT;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAM,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMU,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPV,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLV,aAAa;oBACf;gBACF;gBACA,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAuB,QAAQ;YACNZ,OAAO;gBACL,IAAIC;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLL,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAwB,OAAO;YACLb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAyB,UAAU;YACRd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;IACF;IAEA,OAAOG;AACT;AAEA,SAAS3B,2BACPF,iBAAyC;IAEzC,MAAMoD,gBAAgB5B,cAAcI,GAAG,CAAC5B;IACxC,IAAIoD,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMvB,UAAUwB,QAAQC,OAAO,CAACtD;IAChCwB,cAAcQ,GAAG,CAAChC,mBAAmB6B;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAOrC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,GACrCpC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,CAACmB,IAAI,CAACvD,qBAExC,AACA,qGADqG,YACY;YACjH,oHAAoH;YACpH,iEAAiE;YACjEwD,kCAAkCD,IAAI,CAACvD;QAC7C;QACA0C,MAAM;YACJd;gBACE,OAAO5B,kBAAkB0C,IAAI;YAC/B;QACF;QACAd,KAAK;YACHS,OAAOrC,kBAAkB4B,GAAG,CAAC2B,IAAI,CAACvD;QACpC;QACA8C,QAAQ;YACNT,OAAOrC,kBAAkB8C,MAAM,CAACS,IAAI,CAACvD;QACvC;QACA+C,KAAK;YACHV,OAAOrC,kBAAkB+C,GAAG,CAACQ,IAAI,CAACvD;QACpC;QACAgC,KAAK;YACHK,OAAOrC,kBAAkBgC,GAAG,CAACuB,IAAI,CAACvD;QACpC;QACAiD,QAAQ;YACNZ,OAAOrC,kBAAkBiD,MAAM,CAACM,IAAI,CAACvD;QACvC;QACAkD,OAAO;YACLb,OACE,AACA,OAAOrC,kBAAkBkD,KAAK,KAAK,aAE/BlD,kBAAkBkD,KAAK,CAACK,IAAI,CAACvD,YAHwD,SAKrF,AACA,qGADqG,YACY;YACjH,oHAAoH;YACpH,iEAAiE;YACjEyD,+BAA+BF,IAAI,CAACvD,mBAAmB6B;QAC/D;QACAsB,UAAU;YACRd,OAAOrC,kBAAkBmD,QAAQ,CAACI,IAAI,CAACvD;QACzC;IACF;IAEA,OAAO6B;AACT;AAEA,SAASV,0CACPnB,iBAAyC,EACzCF,KAAc;IAEd,MAAMsD,gBAAgB5B,cAAcI,GAAG,CAAC5B;IACxC,IAAIoD,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMvB,UAAU,IAAIwB,QAAgC,CAACC,UACnDI,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMJ,QAAQtD;IAElCwB,cAAcQ,GAAG,CAAChC,mBAAmB6B;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,GACrCpC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,CAACwB,KAAK,CACtC5D,mBACA2C,aAGF,AACA,qGADqG,YACY;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEa,kCAAkCK,IAAI,CAAC7D;YAC7C;YACA8D,UAAU;QACZ;QACApB,MAAM;YACJd;gBACE,MAAMU,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB0C,IAAI;YAC/B;QACF;QACAd,KAAK;YACHS,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB4B,GAAG,CAACgC,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACAhB,QAAQ;YACNT,OAAO,SAASS;gBACd,IAAIR;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB8C,MAAM,CAACc,KAAK,CACnC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;QACAf,KAAK;YACHV,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB+C,GAAG,CAACa,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACA9B,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMU,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPV,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLV,aAAa;oBACf;gBACF;gBACAqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBgC,GAAG,CAAC4B,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACAb,QAAQ;YACNZ,OAAO;gBACL,IAAIC;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLL,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBiD,MAAM,CAACW,KAAK,CACnC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;QACAZ,OAAO;YACLb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,mFAAmF;gBACnF,OAAO,OAAOtC,kBAAkBkD,KAAK,KAAK,aAEtClD,kBAAkBkD,KAAK,CAACU,KAAK,CAAC5D,mBAAmB2C,aAGjD,AADA,qGAAqG,YACY;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEc,+BAA+BI,IAAI,CAAC7D,mBAAmB6B;YAC7D;YACAiC,UAAU;QACZ;QACAX,UAAU;YACRd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBmD,QAAQ,CAACS,KAAK,CACrC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;IACF;IAEA,OAAOjC;AACT;AAEA,SAASgB,gBAAgBG,GAAY;IACnC,OAAO,OAAOA,QAAQ,YACpBA,QAAQ,QACR,OAAQA,IAAYe,IAAI,KAAK,WAC3B,CAAC,CAAC,EAAGf,IAAYe,IAAI,CAAC,CAAC,CAAC,GACxB,OAAOf,QAAQ,WACb,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GACV;AACR;AAEA,SAASW,UAAU7D,KAAyB,EAAEwC,UAAkB;IAC9D,MAAM7C,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcU,IAAI,KAAK,aACvBV,cAAcuE,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMrD,eAAelB;QACrBwE,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACtD;IACzC;IACA,gCAAgC;IAChCuD,kBAAkBpE,OAAOwC;AAC3B;AAEA,MAAM4B,oBAAoBC,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnE3B;AAGF,SAASA,yBACP1C,KAAyB,EACzBwC,UAAkB;IAElB,MAAM8B,SAAStE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAID,MACT,GAAGuE,OAAO,KAAK,EAAE9B,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASkB;IAGP,OAAO,IAAI,CAACV,MAAM,GACfuB,GAAG,CAAC,CAACC,IAAM;YAACA,EAAEP,IAAI;YAAEO;SAAE,EACtBC,MAAM;AACX;AAEA,SAASd,+BAEPe,UAA2C;IAE3C,KAAK,MAAMC,UAAU,IAAI,CAAC3B,MAAM,GAAI;QAClC,IAAI,CAACG,MAAM,CAACwB,OAAOV,IAAI;IACzB;IACA,OAAOS;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2350, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/web/spec-extension/adapters/headers.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReadonlyHeadersError", "Error", "constructor", "callable", "Headers", "headers", "Proxy", "get", "target", "prop", "receiver", "ReflectAdapter", "lowercased", "toLowerCase", "original", "Object", "keys", "find", "o", "set", "value", "has", "deleteProperty", "seal", "merge", "Array", "isArray", "join", "from", "append", "name", "existing", "push", "delete", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "call", "key", "values", "Symbol", "iterator"], "mappings": ";;;;;;;;;;;;;;;IA2BaA,cAAc,EAAA;eAAdA;;IApBAC,oBAAoB,EAAA;eAApBA;;;yBALkB;AAKxB,MAAMA,6BAA6BC;IACxCC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIH;IACZ;AACF;AAUO,MAAMD,uBAAuBK;IAGlCF,YAAYG,OAA4B,CAAE;QACxC,2EAA2E;QAC3E,2EAA2E;QAC3E,KAAK;QAEL,IAAI,CAACA,OAAO,GAAG,IAAIC,MAAMD,SAAS;YAChCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,sEAAsE;gBACtE,sEAAsE;gBACtE,cAAc;gBACd,IAAI,OAAOD,SAAS,UAAU;oBAC5B,OAAOE,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC1C;gBAEA,MAAME,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,0DAA0D;gBAC1D,IAAI,OAAOE,aAAa,aAAa;gBAErC,mDAAmD;gBACnD,OAAOH,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQM,UAAUJ;YAC9C;YACAS,KAAIX,MAAM,EAAEC,IAAI,EAAEW,KAAK,EAAEV,QAAQ;gBAC/B,IAAI,OAAOD,SAAS,UAAU;oBAC5B,OAAOE,SAAAA,cAAc,CAACQ,GAAG,CAACX,QAAQC,MAAMW,OAAOV;gBACjD;gBAEA,MAAME,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,iEAAiE;gBACjE,OAAOD,SAAAA,cAAc,CAACQ,GAAG,CAACX,QAAQM,YAAYL,MAAMW,OAAOV;YAC7D;YACAW,KAAIb,MAAM,EAAEC,IAAI;gBACd,IAAI,OAAOA,SAAS,UAAU,OAAOE,SAAAA,cAAc,CAACU,GAAG,CAACb,QAAQC;gBAEhE,MAAMG,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,sDAAsD;gBACtD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,8CAA8C;gBAC9C,OAAOH,SAAAA,cAAc,CAACU,GAAG,CAACb,QAAQM;YACpC;YACAQ,gBAAed,MAAM,EAAEC,IAAI;gBACzB,IAAI,OAAOA,SAAS,UAClB,OAAOE,SAAAA,cAAc,CAACW,cAAc,CAACd,QAAQC;gBAE/C,MAAMG,aAAaH,KAAKI,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACX,SAASY,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,qDAAqD;gBACrD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,sDAAsD;gBACtD,OAAOH,SAAAA,cAAc,CAACW,cAAc,CAACd,QAAQM;YAC/C;QACF;IACF;IAEA;;;GAGC,GACD,OAAcS,KAAKlB,OAAgB,EAAmB;QACpD,OAAO,IAAIC,MAAuBD,SAAS;YACzCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOT,qBAAqBG,QAAQ;oBACtC;wBACE,OAAOQ,SAAAA,cAAc,CAACJ,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACOc,MAAMJ,KAAwB,EAAU;QAC9C,IAAIK,MAAMC,OAAO,CAACN,QAAQ,OAAOA,MAAMO,IAAI,CAAC;QAE5C,OAAOP;IACT;IAEA;;;;;GAKC,GACD,OAAcQ,KAAKvB,OAAsC,EAAW;QAClE,IAAIA,mBAAmBD,SAAS,OAAOC;QAEvC,OAAO,IAAIN,eAAeM;IAC5B;IAEOwB,OAAOC,IAAY,EAAEV,KAAa,EAAQ;QAC/C,MAAMW,WAAW,IAAI,CAAC1B,OAAO,CAACyB,KAAK;QACnC,IAAI,OAAOC,aAAa,UAAU;YAChC,IAAI,CAAC1B,OAAO,CAACyB,KAAK,GAAG;gBAACC;gBAAUX;aAAM;QACxC,OAAO,IAAIK,MAAMC,OAAO,CAACK,WAAW;YAClCA,SAASC,IAAI,CAACZ;QAChB,OAAO;YACL,IAAI,CAACf,OAAO,CAACyB,KAAK,GAAGV;QACvB;IACF;IAEOa,OAAOH,IAAY,EAAQ;QAChC,OAAO,IAAI,CAACzB,OAAO,CAACyB,KAAK;IAC3B;IAEOvB,IAAIuB,IAAY,EAAiB;QACtC,MAAMV,QAAQ,IAAI,CAACf,OAAO,CAACyB,KAAK;QAChC,IAAI,OAAOV,UAAU,aAAa,OAAO,IAAI,CAACI,KAAK,CAACJ;QAEpD,OAAO;IACT;IAEOC,IAAIS,IAAY,EAAW;QAChC,OAAO,OAAO,IAAI,CAACzB,OAAO,CAACyB,KAAK,KAAK;IACvC;IAEOX,IAAIW,IAAY,EAAEV,KAAa,EAAQ;QAC5C,IAAI,CAACf,OAAO,CAACyB,KAAK,GAAGV;IACvB;IAEOc,QACLC,UAAkE,EAClEC,OAAa,EACP;QACN,KAAK,MAAM,CAACN,MAAMV,MAAM,IAAI,IAAI,CAACiB,OAAO,GAAI;YAC1CF,WAAWG,IAAI,CAACF,SAAShB,OAAOU,MAAM,IAAI;QAC5C;IACF;IAEA,CAAQO,UAA6C;QACnD,KAAK,MAAME,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAG;YAC3C,MAAMyB,OAAOS,IAAI1B,WAAW;YAC5B,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMO,QAAQ,IAAI,CAACb,GAAG,CAACuB;YAEvB,MAAM;gBAACA;gBAAMV;aAAM;QACrB;IACF;IAEA,CAAQJ,OAAgC;QACtC,KAAK,MAAMuB,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAG;YAC3C,MAAMyB,OAAOS,IAAI1B,WAAW;YAC5B,MAAMiB;QACR;IACF;IAEA,CAAQU,SAAkC;QACxC,KAAK,MAAMD,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACX,OAAO,EAAG;YAC3C,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMe,QAAQ,IAAI,CAACb,GAAG,CAACgC;YAEvB,MAAMnB;QACR;IACF;IAEO,CAACqB,OAAOC,QAAQ,CAAC,GAAsC;QAC5D,OAAO,IAAI,CAACL,OAAO;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2545, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/request/headers.ts"], "sourcesContent": ["import {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * In this version of Next.js `headers()` returns a Promise however you can still reference the properties of the underlying Headers instance\n * synchronously to facilitate migration. The `UnsafeUnwrappedHeaders` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `headers()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedHeaders` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `headers()` value can be awaited or you should call `headers()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedHeaders` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `headers()` will only return a Promise and you will not be able to access the underlying Headers instance\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedHeaders` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedHeaders = ReadonlyHeaders\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedExoticHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We don't track dynamic access here because access will be tracked when you access\n        // one of the properties of the headers object.\n        return makeDynamicallyTrackedExoticHeaders(\n          workStore.route,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We are prerendering with PPR. We need track dynamic access here eagerly\n        // to keep continuity with how headers has worked in PPR without dynamicIO.\n        // TODO consider switching the semantic to throw on property access instead\n        postponeWithTracking(\n          workStore.route,\n          'headers',\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We are in a legacy static generation mode while prerendering\n        // We track dynamic access here so we don't need to wrap the headers in\n        // individual property access tracking.\n        throwToInterruptStaticGeneration('headers', workStore, workUnitStore)\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using headers inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  const requestStore = getExpectedRequestStore('headers')\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    return makeUntrackedExoticHeadersWithDevWarnings(\n      requestStore.headers,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticHeaders(requestStore.headers)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeDynamicallyTrackedExoticHeaders(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`headers()[Symbol.iterator]()`'\n        const error = createHeadersAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: underlyingHeaders.append.bind(underlyingHeaders),\n    },\n    delete: {\n      value: underlyingHeaders.delete.bind(underlyingHeaders),\n    },\n    get: {\n      value: underlyingHeaders.get.bind(underlyingHeaders),\n    },\n    has: {\n      value: underlyingHeaders.has.bind(underlyingHeaders),\n    },\n    set: {\n      value: underlyingHeaders.set.bind(underlyingHeaders),\n    },\n    getSetCookie: {\n      value: underlyingHeaders.getSetCookie.bind(underlyingHeaders),\n    },\n    forEach: {\n      value: underlyingHeaders.forEach.bind(underlyingHeaders),\n    },\n    keys: {\n      value: underlyingHeaders.keys.bind(underlyingHeaders),\n    },\n    values: {\n      value: underlyingHeaders.values.bind(underlyingHeaders),\n    },\n    entries: {\n      value: underlyingHeaders.entries.bind(underlyingHeaders),\n    },\n    [Symbol.iterator]: {\n      value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders),\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = new Promise<ReadonlyHeaders>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingHeaders))\n  )\n\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.append.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.delete.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.get.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.has.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.set.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.getSetCookie.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        syncIODev(route, expression)\n        return underlyingHeaders.forEach.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.keys.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.values.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.entries.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...headers()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingHeaders[Symbol.iterator].apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'string' ? `'${arg}'` : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\ntype HeadersExtensions = {\n  [K in keyof ReadonlyHeaders]: unknown\n}\n"], "names": ["headers", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "Headers", "makeUntrackedExoticHeaders", "type", "dynamicShouldError", "StaticGenBailoutError", "makeDynamicallyTrackedExoticHeaders", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "requestStore", "getExpectedRequestStore", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeUntrackedExoticHeadersWithDevWarnings", "CachedHeaders", "WeakMap", "prerenderStore", "cachedHeaders", "get", "promise", "makeHangingPromise", "renderSignal", "set", "Object", "defineProperties", "append", "value", "expression", "describeNameArg", "arguments", "error", "createHeadersAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "delete", "_delete", "has", "getSetCookie", "for<PERSON>ach", "keys", "values", "entries", "Symbol", "iterator", "Promise", "resolve", "bind", "scheduleImmediate", "syncIODev", "apply", "arg", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "prefix"], "mappings": ";;;;+BAuDgBA,WAAAA;;;eAAAA;;;yBApDT;0CAC0B;8CACO;kCAWjC;yCAC+B;uCACH;0DACyB;2BAC1B;uBACc;AAkCzC,SAASA;IACd,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,CAAAA,GAAAA,OAAAA,+BAA+B,KAChC;YACA,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,yOAAyO,CAAC,GAD/P,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC,SAAAA,cAAc,CAACC,IAAI,CAAC,IAAIC,QAAQ,CAAC;YAC3D,OAAOC,2BAA2BJ;QACpC;QAEA,IAAIP,eAAe;YACjB,IAAIA,cAAcY,IAAI,KAAK,SAAS;gBAClC,MAAM,OAAA,cAEL,CAFK,IAAIR,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIL,cAAcY,IAAI,KAAK,kBAAkB;gBAClD,MAAM,OAAA,cAEL,CAFK,IAAIR,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIR,UAAUgB,kBAAkB,EAAE;YAChC,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEjB,UAAUQ,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcY,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,oFAAoF;gBACpF,+CAA+C;gBAC/C,OAAOG,oCACLlB,UAAUQ,KAAK,EACfL;YAEJ,OAAO,IAAIA,cAAcY,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,0EAA0E;gBAC1E,2EAA2E;gBAC3E,2EAA2E;gBAC3EI,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBnB,UAAUQ,KAAK,EACf,WACAL,cAAciB,eAAe;YAEjC,OAAO,IAAIjB,cAAcY,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,+DAA+D;gBAC/D,uEAAuE;gBACvE,uCAAuC;gBACvCM,CAAAA,GAAAA,kBAAAA,gCAAgC,EAAC,WAAWrB,WAAWG;YACzD;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFmB,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACtB,WAAWG;IAC7C;IAEA,MAAMoB,eAAeC,CAAAA,GAAAA,8BAAAA,uBAAuB,EAAC;IAC7C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiB,CAAA,CAAC3B,aAAAA,OAAAA,KAAAA,IAAAA,UAAW4B,iBAAiB,GAAE;QAC3E,OAAOC,0CACLN,aAAaxB,OAAO,EACpBC,aAAAA,OAAAA,KAAAA,IAAAA,UAAWQ,KAAK;IAEpB,OAAO;QACL,OAAOM,2BAA2BS,aAAaxB,OAAO;IACxD;AACF;AAGA,MAAM+B,gBAAgB,IAAIC;AAE1B,SAASb,oCACPV,KAAa,EACbwB,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCJ,eAAeK,YAAY,EAC3B;IAEFP,cAAcQ,GAAG,CAACN,gBAAgBG;IAElCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/BM,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAME,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChF,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAiB,QAAQ;YACNP,OAAO,SAASQ;gBACd,MAAMP,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3E,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAE,KAAK;YACHQ,OAAO,SAASR;gBACd,MAAMS,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxE,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAmB,KAAK;YACHT,OAAO,SAASS;gBACd,MAAMR,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxE,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAM,KAAK;YACHI,OAAO,SAASJ;gBACd,MAAMK,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7E,MAAMC,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAoB,cAAc;YACZV,OAAO,SAASU;gBACd,MAAMT,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAqB,SAAS;YACPX,OAAO,SAASW;gBACd,MAAMV,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAsB,MAAM;YACJZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAuB,QAAQ;YACNb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACAwB,SAAS;YACPd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;QACA,CAACyB,OAAOC,QAAQ,CAAC,EAAE;YACjBhB,OAAO;gBACL,MAAMC,aAAa;gBACnB,MAAMG,QAAQC,yBAAyBvC,OAAOmC;gBAC9CK,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxC,OACAmC,YACAG,OACAd;YAEJ;QACF;IACF;IAEA,OAAOG;AACT;AAEA,SAASrB,2BACPJ,iBAAkC;IAElC,MAAMuB,gBAAgBH,cAAcI,GAAG,CAACxB;IACxC,IAAIuB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUwB,QAAQC,OAAO,CAAClD;IAChCoB,cAAcQ,GAAG,CAAC5B,mBAAmByB;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/BM,QAAQ;YACNC,OAAOhC,kBAAkB+B,MAAM,CAACoB,IAAI,CAACnD;QACvC;QACAuC,QAAQ;YACNP,OAAOhC,kBAAkBuC,MAAM,CAACY,IAAI,CAACnD;QACvC;QACAwB,KAAK;YACHQ,OAAOhC,kBAAkBwB,GAAG,CAAC2B,IAAI,CAACnD;QACpC;QACAyC,KAAK;YACHT,OAAOhC,kBAAkByC,GAAG,CAACU,IAAI,CAACnD;QACpC;QACA4B,KAAK;YACHI,OAAOhC,kBAAkB4B,GAAG,CAACuB,IAAI,CAACnD;QACpC;QACA0C,cAAc;YACZV,OAAOhC,kBAAkB0C,YAAY,CAACS,IAAI,CAACnD;QAC7C;QACA2C,SAAS;YACPX,OAAOhC,kBAAkB2C,OAAO,CAACQ,IAAI,CAACnD;QACxC;QACA4C,MAAM;YACJZ,OAAOhC,kBAAkB4C,IAAI,CAACO,IAAI,CAACnD;QACrC;QACA6C,QAAQ;YACNb,OAAOhC,kBAAkB6C,MAAM,CAACM,IAAI,CAACnD;QACvC;QACA8C,SAAS;YACPd,OAAOhC,kBAAkB8C,OAAO,CAACK,IAAI,CAACnD;QACxC;QACA,CAAC+C,OAAOC,QAAQ,CAAC,EAAE;YACjBhB,OAAOhC,iBAAiB,CAAC+C,OAAOC,QAAQ,CAAC,CAACG,IAAI,CAACnD;QACjD;IACF;IAEA,OAAOyB;AACT;AAEA,SAASN,0CACPnB,iBAAkC,EAClCF,KAAc;IAEd,MAAMyB,gBAAgBH,cAAcI,GAAG,CAACxB;IACxC,IAAIuB,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAU,IAAIwB,QAAyB,CAACC,UAC5CE,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMF,QAAQlD;IAGlCoB,cAAcQ,GAAG,CAAC5B,mBAAmByB;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/BM,QAAQ;YACNC,OAAO,SAASD;gBACd,MAAME,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAChFkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB+B,MAAM,CAACuB,KAAK,CACnCtD,mBACAmC;YAEJ;QACF;QACAI,QAAQ;YACNP,OAAO,SAASQ;gBACd,MAAMP,aAAa,CAAC,mBAAmB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBAC3EkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkBuC,MAAM,CAACe,KAAK,CACnCtD,mBACAmC;YAEJ;QACF;QACAX,KAAK;YACHQ,OAAO,SAASR;gBACd,MAAMS,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkBwB,GAAG,CAAC8B,KAAK,CAACtD,mBAAmBmC;YACxD;QACF;QACAM,KAAK;YACHT,OAAO,SAASS;gBACd,MAAMR,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACxEkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkByC,GAAG,CAACa,KAAK,CAACtD,mBAAmBmC;YACxD;QACF;QACAP,KAAK;YACHI,OAAO,SAASJ;gBACd,MAAMK,aAAa,CAAC,gBAAgB,EAAEC,gBAAgBC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC7EkB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB4B,GAAG,CAAC0B,KAAK,CAACtD,mBAAmBmC;YACxD;QACF;QACAO,cAAc;YACZV,OAAO,SAASU;gBACd,MAAMT,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB0C,YAAY,CAACY,KAAK,CACzCtD,mBACAmC;YAEJ;QACF;QACAQ,SAAS;YACPX,OAAO,SAASW;gBACd,MAAMV,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB2C,OAAO,CAACW,KAAK,CACpCtD,mBACAmC;YAEJ;QACF;QACAS,MAAM;YACJZ,OAAO,SAASY;gBACd,MAAMX,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB4C,IAAI,CAACU,KAAK,CAACtD,mBAAmBmC;YACzD;QACF;QACAU,QAAQ;YACNb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB6C,MAAM,CAACS,KAAK,CACnCtD,mBACAmC;YAEJ;QACF;QACAW,SAAS;YACPd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,kBAAkB8C,OAAO,CAACQ,KAAK,CACpCtD,mBACAmC;YAEJ;QACF;QACA,CAACY,OAAOC,QAAQ,CAAC,EAAE;YACjBhB,OAAO;gBACL,MAAMC,aAAa;gBACnBoB,UAAUvD,OAAOmC;gBACjB,OAAOjC,iBAAiB,CAAC+C,OAAOC,QAAQ,CAAC,CAACM,KAAK,CAC7CtD,mBACAmC;YAEJ;QACF;IACF;IAEA,OAAOV;AACT;AAEA,SAASS,gBAAgBqB,GAAY;IACnC,OAAO,OAAOA,QAAQ,WAAW,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GAAG;AAChD;AAEA,SAASF,UAAUvD,KAAyB,EAAEmC,UAAkB;IAC9D,MAAMxC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcY,IAAI,KAAK,aACvBZ,cAAc+D,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAM3C,eAAepB;QACrBgE,CAAAA,GAAAA,kBAAAA,sCAAsC,EAAC5C;IACzC;IACA,gCAAgC;IAChC6C,kBAAkB5D,OAAOmC;AAC3B;AAEA,MAAMyB,oBAAoBC,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEtB;AAGF,SAASA,yBACPvC,KAAyB,EACzBmC,UAAkB;IAElB,MAAM2B,SAAS9D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAID,MACT,GAAG+D,OAAO,KAAK,EAAE3B,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/src/server/request/draft-mode.ts"], "sourcesContent": ["import {\n  getDraftModeProviderForCacheScope,\n  throwForMissingRequestStore,\n} from '../app-render/work-unit-async-storage.external'\n\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\n\n/**\n * In this version of Next.js `draftMode()` returns a Promise however you can still reference the properties of the underlying draftMode object\n * synchronously to facilitate migration. The `UnsafeUnwrappedDraftMode` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `draftMode()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedDraftMode` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `draftMode()` value can be awaited or you should call `draftMode()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedDraftMode` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `draftMode()` will only return a Promise and you will not be able to access the underlying draftMode object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedDraftMode` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedDraftMode = DraftMode\n\nexport function draftMode(): Promise<DraftMode> {\n  const callingExpression = 'draftMode'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workStore || !workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'request':\n      return createOrGetCachedExoticDraftMode(\n        workUnitStore.draftMode,\n        workStore\n      )\n\n    case 'cache':\n    case 'unstable-cache':\n      // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n      // the outmost work unit store is a request store, and if draft mode is\n      // enabled.\n      const draftModeProvider = getDraftModeProviderForCacheScope(\n        workStore,\n        workUnitStore\n      )\n\n      if (draftModeProvider) {\n        return createOrGetCachedExoticDraftMode(draftModeProvider, workStore)\n      }\n\n    // Otherwise, we fall through to providing an empty draft mode.\n    // eslint-disable-next-line no-fallthrough\n    case 'prerender':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // Return empty draft mode\n      if (\n        process.env.NODE_ENV === 'development' &&\n        !workStore?.isPrefetchRequest\n      ) {\n        const route = workStore?.route\n        return createExoticDraftModeWithDevWarnings(null, route)\n      } else {\n        return createExoticDraftMode(null)\n      }\n\n    default:\n      const _exhaustiveCheck: never = workUnitStore\n      return _exhaustiveCheck\n  }\n}\n\nfunction createOrGetCachedExoticDraftMode(\n  draftModeProvider: DraftModeProvider,\n  workStore: WorkStore | undefined\n): Promise<DraftMode> {\n  const cachedDraftMode = CachedDraftModes.get(draftMode)\n\n  if (cachedDraftMode) {\n    return cachedDraftMode\n  }\n\n  let promise: Promise<DraftMode>\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    const route = workStore?.route\n    promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route)\n  } else {\n    promise = createExoticDraftMode(draftModeProvider)\n  }\n\n  CachedDraftModes.set(draftModeProvider, promise)\n\n  return promise\n}\n\ninterface CacheLifetime {}\nconst CachedDraftModes = new WeakMap<CacheLifetime, Promise<DraftMode>>()\n\nfunction createExoticDraftMode(\n  underlyingProvider: null | DraftModeProvider\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      return instance.isEnabled\n    },\n    set(newValue) {\n      Object.defineProperty(promise, 'isEnabled', {\n        value: newValue,\n        writable: true,\n        enumerable: true,\n      })\n    },\n    enumerable: true,\n    configurable: true,\n  })\n  ;(promise as any).enable = instance.enable.bind(instance)\n  ;(promise as any).disable = instance.disable.bind(instance)\n\n  return promise\n}\n\nfunction createExoticDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      const expression = '`draftMode().isEnabled`'\n      syncIODev(route, expression)\n      return instance.isEnabled\n    },\n    set(newValue) {\n      Object.defineProperty(promise, 'isEnabled', {\n        value: newValue,\n        writable: true,\n        enumerable: true,\n      })\n    },\n    enumerable: true,\n    configurable: true,\n  })\n\n  Object.defineProperty(promise, 'enable', {\n    value: function get() {\n      const expression = '`draftMode().enable()`'\n      syncIODev(route, expression)\n      return instance.enable.apply(instance, arguments as any)\n    },\n  })\n\n  Object.defineProperty(promise, 'disable', {\n    value: function get() {\n      const expression = '`draftMode().disable()`'\n      syncIODev(route, expression)\n      return instance.disable.apply(instance, arguments as any)\n    },\n  })\n\n  return promise\n}\n\nclass DraftMode {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _provider: null | DraftModeProvider\n\n  constructor(provider: null | DraftModeProvider) {\n    this._provider = provider\n  }\n  get isEnabled() {\n    if (this._provider !== null) {\n      return this._provider.isEnabled\n    }\n    return false\n  }\n  public enable() {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    trackDynamicDraftMode('draftMode().enable()')\n    if (this._provider !== null) {\n      this._provider.enable()\n    }\n  }\n  public disable() {\n    trackDynamicDraftMode('draftMode().disable()')\n    if (this._provider !== null) {\n      this._provider.disable()\n    }\n  }\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createDraftModeAccessError\n)\n\nfunction createDraftModeAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`draftMode()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction trackDynamicDraftMode(expression: string) {\n  const store = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (store) {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      } else if (workUnitStore.phase === 'after') {\n        throw new Error(\n          `Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`\n        )\n      }\n    }\n\n    if (store.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        const error = new Error(\n          `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`\n        )\n        abortAndThrowOnSynchronousRequestDataAccess(\n          store.route,\n          expression,\n          error,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender\n        postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // legacy Prerender\n        workUnitStore.revalidate = 0\n\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      } else if (\n        process.env.NODE_ENV === 'development' &&\n        workUnitStore &&\n        workUnitStore.type === 'request'\n      ) {\n        workUnitStore.usedDynamic = true\n      }\n    }\n  }\n}\n"], "names": ["draftMode", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "throwForMissingRequestStore", "type", "createOrGetCachedExoticDraftMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDraftModeProviderForCacheScope", "process", "env", "NODE_ENV", "isPrefetchRequest", "route", "createExoticDraftModeWithDevWarnings", "createExoticDraftMode", "_exhaustiveCheck", "cachedDraftMode", "CachedDraftModes", "get", "promise", "set", "WeakMap", "underlyingProvider", "instance", "DraftMode", "Promise", "resolve", "Object", "defineProperty", "isEnabled", "newValue", "value", "writable", "enumerable", "configurable", "enable", "bind", "disable", "expression", "syncIODev", "apply", "arguments", "constructor", "provider", "_provider", "trackDynamicDraftMode", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "createDraftModeAccessError", "prefix", "Error", "store", "phase", "dynamicShouldError", "StaticGenBailoutError", "error", "abortAndThrowOnSynchronousRequestDataAccess", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "usedDynamic"], "mappings": ";;;;+BA4Cg<PERSON>,aAAAA;;;eAAAA;;;8CAzCT;0CAOA;kCAMA;0DACqD;yCACtB;oCACH;AAyB5B,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IAEnD,IAAI,CAACF,aAAa,CAACG,eAAe;QAChCE,CAAAA,GAAAA,8BAAAA,2BAA2B,EAACN;IAC9B;IAEA,OAAQI,cAAcG,IAAI;QACxB,KAAK;YACH,OAAOC,iCACLJ,cAAcL,SAAS,EACvBE;QAGJ,KAAK;QACL,KAAK;YACH,0EAA0E;YAC1E,uEAAuE;YACvE,WAAW;YACX,MAAMQ,oBAAoBC,CAAAA,GAAAA,8BAAAA,iCAAiC,EACzDT,WACAG;YAGF,IAAIK,mBAAmB;gBACrB,OAAOD,iCAAiCC,mBAAmBR;YAC7D;QAEF,+DAA+D;QAC/D,0CAA0C;QAC1C,KAAK;QACL,KAAK;QACL,KAAK;YACH,0BAA0B;YAC1B,IACEU,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,CAAA,CAACZ,aAAAA,OAAAA,KAAAA,IAAAA,UAAWa,iBAAiB,GAC7B;gBACA,MAAMC,QAAQd,aAAAA,OAAAA,KAAAA,IAAAA,UAAWc,KAAK;gBAC9B,OAAOC,qCAAqC,MAAMD;YACpD,OAAO;gBACL,OAAOE,sBAAsB;YAC/B;QAEF;YACE,MAAMC,mBAA0Bd;YAChC,OAAOc;IACX;AACF;AAEA,SAASV,iCACPC,iBAAoC,EACpCR,SAAgC;IAEhC,MAAMkB,kBAAkBC,iBAAiBC,GAAG,CAACtB;IAE7C,IAAIoB,iBAAiB;QACnB,OAAOA;IACT;IAEA,IAAIG;IAEJ,IAAIX,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiB,CAAA,CAACZ,aAAAA,OAAAA,KAAAA,IAAAA,UAAWa,iBAAiB,GAAE;QAC3E,MAAMC,QAAQd,aAAAA,OAAAA,KAAAA,IAAAA,UAAWc,KAAK;QAC9BO,UAAUN,qCAAqCP,mBAAmBM;IACpE,OAAO;QACLO,UAAUL,sBAAsBR;IAClC;IAEAW,iBAAiBG,GAAG,CAACd,mBAAmBa;IAExC,OAAOA;AACT;AAGA,MAAMF,mBAAmB,IAAII;AAE7B,SAASP,sBACPQ,kBAA4C;IAE5C,MAAMC,WAAW,IAAIC,UAAUF;IAC/B,MAAMH,UAAUM,QAAQC,OAAO,CAACH;IAEhCI,OAAOC,cAAc,CAACT,SAAS,aAAa;QAC1CD;YACE,OAAOK,SAASM,SAAS;QAC3B;QACAT,KAAIU,QAAQ;YACVH,OAAOC,cAAc,CAACT,SAAS,aAAa;gBAC1CY,OAAOD;gBACPE,UAAU;gBACVC,YAAY;YACd;QACF;QACAA,YAAY;QACZC,cAAc;IAChB;IACEf,QAAgBgB,MAAM,GAAGZ,SAASY,MAAM,CAACC,IAAI,CAACb;IAC9CJ,QAAgBkB,OAAO,GAAGd,SAASc,OAAO,CAACD,IAAI,CAACb;IAElD,OAAOJ;AACT;AAEA,SAASN,qCACPS,kBAA4C,EAC5CV,KAAyB;IAEzB,MAAMW,WAAW,IAAIC,UAAUF;IAC/B,MAAMH,UAAUM,QAAQC,OAAO,CAACH;IAEhCI,OAAOC,cAAc,CAACT,SAAS,aAAa;QAC1CD;YACE,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASM,SAAS;QAC3B;QACAT,KAAIU,QAAQ;YACVH,OAAOC,cAAc,CAACT,SAAS,aAAa;gBAC1CY,OAAOD;gBACPE,UAAU;gBACVC,YAAY;YACd;QACF;QACAA,YAAY;QACZC,cAAc;IAChB;IAEAP,OAAOC,cAAc,CAACT,SAAS,UAAU;QACvCY,OAAO,SAASb;YACd,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASY,MAAM,CAACK,KAAK,CAACjB,UAAUkB;QACzC;IACF;IAEAd,OAAOC,cAAc,CAACT,SAAS,WAAW;QACxCY,OAAO,SAASb;YACd,MAAMoB,aAAa;YACnBC,UAAU3B,OAAO0B;YACjB,OAAOf,SAASc,OAAO,CAACG,KAAK,CAACjB,UAAUkB;QAC1C;IACF;IAEA,OAAOtB;AACT;AAEA,MAAMK;IAMJkB,YAAYC,QAAkC,CAAE;QAC9C,IAAI,CAACC,SAAS,GAAGD;IACnB;IACA,IAAId,YAAY;QACd,IAAI,IAAI,CAACe,SAAS,KAAK,MAAM;YAC3B,OAAO,IAAI,CAACA,SAAS,CAACf,SAAS;QACjC;QACA,OAAO;IACT;IACOM,SAAS;QACd,oEAAoE;QACpE,+DAA+D;QAC/DU,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACT,MAAM;QACvB;IACF;IACOE,UAAU;QACfQ,sBAAsB;QACtB,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACP,OAAO;QACxB;IACF;AACF;AAEA,SAASE,UAAU3B,KAAyB,EAAE0B,UAAkB;IAC9D,MAAMrC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAc6C,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAe9C;QACrB+C,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;IACA,gCAAgC;IAChCE,kBAAkBrC,OAAO0B;AAC3B;AAEA,MAAMW,oBAAoBC,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,SAASA,2BACPvC,KAAyB,EACzB0B,UAAkB;IAElB,MAAMc,SAASxC,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIyC,MACT,GAAGD,OAAO,KAAK,EAAEd,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASO,sBAAsBP,UAAkB;IAC/C,MAAMgB,QAAQvD,0BAAAA,gBAAgB,CAACC,QAAQ;IACvC,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACF,QAAQ;IACnD,IAAIsD,OAAO;QACT,oEAAoE;QACpE,+DAA+D;QAC/D,IAAIrD,eAAe;YACjB,IAAIA,cAAcG,IAAI,KAAK,SAAS;gBAClC,MAAM,OAAA,cAEL,CAFK,IAAIiD,MACR,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,OAAO,EAAE0B,WAAW,uNAAuN,CAAC,GAD7P,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIrC,cAAcG,IAAI,KAAK,kBAAkB;gBAClD,MAAM,OAAA,cAEL,CAFK,IAAIiD,MACR,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,OAAO,EAAE0B,WAAW,gQAAgQ,CAAC,GADtS,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIrC,cAAcsD,KAAK,KAAK,SAAS;gBAC1C,MAAM,OAAA,cAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,OAAO,EAAE0B,WAAW,0MAA0M,CAAC,GADhP,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAIgB,MAAME,kBAAkB,EAAE;YAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEH,MAAM1C,KAAK,CAAC,8EAA8E,EAAE0B,WAAW,4HAA4H,CAAC,GADzO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIrC,eAAe;YACjB,IAAIA,cAAcG,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,MAAMsD,QAAQ,OAAA,cAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAEC,MAAM1C,KAAK,CAAC,MAAM,EAAE0B,WAAW,+HAA+H,CAAC,GAD5J,qBAAA;2BAAA;gCAAA;kCAAA;gBAEd;gBACAqB,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCL,MAAM1C,KAAK,EACX0B,YACAoB,OACAzD;YAEJ,OAAO,IAAIA,cAAcG,IAAI,KAAK,iBAAiB;gBACjD,gBAAgB;gBAChBwD,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBN,MAAM1C,KAAK,EACX0B,YACArC,cAAc4D,eAAe;YAEjC,OAAO,IAAI5D,cAAcG,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnBH,cAAc6D,UAAU,GAAG;gBAE3B,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEV,MAAM1C,KAAK,CAAC,mDAAmD,EAAE0B,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAgB,MAAMW,uBAAuB,GAAG3B;gBAChCgB,MAAMY,iBAAiB,GAAGH,IAAII,KAAK;gBAEnC,MAAMJ;YACR,OAAO,IACLvD,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBT,iBACAA,cAAcG,IAAI,KAAK,WACvB;gBACAH,cAAcmE,WAAW,GAAG;YAC9B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/headers.js"], "sourcesContent": ["module.exports.cookies = require('./dist/server/request/cookies').cookies\nmodule.exports.headers = require('./dist/server/request/headers').headers\nmodule.exports.draftMode = require('./dist/server/request/draft-mode').draftMode\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,CAAC,OAAO,GAAG,6GAAyC,OAAO;AACzE,OAAO,OAAO,CAAC,OAAO,GAAG,6GAAyC,OAAO;AACzE,OAAO,OAAO,CAAC,SAAS,GAAG,gHAA4C,SAAS", "ignoreList": [0], "debugId": null}}]}