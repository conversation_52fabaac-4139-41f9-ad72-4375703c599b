(()=>{var e={};e.id=6792,e.ids=[6792],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var i=r(36344),n=r(65752),s=r(13581),a=r(75745),o=r(17063);let c={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,n.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,s.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,a.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,n]=r.split(":");if(i!==e.token||n!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,a.A)();try{let i=await o.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i);let s=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),a=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[s],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({email:1}),a.index({role:1}),a.index({emailVerificationToken:1}),a.index({"accounts.provider":1,"accounts.providerAccountId":1}),a.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},a.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=n().models.User||n().model("User",a)},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var n=r(12269);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var o=n?Object.getOwnPropertyDescriptor(e,s):null;o&&(o.get||o.set)?Object.defineProperty(i,s,o):i[s]=e[s]}return i.default=e,r&&r.set(e,i),i}(r(35426));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i),s=r(36317);let a=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:s.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({launchDate:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=n().models.Tool||n().model("Tool",a)},36317:(e,t,r)=>{"use strict";r.d(t,{Bi:()=>n,PZ:()=>a,ut:()=>o,vK:()=>s});let i=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}];[...i.map(e=>({value:e.slug,label:e.name}))];let n=i.reduce((e,t)=>(e[t.slug]=t.name,e),{}),s=i.reduce((e,t)=>(e[t.slug]=t,e),{}),a=e=>s[e],o=i.map(e=>e.slug)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(s,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},95670:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>h,serverHooks:()=>w,workAsyncStorage:()=>I,workUnitAsyncStorage:()=>A});var i={};r.r(i),r.d(i,{GET:()=>v,POST:()=>x});var n=r(96559),s=r(48088),a=r(37719),o=r(32190),c=r(19854),u=r(12909),l=r(75745),d=r(56037),p=r.n(d);let m=new d.Schema({toolId:{type:d.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},userId:{type:d.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},content:{type:String,required:[!0,"Comment content is required"],trim:!0,maxlength:[1e3,"Comment cannot exceed 1000 characters"],minlength:[1,"Comment cannot be empty"]},parentId:{type:d.Schema.Types.ObjectId,ref:"Comment",default:null},likes:{type:Number,default:0,min:0},isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});m.index({toolId:1,createdAt:-1}),m.index({userId:1}),m.index({parentId:1}),m.index({isActive:1}),m.virtual("replyCount",{ref:"Comment",localField:"_id",foreignField:"parentId",count:!0,match:{isActive:!0}}),m.statics.getToolComments=function(e){return this.find({toolId:e,isActive:!0}).populate("userId","name avatar").populate({path:"parentId",select:"content userId",populate:{path:"userId",select:"name"}}).sort({createdAt:-1})},m.methods.getReplies=function(){return p().model("Comment").find({parentId:this._id,isActive:!0}).populate("userId","name avatar").sort({createdAt:1})};let g=p().models.Comment||p().model("Comment",m);var f=r(17063),y=r(30762);async function v(e,{params:t}){try{await (0,l.A)();let r=t.id,{searchParams:i}=new URL(e.url),n=parseInt(i.get("page")||"1"),s=parseInt(i.get("limit")||"10"),a=(n-1)*s,c=await g.find({toolId:r,isActive:!0,parentId:{$exists:!1}}).populate("userId","name email image").sort({createdAt:-1}).skip(a).limit(s),u=await Promise.all(c.map(async e=>{let t=await g.find({parentId:e._id,isActive:!0}).populate("userId","name email image").sort({createdAt:1});return{...e.toObject(),replies:t}})),d=await g.countDocuments({toolId:r,isActive:!0,parentId:{$exists:!1}});return o.NextResponse.json({success:!0,data:{comments:u,pagination:{page:n,limit:s,total:d,pages:Math.ceil(d/s)}}})}catch(e){return console.error("Get comments error:",e),o.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}async function x(e,{params:t}){try{let r=await (0,c.getServerSession)(u.N);if(!r?.user?.email)return o.NextResponse.json({success:!1,message:"请先登录"},{status:401});await (0,l.A)();let i=await f.A.findOne({email:r.user.email});if(!i)return o.NextResponse.json({success:!1,message:"用户不存在"},{status:404});let n=t.id,{content:s,parentId:a}=await e.json();if(!s||0===s.trim().length)return o.NextResponse.json({success:!1,message:"评论内容不能为空"},{status:400});if(s.length>1e3)return o.NextResponse.json({success:!1,message:"评论内容不能超过1000字符"},{status:400});if(!await y.A.findById(n))return o.NextResponse.json({success:!1,message:"工具不存在"},{status:404});if(a){let e=await g.findById(a);if(!e||e.toolId!==n)return o.NextResponse.json({success:!1,message:"父评论不存在"},{status:404})}let d=new g({toolId:n,userId:i._id,content:s.trim(),parentId:a||void 0});return await d.save(),await d.populate("userId","name email image"),i.comments.includes(d._id.toString())||(i.comments.push(d._id.toString()),await i.save()),o.NextResponse.json({success:!0,data:d},{status:201})}catch(e){return console.error("Create comment error:",e),o.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/tools/[id]/comments/route",pathname:"/api/tools/[id]/comments",filename:"route",bundlePath:"app/api/tools/[id]/comments/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:I,workUnitAsyncStorage:A,serverHooks:w}=h;function S(){return(0,a.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:A})}},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,580,3136],()=>r(95670));module.exports=i})();