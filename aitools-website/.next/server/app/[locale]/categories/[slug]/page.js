(()=>{var e={};e.id=14,e.ids=[14],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8639:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx","default")},9283:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,82385)),Promise.resolve().then(t.bind(t,5973))},9963:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,8639)),Promise.resolve().then(t.bind(t,92588))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25366:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36317:(e,s,t)=>{"use strict";t.d(s,{Bi:()=>r,PZ:()=>i,ut:()=>n,vK:()=>l});let a=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}];[...a.map(e=>({value:e.slug,label:e.name}))];let r=a.reduce((e,s)=>(e[s.slug]=s.name,e),{}),l=a.reduce((e,s)=>(e[s.slug]=s,e),{}),i=e=>l[e],n=a.map(e=>e.slug)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78272:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},80872:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c={children:["",{children:["[locale]",{children:["categories",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,93262)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/categories/[slug]/page",pathname:"/[locale]/categories/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},82385:(e,s,t)=>{"use strict";t.d(s,{default:()=>h});var a=t(60687),r=t(43210),l=t(85814),i=t.n(l),n=t(73899),o=t(11011),c=t(28559),d=t(80462),m=t(78272),x=t(6943),u=t(25366);let p=[{value:"",label:"所有价格"},{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}],g=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function h({categoryInfo:e,tools:s,error:t}){let[l,h]=(0,r.useState)(""),[b,f]=(0,r.useState)(""),[v,y]=(0,r.useState)("popular"),[j,w]=(0,r.useState)("grid"),[N,A]=(0,r.useState)(!1);if(t)return(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(o.A,{message:t})});if(!e)return(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"分类未找到"}),(0,a.jsx)("p",{className:"text-gray-600",children:"请检查URL或返回分类列表"}),(0,a.jsx)(i(),{href:"/categories",className:"text-blue-600 hover:text-blue-700 mt-4 inline-block",children:"返回分类列表"})]})});let C=[...s.filter(e=>{let s=e.name.toLowerCase().includes(l.toLowerCase())||e.description.toLowerCase().includes(l.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(l.toLowerCase())),t=!b||e.pricing===b;return s&&t})].sort((e,s)=>{switch(v){case"popular":return(s.likes||0)-(e.likes||0);case"views":return(s.views||0)-(e.views||0);case"name":return e.name.localeCompare(s.name);case"newest":if(e.createdAt&&s.createdAt)return new Date(s.createdAt).getTime()-new Date(e.createdAt).getTime();return 0;default:return 0}});return(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[(0,a.jsx)(i(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)(i(),{href:"/categories",className:"hover:text-blue-600",children:"分类"}),(0,a.jsx)("span",{children:"/"}),(0,a.jsx)("span",{className:"text-gray-900",children:e.name})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)(i(),{href:"/categories",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"返回分类列表"]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-3xl",style:{backgroundColor:e.color},children:(0,a.jsx)("span",{className:"text-white",children:e.icon})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:e.name}),(0,a.jsxs)("p",{className:"text-lg text-gray-600",children:[e.toolCount," 个工具"]})]})]}),(0,a.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)("input",{type:"text",placeholder:"在此分类中搜索工具...",value:l,onChange:e=>h(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(d.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,a.jsx)("div",{className:"md:hidden mb-4",children:(0,a.jsxs)("button",{onClick:()=>A(!N),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,a.jsx)(m.A,{className:`ml-2 h-4 w-4 transform ${N?"rotate-180":""}`})]})}),(0,a.jsxs)("div",{className:`grid grid-cols-1 md:grid-cols-3 gap-4 ${N?"block":"hidden md:grid"}`,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,a.jsx)("select",{value:b,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:p.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,a.jsx)("select",{value:v,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:g.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,a.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,a.jsx)("button",{onClick:()=>w("grid"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${"grid"===j?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,a.jsx)(x.A,{className:"h-4 w-4 mx-auto"})}),(0,a.jsx)("button",{onClick:()=>w("list"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${"list"===j?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,a.jsx)(u.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["显示 ",C.length," 个结果",l&&` 搜索 "${l}"`]})}),C.length>0?(0,a.jsx)("div",{className:"grid"===j?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:C.map(e=>(0,a.jsx)(n.default,{tool:e},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,a.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]}),(0,a.jsxs)("section",{className:"mt-16 bg-gray-50 rounded-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"相关分类"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(i(),{href:"/categories/image-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFA8"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"图像生成"})]}),(0,a.jsxs)(i(),{href:"/categories/code-generation",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBB"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"代码生成"})]}),(0,a.jsxs)(i(),{href:"/categories/data-analysis",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"数据分析"})]}),(0,a.jsxs)(i(),{href:"/categories/audio-processing",className:"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFB5"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"音频处理"})]})]})]})]})}},93262:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u,generateMetadata:()=>m});var a=t(37413);t(61120);var r=t(39916),l=t(98964),i=t(8639),n=t(10806),o=t(36317),c=t(54290);let d=(e,s)=>{let t=(0,o.PZ)(e);return t?{_id:e,slug:e,name:t.name,description:t.description,icon:t.icon,color:t.color,toolCount:s}:{_id:e,slug:e,name:e.replace("-"," ").replace(/\b\w/g,e=>e.toUpperCase()),description:`AI tools in the ${e} category.`,icon:"\uD83E\uDD16",color:"#6B7280",toolCount:s}};async function m({params:e}){try{let s=await e,{categoryInfo:t,tools:a}=await x(s.slug);if(!t)return{title:"分类不存在 - AI工具导航",description:"您访问的AI工具分类不存在。"};let r=`${t.name} AI工具 - AI工具导航`,l=`发现最好的${t.name} AI工具。${t.description}，共${a.length}个精选工具。`,i=`${t.name},${t.name}AI工具,人工智能,AI应用,机器学习,深度学习`,n=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",o=`/categories/${t.slug}`;return{title:r,description:l,keywords:i,authors:[{name:"AI工具导航团队"}],robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"zh_CN",url:`${n}${o}`,siteName:"AI工具导航",title:r,description:l,images:[{url:`/og-category-${t.slug}.jpg`,width:1200,height:630,alt:`${t.name} AI工具`}]},twitter:{card:"summary_large_image",title:r,description:l,images:[`/og-category-${t.slug}.jpg`]},alternates:{canonical:`${n}${o}`}}}catch(e){return{title:"AI工具分类 - AI工具导航",description:"浏览AI工具分类，发现适合您需求的人工智能工具。"}}}async function x(e){try{let s=await n.u.getTools({category:e,status:"published",limit:100});if(!s.success||!s.data)return{categoryInfo:null,tools:[],error:s.error||"获取分类数据失败"};{let t=s.data.tools;return{categoryInfo:d(e,t.length),tools:t,error:null}}}catch(e){return console.error("Failed to fetch category data:",e),{categoryInfo:null,tools:[],error:"获取分类数据失败，请稍后重试"}}}async function u({params:e}){let s=await e,{categoryInfo:t,tools:n,error:o}=await x(s.slug);t||(0,r.notFound)();let d=n.length>0?(0,c.eb)(n,t.name):null,m=(0,c.hC)([{name:"首页",url:"/"},{name:"AI工具分类",url:"/categories"},{name:t.name,url:`/categories/${t.slug}`}]),u={"@context":"https://schema.org","@type":"CollectionPage",name:`${t.name} AI工具`,description:t.description,url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/categories/${t.slug}`,mainEntity:{"@type":"ItemList",name:`${t.name} AI工具列表`,numberOfItems:n.length,itemListElement:n.map((e,s)=>({"@type":"ListItem",position:s+1,item:{"@type":"SoftwareApplication",name:e.name,description:e.description,url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/tools/${e._id}`,applicationCategory:t.name,image:e.logo}}))}};return(0,a.jsxs)(l.A,{children:[d&&(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(d)}}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(m)}}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(u)}}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsx)("nav",{className:"flex","aria-label":"面包屑导航",children:(0,a.jsxs)("ol",{className:"inline-flex items-center space-x-1 md:space-x-3",children:[(0,a.jsx)("li",{className:"inline-flex items-center",children:(0,a.jsx)("a",{href:"/",className:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600",children:"首页"})}),(0,a.jsx)("li",{children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),(0,a.jsx)("a",{href:"/categories",className:"ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2",children:"AI工具分类"})]})}),(0,a.jsx)("li",{children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),(0,a.jsx)("span",{className:"ml-1 text-sm font-medium text-gray-500 md:ml-2",children:t.name})]})})]})})}),(0,a.jsx)(i.default,{categoryInfo:t,tools:n,error:o})]})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,4999,4825,2579,3876,1525,6804,1606],()=>t(80872));module.exports=a})();