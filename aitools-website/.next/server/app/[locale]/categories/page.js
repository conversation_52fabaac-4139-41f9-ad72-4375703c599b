(()=>{var e={};e.id=5534,e.ids=[5534],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5973:(e,t,s)=>{"use strict";function r(){return null}s.d(t,{default:()=>r}),s(43210)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21696:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,generateMetadata:()=>c});var r=s(37413);s(61120);var i=s(98964),o=s(23595),a=s(36317),l=s(10806),n=s(54290);async function c(){try{let e=await l.u.getCategories(),t=e.success&&e.data?e.data.categories.length:0,s=e.success&&e.data?e.data.overview.totalTools:0,r=`按功能分类浏览AI工具，共${t}个分类，收录${s}+个优质AI工具。包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。`;return{title:"AI工具分类 - 按功能浏览人工智能工具",description:r,keywords:"AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类",authors:[{name:"AI工具导航团队"}],robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"zh_CN",url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/categories`,siteName:"AI工具导航",title:"AI工具分类 - 按功能浏览人工智能工具",description:r,images:[{url:"/og-categories.jpg",width:1200,height:630,alt:"AI工具分类 - 按功能浏览人工智能工具"}]},twitter:{card:"summary_large_image",title:"AI工具分类 - 按功能浏览人工智能工具",description:r,images:["/og-categories.jpg"]},alternates:{canonical:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/categories`}}}catch(e){return{title:"AI工具分类 - 按功能浏览人工智能工具",description:"按功能分类浏览AI工具，包括文本生成、图像创作、数据分析、自动化、音频处理等各类人工智能工具分类。",keywords:"AI工具分类,人工智能分类,AI工具类别,机器学习分类,深度学习工具分类,AI应用分类,智能工具分类"}}}async function d(){try{let e=await l.u.getCategories();if(e.success&&e.data)return{categories:e.data.categories.map(e=>{let t=a.vK[e.id]||{description:"优质AI工具集合",icon:"\uD83D\uDD27",color:"#6B7280"};return{_id:e.id,name:e.name,slug:e.id,description:t.description,icon:t.icon,color:t.color,toolCount:e.count}}),error:null};return{categories:[],error:e.error||"获取分类列表失败"}}catch(e){return console.error("Failed to fetch categories:",e),{categories:[],error:"获取分类列表失败，请稍后重试"}}}async function m(){let{categories:e,error:t}=await d(),s=(0,n.hC)([{name:"首页",url:"/"},{name:"AI工具分类",url:"/categories"}]),a={"@context":"https://schema.org","@type":"ItemList",name:"AI工具分类",description:"按功能分类的AI工具目录",numberOfItems:e.length,itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,item:{"@type":"Thing",name:e.name,description:e.description,url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/categories/${e.slug}`,additionalProperty:{"@type":"PropertyValue",name:"工具数量",value:e.toolCount}}}))};return(0,r.jsxs)(i.A,{children:[(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(s)}}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(a)}}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsx)("nav",{className:"flex","aria-label":"面包屑导航",children:(0,r.jsxs)("ol",{className:"inline-flex items-center space-x-1 md:space-x-3",children:[(0,r.jsx)("li",{className:"inline-flex items-center",children:(0,r.jsx)("a",{href:"/",className:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600",children:"首页"})}),(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),(0,r.jsx)("span",{className:"ml-1 text-sm font-medium text-gray-500 md:ml-2",children:"AI工具分类"})]})})]})})}),(0,r.jsx)(o.default,{categories:e,error:t})]})}},23595:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx","default")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27370:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(65239),i=s(48088),o=s(88170),a=s.n(o),l=s(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let c={children:["",{children:["[locale]",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21696)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/[locale]/categories/page",pathname:"/[locale]/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36317:(e,t,s)=>{"use strict";s.d(t,{Bi:()=>i,PZ:()=>a,ut:()=>l,vK:()=>o});let r=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}];[...r.map(e=>({value:e.slug,label:e.name}))];let i=r.reduce((e,t)=>(e[t.slug]=t.name,e),{}),o=r.reduce((e,t)=>(e[t.slug]=t,e),{}),a=e=>o[e],l=r.map(e=>e.slug)},45847:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(60687);s(43210);var i=s(85814),o=s.n(i);let a=({category:e})=>(0,r.jsx)(o(),{href:`/categories/${e.slug}`,children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:e.color||"#3B82F6"},children:(0,r.jsx)("span",{className:"text-white",children:e.icon||"\uD83D\uDD27"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.toolCount," 个工具"]})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})})});var l=s(11011),n=s(6943),c=s(25541);function d({categories:e,error:t}){if(t)return(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(l.A,{message:t})});let s=e.sort((e,t)=>t.toolCount-e.toolCount).slice(0,6);return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)(n.A,{className:"inline-block mr-3 h-10 w-10 text-blue-600"}),"AI 工具分类"]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"按功能分类浏览我们精选的 AI 工具集合。每个分类都包含经过验证的高质量工具，帮助您快速找到所需的解决方案。"})]}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length}),(0,r.jsx)("div",{className:"text-gray-700",children:"个分类"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.reduce((e,t)=>e+t.toolCount,0)}),(0,r.jsx)("div",{className:"text-gray-700",children:"个工具"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.length>0?Math.round(e.reduce((e,t)=>e+t.toolCount,0)/e.length):0}),(0,r.jsx)("div",{className:"text-gray-700",children:"平均每分类工具数"})]})]})}),(0,r.jsxs)("section",{className:"mb-16",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"热门分类"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,r.jsx)(a,{category:e},e._id))})]}),(0,r.jsxs)("section",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"所有分类"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[e.length," 个分类"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,r.jsx)(a,{category:e},e._id))})]}),(0,r.jsxs)("section",{className:"mt-16 bg-blue-600 rounded-lg p-8 text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"没有找到您需要的分类？"}),(0,r.jsx)("p",{className:"text-blue-100 mb-6",children:"我们持续添加新的工具和分类。如果您有建议或想要提交新工具，请联系我们。"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("a",{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:"提交新工具"}),(0,r.jsx)("a",{href:"/contact",className:"inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors",children:"联系我们"})]})]})]})}},46183:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,23595)),Promise.resolve().then(s.bind(s,92588))},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),a=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:o="",children:a,iconNode:d,...m},x)=>(0,r.createElement)("svg",{ref:x,...c,width:t,height:t,stroke:e,strokeWidth:i?24*Number(s)/Number(t):s,className:l("lucide",o),...!a&&!n(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(a)?a:[a]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...o},n)=>(0,r.createElement)(d,{ref:n,iconNode:t,className:l(`lucide-${i(a(e))}`,`lucide-${e}`,s),...o}));return s.displayName=a(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69151:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,45847)),Promise.resolve().then(s.bind(s,5973))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,4825,2579,1525,1606],()=>s(27370));module.exports=r})();