(()=>{var e={};e.id=741,e.ids=[741],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21469:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,generateMetadata:()=>n});var r=s(37413);s(61120);var a=s(98964),l=s(64153),o=s(10806),i=s(54290);async function n(){try{let e=await o.u.getTools({status:"published",limit:1}),t=e.success&&e.data?e.data.pagination.totalItems:0,s=`浏览完整的AI工具目录，发现适合您需求的人工智能工具。已收录${t}+个优质AI工具，包含文本生成、图像创作、数据分析、自动化等各类AI工具。`;return{title:"AI工具目录 - 发现最好的人工智能工具",description:s,keywords:"AI工具目录,人工智能工具,AI工具列表,机器学习工具,深度学习工具,AI应用,自动化工具,智能工具",authors:[{name:"AI工具导航团队"}],robots:{index:!0,follow:!0},openGraph:{type:"website",locale:"zh_CN",url:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/tools`,siteName:"AI工具导航",title:"AI工具目录 - 发现最好的人工智能工具",description:s,images:[{url:"/og-tools.jpg",width:1200,height:630,alt:"AI工具目录 - 发现最好的人工智能工具"}]},twitter:{card:"summary_large_image",title:"AI工具目录 - 发现最好的人工智能工具",description:s,images:["/og-tools.jpg"]},alternates:{canonical:`${process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com"}/tools`}}}catch(e){return{title:"AI工具目录 - 发现最好的人工智能工具",description:"浏览完整的AI工具目录，发现适合您需求的人工智能工具。包含文本生成、图像创作、数据分析、自动化等各类AI工具。",keywords:"AI工具目录,人工智能工具,AI工具列表,机器学习工具,深度学习工具,AI应用,自动化工具,智能工具"}}}async function c(){try{let e=await o.u.getTools({status:"published",limit:100});if(e.success&&e.data)return{tools:e.data.tools,error:null};return{tools:[],error:e.error||"获取工具列表失败"}}catch(e){return console.error("Failed to fetch tools:",e),{tools:[],error:"获取工具列表失败，请稍后重试"}}}async function d(){let{tools:e,error:t}=await c(),s=e.length>0?(0,i.eb)(e):null,o=(0,i.hC)([{name:"首页",url:"/"},{name:"AI工具目录",url:"/tools"}]);return(0,r.jsxs)(a.A,{children:[s&&(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(s)}}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(o)}}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsx)("nav",{className:"flex","aria-label":"面包屑导航",children:(0,r.jsxs)("ol",{className:"inline-flex items-center space-x-1 md:space-x-3",children:[(0,r.jsx)("li",{className:"inline-flex items-center",children:(0,r.jsx)("a",{href:"/",className:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600",children:"首页"})}),(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),(0,r.jsx)("span",{className:"ml-1 text-sm font-medium text-gray-500 md:ml-2",children:"AI工具目录"})]})})]})})}),(0,r.jsx)(l.default,{initialTools:e,error:t})]})}},25366:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55183:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var r=s(60687),a=s(43210),l=s(73899),o=s(11011),i=s(94865),n=s(71123),c=s(99270),d=s(80462),u=s(78272),m=s(6943),p=s(25366);let x=n.xO,g=i.v4,h=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function b({initialTools:e,error:t}){let[s]=(0,a.useState)(e),[i,n]=(0,a.useState)(""),[b,v]=(0,a.useState)(""),[f,y]=(0,a.useState)(""),[w,j]=(0,a.useState)("popular"),[A,N]=(0,a.useState)("grid"),[k,I]=(0,a.useState)(!1),C=[...s.filter(e=>{let t=e.name.toLowerCase().includes(i.toLowerCase())||e.description.toLowerCase().includes(i.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(i.toLowerCase())),s=!b||e.category===b,r=!f||e.pricing===f;return t&&s&&r})].sort((e,t)=>{switch(w){case"popular":return(t.likes||0)-(e.likes||0);case"views":return(t.views||0)-(e.views||0);case"name":return e.name.localeCompare(t.name);case"newest":if(e.launchDate&&t.launchDate)return new Date(t.launchDate).getTime()-new Date(e.launchDate).getTime();return new Date(t.createdAt||"").getTime()-new Date(e.createdAt||"").getTime();default:return 0}});return t?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(o.A,{message:t})}):(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"AI 工具目录"}),(0,r.jsxs)("p",{className:"text-lg text-gray-600",children:["发现 ",s.length," 个精选的 AI 工具，提升您的工作效率"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或标签...",value:i,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(c.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,r.jsx)("div",{className:"md:hidden mb-4",children:(0,r.jsxs)("button",{onClick:()=>I(!k),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,r.jsx)(u.A,{className:`ml-2 h-4 w-4 transform ${k?"rotate-180":""}`})]})}),(0,r.jsxs)("div",{className:`grid grid-cols-1 md:grid-cols-4 gap-4 ${k?"block":"hidden md:grid"}`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"分类"}),(0,r.jsx)("select",{value:b,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:x.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,r.jsx)("select",{value:f,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:g.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,r.jsx)("select",{value:w,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:h.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,r.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,r.jsx)("button",{onClick:()=>N("grid"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${"grid"===A?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(m.A,{className:"h-4 w-4 mx-auto"})}),(0,r.jsx)("button",{onClick:()=>N("list"),className:`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${"list"===A?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"}`,children:(0,r.jsx)(p.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["显示 ",C.length," 个结果",i&&` 搜索 "${i}"`,b&&` 在 "${x.find(e=>e.value===b)?.label}"`]})}),C.length>0?(0,r.jsx)("div",{className:"grid"===A?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:C.map(e=>(0,r.jsx)(l.default,{tool:e},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(c.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64153:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx","default")},64196:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),l=s(88170),o=s.n(l),i=s(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let c={children:["",{children:["[locale]",{children:["tools",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21469)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/tools/page",pathname:"/[locale]/tools",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},71123:(e,t,s)=>{"use strict";s.d(t,{Bi:()=>o,ch:()=>a,xO:()=>l});let r=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],a=r.map(e=>({value:e.slug,label:e.name})),l=[{value:"",label:"所有分类"},...a],o=r.reduce((e,t)=>(e[t.slug]=t.name,e),{});r.reduce((e,t)=>(e[t.slug]=t,e),{}),r.map(e=>e.slug)},78272:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79195:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,92588)),Promise.resolve().then(s.bind(s,64153))},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},89811:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,5973)),Promise.resolve().then(s.bind(s,55183))},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,4825,2579,3876,1525,6804,1606],()=>s(64196));module.exports=r})();