(()=>{var e={};e.id=6249,e.ids=[6249],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,s)=>{let{createProxy:r}=s(39844);e.exports=r("/Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/client/app-dir/link.js")},5973:(e,t,s)=>{"use strict";function r(){return null}s.d(t,{default:()=>r}),s(43210)},7485:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(60687);s(43210);var a=s(82136),n=s(23877),o=s(2328);function i({toolId:e,initialLikes:t=0,initialLiked:s=!1,onLoginRequired:i,onUnlike:l,isInLikedPage:c=!1,showCount:d=!0,size:m="md"}){let{data:u}=(0,a.useSession)(),{getToolState:p,initializeToolState:h,toggleLike:x}=(0,o.X)(),g=p(e),f=async()=>{if(!u)return void i?.();if(g.loading)return;let t=g.liked;await x(e,c)&&c&&t&&l&&l(e)},y=(()=>{switch(m){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,r.jsxs)("button",{onClick:f,disabled:g.loading,className:`
        ${y.button}
        inline-flex items-center space-x-1
        ${g.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"}
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
      `,title:g.liked?"取消点赞":"点赞",children:[g.loading?(0,r.jsx)("div",{className:`${y.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`}):g.liked?(0,r.jsx)(n.Mbv,{className:y.icon}):(0,r.jsx)(n.sOK,{className:y.icon}),d&&(0,r.jsx)("span",{className:`${y.text} font-medium`,children:g.likes})]})}},10806:(e,t,s)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function n(){return"production"}function o(){return"development"===n()}s.d(t,{u:()=>d});let i={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:n(),isDevelopment:o(),isProduction:"production"===n(),port:process.env.PORT||"3001"};o()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port));let l=a();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let s=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),n=await a.json();if(!a.ok)throw Error(n.error||`HTTP error! status: ${a.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/tools${s?`?${s}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/user/liked-tools${s?`?${s}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/admin/tools${s?`?${s}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}}let d=new c},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15872:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,5973)),Promise.resolve().then(s.bind(s,95437))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29024:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,92588)),Promise.resolve().then(s.bind(s,76106))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},54290:(e,t,s)=>{"use strict";s.d(t,{B3:()=>a,L2:()=>n,eb:()=>l,hC:()=>i,sU:()=>o});let r=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com";function a(){return{"@context":"https://schema.org","@type":"WebSite",name:"AI工具导航",description:"发现最好的AI工具，提升您的工作效率和创造力",url:r,potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${r}/tools?search={search_term_string}`},"query-input":"required name=search_term_string"},publisher:{"@type":"Organization",name:"AI工具导航",url:r}}}function n(){return{"@context":"https://schema.org","@type":"Organization",name:"AI工具导航",description:"专业的AI工具发现和推荐平台",url:r,logo:`${r}/logo.png`,sameAs:[]}}function o(e){return{"@context":"https://schema.org","@type":"SoftwareApplication",name:e.name,description:e.description,url:e.website,applicationCategory:"AI工具",operatingSystem:"Web",offers:{"@type":"Offer",price:"free"===e.pricing?"0":void 0,priceCurrency:"USD",availability:"https://schema.org/InStock"},aggregateRating:e.likes?{"@type":"AggregateRating",ratingValue:Math.min(5,Math.max(1,e.likes/10+3)),reviewCount:e.likes,bestRating:5,worstRating:1}:void 0,image:e.logo||`${r}/default-tool-image.jpg`,datePublished:e.launchDate,publisher:{"@type":"Organization",name:"AI工具导航",url:r}}}function i(e){return{"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:`${r}${e.url}`}))}}function l(e,t){return{"@context":"https://schema.org","@type":"ItemList",name:t?`${t} AI工具`:"AI工具列表",description:t?`发现最好的${t} AI工具`:"发现最好的AI工具",numberOfItems:e.length,itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,item:{"@type":"SoftwareApplication",name:e.name,description:e.description,url:`${r}/tools/${e._id}`,image:e.logo||`${r}/default-tool-image.jpg`}}))}}},56976:(e,t,s)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function n(){return"production"}function o(){return"development"===n()}s.d(t,{u:()=>d});let i={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:n(),isDevelopment:o(),isProduction:"production"===n(),port:process.env.PORT||"3001"};o()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port));let l=a();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let s=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),n=await a.json();if(!a.ok)throw Error(n.error||`HTTP error! status: ${a.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/tools${s?`?${s}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/user/liked-tools${s?`?${s}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/admin/tools${s?`?${s}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}}let d=new c},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:n="",children:o,iconNode:d,...m},u)=>(0,r.createElement)("svg",{ref:u,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:i("lucide",n),...!o&&!l(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]])),m=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...n},l)=>(0,r.createElement)(d,{ref:l,iconNode:t,className:i(`lucide-${a(o(e))}`,`lucide-${e}`,s),...n}));return s.displayName=o(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63687:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,generateMetadata:()=>m});var r=s(37413);s(61120);var a=s(4536),n=s.n(a),o=s(39916),i=s(98964),l=s(76106),c=s(10806),d=s(54290);async function m({params:e}){try{let{id:t}=await e,s=await c.u.getTool(t);if(!s.success||!s.data)return{title:"工具不存在 - AI工具导航",description:"您访问的AI工具不存在或已被删除。"};let r=s.data,a=`${r.name} - AI工具导航`,n=r.description||`${r.name}是一个优秀的AI工具，帮助您提升工作效率。`,o=[r.name,...r.tags||[],"AI工具",r.category].join(", "),i=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",l=`/tools/${r._id}`,d=r.logo||"/og-tool-default.jpg";return{title:a,description:n,keywords:o,authors:[{name:"AI工具导航团队"}],robots:{index:!0,follow:!0},openGraph:{type:"article",locale:"zh_CN",url:`${i}${l}`,siteName:"AI工具导航",title:a,description:n,images:[{url:d.startsWith("http")?d:`${i}${d}`,width:1200,height:630,alt:`${r.name} - AI工具`}],publishedTime:r.launchDate?new Date(r.launchDate).toISOString():void 0,modifiedTime:r.updatedAt?new Date(r.updatedAt).toISOString():void 0},twitter:{card:"summary_large_image",title:a,description:n,images:[d.startsWith("http")?d:`${i}${d}`]},alternates:{canonical:`${i}${l}`}}}catch(e){return{title:"工具详情 - AI工具导航",description:"查看AI工具的详细信息和使用指南。"}}}async function u({params:e}){try{let{id:t}=await e,s=await c.u.getTool(t);s.success&&s.data||(0,o.notFound)();let a=s.data;"approved"===a.status&&a.launchDate&&new Date(a.launchDate)<=new Date||(0,o.notFound)();let m=(0,d.sU)(a),u=(0,d.hC)([{name:"首页",url:"/"},{name:"工具目录",url:"/tools"},{name:a.name,url:`/tools/${a._id}`}]);return(0,r.jsxs)(i.A,{children:[(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(m)}}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(u)}}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6","aria-label":"面包屑导航",children:[(0,r.jsx)(n(),{href:"/",className:"hover:text-blue-600",children:"首页"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)(n(),{href:"/tools",className:"hover:text-blue-600",children:"工具目录"}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900",children:a.name})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(n(),{href:"/tools",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,r.jsx)("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"返回工具目录"]})}),(0,r.jsx)(l.default,{initialTool:a,toolId:t})]})]})}catch(e){console.error("Error loading tool:",e),(0,o.notFound)()}}},67760:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},76106:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},86442:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),o=s.n(n),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c={children:["",{children:["[locale]",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,63687)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/tools/[id]/page",pathname:"/[locale]/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92588:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx","default");(0,r.registerClientReference)(function(){throw Error("Attempted to call PerformanceOptimizations() from the server but PerformanceOptimizations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx","PerformanceOptimizations")},94865:(e,t,s)=>{"use strict";s.d(t,{$g:()=>d,Ef:()=>l,Y$:()=>i,kX:()=>r,mV:()=>c,tF:()=>m,v4:()=>o,vS:()=>a});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:r.FREE_LAUNCH.description,price:r.FREE_LAUNCH.displayPrice,features:r.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:r.PRIORITY_LAUNCH.description,price:r.PRIORITY_LAUNCH.displayPrice,features:r.PRIORITY_LAUNCH.features,recommended:!0}],n={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},o=[{value:"",label:"所有价格"},{value:n.FREE.value,label:n.FREE.label},{value:n.FREEMIUM.value,label:n.FREEMIUM.label},{value:n.PAID.value,label:n.PAID.label}],i=[{value:n.FREE.value,label:n.FREE.label},{value:n.FREEMIUM.value,label:n.FREEMIUM.label},{value:n.PAID.value,label:n.PAID.label}],l=e=>{switch(e){case n.FREE.value:return n.FREE.color;case n.FREEMIUM.value:return n.FREEMIUM.color;case n.PAID.value:return n.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case n.FREE.value:return n.FREE.label;case n.FREEMIUM.value:return n.FREEMIUM.label;case n.PAID.value:return n.PAID.label;default:return e}},d=e=>0===e?"免费":`\xa5${e}`,m=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)},95437:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var r=s(60687),a=s(43210),n=s(85814),o=s.n(n),i=s(7485),l=s(82136),c=s(23877);function d({toolId:e,onLoginRequired:t}){let{data:s}=(0,l.useSession)(),[n,o]=(0,a.useState)([]),[i,d]=(0,a.useState)(""),[m,u]=(0,a.useState)(null),[p,h]=(0,a.useState)(""),[x,g]=(0,a.useState)(!1),[f,y]=(0,a.useState)(!1),b=async()=>{g(!0);try{let t=await fetch(`/api/tools/${e}/comments`);if(t.ok){let e=await t.json();e.success&&o(e.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{g(!1)}},v=async()=>{if(!s)return void t?.();if(i.trim()){y(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:i.trim()})});if(t.ok)(await t.json()).success&&(d(""),b());else{let e=await t.json();console.error("Comment submission failed:",e.message)}}catch(e){console.error("Comment submission error:",e)}finally{y(!1)}}},j=async r=>{if(!s)return void t?.();if(p.trim()){y(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:p.trim(),parentId:r})});if(t.ok)(await t.json()).success&&(h(""),u(null),b());else{let e=await t.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{y(!1)}}},N=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/36e5);return s<1?"刚刚":s<24?`${s}小时前`:s<168?`${Math.floor(s/24)}天前`:t.toLocaleDateString("zh-CN")},w=({comment:e,isReply:t=!1})=>(0,r.jsx)("div",{className:`${t?"ml-8 border-l-2 border-gray-100 pl-4":""}`,children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:e.userId.image?(0,r.jsx)("img",{src:e.userId.image,alt:e.userId.name,className:"w-8 h-8 rounded-full"}):(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(c.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.userId.name}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:N(e.createdAt)})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-2",children:e.content}),(0,r.jsx)("div",{className:"flex items-center gap-4",children:!t&&(0,r.jsxs)("button",{onClick:()=>u(m===e._id?null:e._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,r.jsx)(c.w1Z,{className:"w-3 h-3"}),"回复"]})}),m===e._id&&(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("textarea",{value:p,onChange:e=>h(e.target.value),placeholder:"写下你的回复...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,r.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,r.jsx)("button",{onClick:()=>{u(null),h("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,r.jsx)("button",{onClick:()=>j(e._id),disabled:f||!p.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"发送中...":"发送"})]})]}),e.replies&&e.replies.length>0&&(0,r.jsx)("div",{className:"mt-4 space-y-4",children:e.replies.map(e=>(0,r.jsx)(w,{comment:e,isReply:!0},e._id))})]})]})});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["评论 (",n.length,")"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:i,onChange:e=>d(e.target.value),placeholder:s?"写下你的评论...":"请先登录后评论",className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!s}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[i.length,"/1000"]}),(0,r.jsx)("button",{onClick:v,disabled:f||!i.trim()||!s,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"发送中...":"发表评论"})]})]}),x?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"text-gray-500 mt-2",children:"加载评论中..."})]}):0===n.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:"暂无评论，来发表第一条评论吧！"})}):(0,r.jsx)("div",{className:"space-y-6",children:n.map(e=>(0,r.jsx)(w,{comment:e},e._id))})]})}var m=s(48577);s(56976);var u=s(94865),p=s(23928),h=s(13861),x=s(67760);let g=(0,s(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var f=s(37360),y=s(25334);function b({initialTool:e,toolId:t}){let[s,n]=(0,a.useState)(e),[l,c]=(0,a.useState)([]),[b,v]=(0,a.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsxs)("header",{className:"flex items-start justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[s.logo?(0,r.jsx)("img",{src:s.logo,alt:`${s.name} logo`,className:"w-16 h-16 rounded-lg object-cover"}):(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:s.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:s.name}),s.tagline&&(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:s.tagline}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${(0,u.Ef)(s.pricing)}`,children:[(0,r.jsx)(p.A,{className:"mr-1 h-4 w-4"}),(0,u.mV)(s.pricing)]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[s.views||0," 浏览"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[s.likes||0," 喜欢"]})]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{toolId:s._id,initialLikes:s.likes,onLoginRequired:()=>v(!0)}),(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,r.jsx)(g,{className:"h-5 w-5"})})]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:s.description})}),s.tags&&s.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:s.tags.map((e,t)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,r.jsx)(f.A,{className:"mr-1 h-3 w-3"}),e]},t))}),(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-4",children:(0,r.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(y.A,{className:"mr-2 h-5 w-5"}),"访问 ",s.name]})})]})}),(0,r.jsxs)("aside",{className:"lg:col-span-1",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"工具信息"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"分类"}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:s.category})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"价格模式"}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm font-medium ${(0,u.Ef)(s.pricing)}`,children:(0,u.mV)(s.pricing)})]}),s.launchDate&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"发布日期"}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:new Date(s.launchDate).toLocaleDateString("zh-CN")})]})]})]}),l.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"相关工具"}),(0,r.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,r.jsx)("div",{children:(0,r.jsx)(o(),{href:`/tools/${e._id}`,className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,r.jsx)("img",{src:e.logo,alt:e.name,className:"w-10 h-10 rounded object-cover"}):(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:e.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded ${(0,u.Ef)(e.pricing)}`,children:(0,u.mV)(e.pricing)}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{children:[e.views||0," 浏览"]}),(0,r.jsxs)("span",{children:[e.likes||0," 喜欢"]})]})]})]})]})})},e._id))})]})]})]}),(0,r.jsx)("div",{className:"mt-12",children:(0,r.jsx)(d,{toolId:s._id,onLoginRequired:()=>v(!0)})}),(0,r.jsx)(m.A,{isOpen:b,onClose:()=>v(!1)})]})}},98964:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(37413);s(61120);var a=s(4536),n=s.n(a),o=s(92588);let i=({children:e})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(o.default,{}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,4825,2579,1525],()=>s(86442));module.exports=r})();