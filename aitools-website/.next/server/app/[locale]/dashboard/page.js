(()=>{var e={};e.id=758,e.ids=[758],e.modules={2716:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),o=t(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let c={children:["",{children:["[locale]",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73010)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/dashboard/page",pathname:"/[locale]/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21281:(e,s,t)=>{Promise.resolve().then(t.bind(t,43660))},25334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43660:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(60687),a=t(43210),n=t(85814),l=t.n(n),o=t(98402),i=t(33823),c=t(11011);t(56976),t(71123);var d=t(5336),p=t(48730),m=t(35071),x=t(96474),h=t(53411),u=t(13861),g=t(40228),v=t(25334),b=t(63143);let j=e=>{switch(e){case"published":return"bg-green-100 text-green-800";case"approved":return"bg-blue-100 text-blue-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},y=e=>{switch(e){case"published":return"已发布";case"approved":return"已通过";case"pending":return"审核中";case"rejected":return"已拒绝";case"draft":return"草稿";default:return e}},f=e=>{switch(e){case"published":case"approved":return(0,r.jsx)(d.A,{className:"h-4 w-4"});case"pending":case"draft":return(0,r.jsx)(p.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(m.A,{className:"h-4 w-4"});default:return null}};function N(){let[e,s]=(0,a.useState)("all"),[t,n]=(0,a.useState)([]),[p,m]=(0,a.useState)(!0),[N,w]=(0,a.useState)(""),A=t.filter(s=>"all"===e||s.status===e),k={total:t.length,approved:t.filter(e=>"approved"===e.status).length,pending:t.filter(e=>"pending"===e.status).length,rejected:t.filter(e=>"rejected"===e.status).length,totalViews:t.reduce((e,s)=>e+s.views,0),totalLikes:t.reduce((e,s)=>e+s.likes,0)};return p?(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.A,{size:"lg",className:"py-20"})})}):(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"开发者仪表板"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"管理您提交的 AI 工具"})]}),(0,r.jsxs)(l(),{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(x.A,{className:"mr-2 h-5 w-5"}),"提交新工具"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总提交数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(d.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k.approved})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总点赞数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:k.totalLikes})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>s("all"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"all"===e?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["全部 (",k.total,")"]}),(0,r.jsxs)("button",{onClick:()=>s("approved"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"approved"===e?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["已通过 (",k.approved,")"]}),(0,r.jsxs)("button",{onClick:()=>s("pending"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"pending"===e?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["审核中 (",k.pending,")"]}),(0,r.jsxs)("button",{onClick:()=>s("rejected"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"rejected"===e?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["已拒绝 (",k.rejected,")"]})]})}),N&&(0,r.jsx)(c.A,{message:N,onClose:()=>w(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:A.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:A.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${j(e.status)}`,children:[f(e.status),(0,r.jsx)("span",{className:"ml-1",children:y(e.status)})]})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["提交于 ",new Date(e.submittedAt).toLocaleDateString("zh-CN")]})]}),e.launchDate&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["发布于 ",new Date(e.launchDate).toLocaleDateString("zh-CN")]})]}),"approved"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views," 浏览"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"❤️"}),(0,r.jsxs)("span",{children:[e.likes," 点赞"]})]})]})]}),"rejected"===e.status&&e.reviewNotes&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-red-800",children:[(0,r.jsx)("strong",{children:"拒绝原因："})," ",e.reviewNotes]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,r.jsx)(l(),{href:`/tools/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"查看详情",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:"访问网站",children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),("rejected"===e.status||"pending"===e.status)&&(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"编辑",children:(0,r.jsx)(b.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(h.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===e?"还没有提交任何工具":`没有${y(e)}的工具`}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===e?"开始提交您的第一个 AI 工具吧！":"尝试选择其他状态查看工具"}),"all"===e&&(0,r.jsxs)(l(),{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"提交工具"]})]})})]})})}},53411:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},56976:(e,s,t)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function n(){return"production"}function l(){return"development"===n()}t.d(s,{u:()=>d});let o={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:n(),isDevelopment:l(),isProduction:"production"===n(),port:process.env.PORT||"3001"};l()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",o.baseUrl),console.log("  API Base URL:",o.apiBaseUrl),console.log("  NextAuth URL:",o.nextAuthUrl),console.log("  Environment:",o.environment),console.log("  Port:",o.port));let i=a();class c{constructor(e=i){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),n=await a.json();if(!a.ok)throw Error(n.error||`HTTP error! status: ${a.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}}let d=new c},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},71123:(e,s,t)=>{"use strict";t.d(s,{Bi:()=>l,ch:()=>a,xO:()=>n});let r=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],a=r.map(e=>({value:e.slug,label:e.name})),n=[{value:"",label:"所有分类"},...a],l=r.reduce((e,s)=>(e[s.slug]=s.name,e),{});r.reduce((e,s)=>(e[s.slug]=s,e),{}),r.map(e=>e.slug)},73010:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},84833:(e,s,t)=>{Promise.resolve().then(t.bind(t,73010))},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,4825,2579,1525,1226],()=>t(2716));module.exports=r})();