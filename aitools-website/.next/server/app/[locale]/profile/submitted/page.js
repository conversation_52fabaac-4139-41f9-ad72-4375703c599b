(()=>{var e={};e.id=1569,e.ids=[1569],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12868:(e,s,t)=>{Promise.resolve().then(t.bind(t,42247))},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20856:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let c={children:["",{children:["[locale]",{children:["profile",{children:["submitted",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52549)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/profile/submitted/page",pathname:"/[locale]/profile/submitted",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},25334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42247:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var r=t(60687),a=t(43210),l=t(82136),n=t(16189),i=t(85814),d=t.n(i),c=t(98402),o=t(33823),x=t(11011),m=t(5336),h=t(48730),p=t(35071),u=t(63143),g=t(28559),b=t(96474),j=t(53411),f=t(13861),v=t(40228),y=t(25334);let N=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"approved":return"已通过";case"pending":return"审核中";case"rejected":return"已拒绝";case"draft":return"草稿";default:return e}},k=e=>{switch(e){case"approved":return(0,r.jsx)(m.A,{className:"h-4 w-4"});case"pending":return(0,r.jsx)(h.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(p.A,{className:"h-4 w-4"});case"draft":return(0,r.jsx)(u.A,{className:"h-4 w-4"});default:return null}};function A(){let{data:e,status:s}=(0,l.useSession)();(0,n.useRouter)();let[t,i]=(0,a.useState)("all"),[h,p]=(0,a.useState)([]),[A,_]=(0,a.useState)(!0),[D,M]=(0,a.useState)(""),P=h.filter(e=>"all"===t||e.status===t),C={total:h.length,draft:h.filter(e=>"draft"===e.status).length,approved:h.filter(e=>"approved"===e.status).length,pending:h.filter(e=>"pending"===e.status).length,rejected:h.filter(e=>"rejected"===e.status).length,totalViews:h.reduce((e,s)=>e+s.views,0),totalLikes:h.reduce((e,s)=>e+s.likes,0)};return"loading"===s||A?(0,r.jsx)(c.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(o.A,{size:"lg",className:"py-20"})})}):e?(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(d(),{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"我提交的AI工具"})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"管理您提交的所有AI工具"})]}),(0,r.jsxs)(d(),{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(b.A,{className:"mr-2 h-5 w-5"}),"提交新工具"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(j.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总提交数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.approved})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(f.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总点赞数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:C.totalLikes})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>i("all"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"all"===t?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["全部 (",C.total,")"]}),(0,r.jsxs)("button",{onClick:()=>i("draft"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"draft"===t?"bg-gray-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["草稿 (",C.draft,")"]}),(0,r.jsxs)("button",{onClick:()=>i("approved"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"approved"===t?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["已通过 (",C.approved,")"]}),(0,r.jsxs)("button",{onClick:()=>i("pending"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"pending"===t?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["审核中 (",C.pending,")"]}),(0,r.jsxs)("button",{onClick:()=>i("rejected"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"rejected"===t?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["已拒绝 (",C.rejected,")"]})]})}),D&&(0,r.jsx)(x.A,{message:D,onClose:()=>M(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:P.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:P.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${N(e.status)}`,children:[k(e.status),(0,r.jsx)("span",{className:"ml-1",children:w(e.status)})]})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["提交于 ",new Date(e.submittedAt).toLocaleDateString("zh-CN")]})]}),e.launchDate&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["发布于 ",new Date(e.launchDate).toLocaleDateString("zh-CN")]})]}),"approved"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views," 浏览"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"❤️"}),(0,r.jsxs)("span",{children:[e.likes," 点赞"]})]})]})]}),"rejected"===e.status&&e.reviewNotes&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-red-800",children:[(0,r.jsx)("strong",{children:"拒绝原因："})," ",e.reviewNotes]})}),"draft"===e.status&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsxs)("p",{className:"text-sm text-blue-800 mb-2",children:[(0,r.jsx)("strong",{children:"下一步："})," 选择发布日期"]}),(0,r.jsx)(d(),{href:`/submit/launch-date/${e._id}`,className:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors",children:"选择发布日期"})]}),"pending"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"发布选项："})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"}`,children:"paid"===e.launchOption?"优先发布":"免费发布"})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"计划发布："})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh-CN")})]}),e.paymentRequired&&(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"支付状态："})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"completed"===e.paymentStatus?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"completed"===e.paymentStatus?"已支付":"待支付"})]}),(0,r.jsx)("div",{className:"flex justify-end mt-2",children:(0,r.jsxs)(d(),{href:`/submit/edit-launch-date/${e._id}`,className:"inline-flex items-center px-3 py-1 bg-yellow-600 text-white text-xs rounded-lg hover:bg-yellow-700 transition-colors",children:[(0,r.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"修改发布日期"]})})]})}),"approved"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-green-800",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"发布选项："})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"}`,children:"paid"===e.launchOption?"优先发布":"免费发布"})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:"发布日期："})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh-CN")})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,r.jsx)(d(),{href:`/tools/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"查看详情",children:(0,r.jsx)(f.A,{className:"h-5 w-5"})}),"draft"===e.status&&!e.launchDateSelected&&(0,r.jsx)(d(),{href:`/submit/launch-date/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"设定发布日期",children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),["pending","approved"].includes(e.status)&&e.launchDateSelected&&(0,r.jsx)(d(),{href:`/submit/edit-launch-date/${e._id}`,className:"p-2 text-gray-400 hover:text-orange-600 transition-colors",title:"修改发布日期",children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:"访问网站",children:(0,r.jsx)(y.A,{className:"h-5 w-5"})}),["draft","pending","rejected","approved","published"].includes(e.status)&&(0,r.jsx)(d(),{href:`/submit/edit/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"approved"===e.status&&e.launchDate&&new Date(e.launchDate)<=new Date?"编辑基础信息":"approved"===e.status?"编辑基础信息（不可修改URL）":"编辑工具信息",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(j.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===t?"还没有提交任何工具":`没有${w(t)}的工具`}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===t?"开始提交您的第一个 AI 工具吧！":"尝试选择其他状态查看工具"}),"all"===t&&(0,r.jsxs)(d(),{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"提交工具"]})]})})]})}):null}},52549:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx","default")},53411:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},79551:e=>{"use strict";e.exports=require("url")},88900:(e,s,t)=>{Promise.resolve().then(t.bind(t,52549))},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,4825,2579,1525,1226],()=>t(20856));module.exports=r})();