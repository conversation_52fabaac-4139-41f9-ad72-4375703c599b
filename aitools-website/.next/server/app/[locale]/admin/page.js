(()=>{var e={};e.id=6089,e.ids=[6089],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11597:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx","default")},17022:(e,t,s)=>{Promise.resolve().then(s.bind(s,11597))},17928:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c={children:["",{children:["[locale]",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,11597)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/admin/page",pathname:"/[locale]/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44987:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(60687),a=s(43210),n=s(16189),l=s(98402),i=s(33823),o=s(11011),c=s(78890),d=s(56976),x=s(48730),m=s(5336),u=s(35071),p=s(99891),h=s(99270),g=s(80462),v=s(43649),y=s(58869),f=s(40228),b=s(25334);let j={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},N={free:"免费",freemium:"免费增值",paid:"付费"};function w(){let e=(0,n.useRouter)(),[t,s]=(0,a.useState)([]),[w,A]=(0,a.useState)(!0),[k,P]=(0,a.useState)(""),[_,U]=(0,a.useState)(""),[L,C]=(0,a.useState)(""),[T,S]=(0,a.useState)("pending"),[R,E]=(0,a.useState)(null),[q,$]=(0,a.useState)(!1),[I,M]=(0,a.useState)(""),[O,B]=(0,a.useState)(!1),D=async()=>{try{A(!0),P("");let e=await d.u.getAdminTools({status:"all"===T?void 0:T,limit:50});e.success&&e.data?s(e.data.tools):P(e.error||"获取工具列表失败")}catch(e){P("网络错误，请重试")}finally{A(!1)}},z=t.filter(e=>{let t=e.name.toLowerCase().includes(L.toLowerCase())||e.description.toLowerCase().includes(L.toLowerCase()),s="all"===T||e.status===T;return t&&s}),X=async e=>{try{B(!0),P("");let t=await d.u.approveTool(e,{reviewedBy:"admin",reviewNotes:"审核通过",launchDate:new Date().toISOString()});t.success?(U("工具审核通过！"),await D()):P(t.error||"审核操作失败")}catch(e){P("网络错误，请重试")}finally{B(!1)}},G=async(e,t)=>{try{B(!0),P("");let s=await d.u.rejectTool(e,{reviewedBy:"admin",rejectReason:t});s.success?(U("工具已拒绝！"),await D(),$(!1),M(""),E(null)):P(s.error||"拒绝操作失败")}catch(e){P("网络错误，请重试")}finally{B(!1)}},H=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),J=e=>{switch(e){case"pending":return(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"待审核"]});case"approved":return(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,r.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"已批准"]});case"rejected":return(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,r.jsx)(u.A,{className:"w-3 h-3 mr-1"}),"已拒绝"]});default:return null}};return w?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.A,{size:"lg",className:"py-20"})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-3 h-8 w-8 text-blue-600"}),"管理员审核中心"]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"审核和管理用户提交的 AI 工具"})]}),(0,r.jsxs)("div",{className:"flex space-x-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:t.filter(e=>"pending"===e.status).length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"待审核"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.filter(e=>"approved"===e.status).length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"已批准"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:t.filter(e=>"rejected"===e.status).length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"已拒绝"})]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或提交者...",value:L,onChange:e=>C(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,r.jsx)("div",{className:"sm:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,r.jsxs)("select",{value:T,onChange:e=>S(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none",children:[(0,r.jsx)("option",{value:"all",children:"所有状态"}),(0,r.jsx)("option",{value:"pending",children:"待审核"}),(0,r.jsx)("option",{value:"approved",children:"已批准"}),(0,r.jsx)("option",{value:"rejected",children:"已拒绝"})]})]})})]})}),_&&(0,r.jsx)(c.A,{message:_,onClose:()=>U(""),className:"mb-6"}),k&&(0,r.jsx)(o.A,{message:k,onClose:()=>P(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===z.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(v.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"没有找到工具"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:L||"all"!==T?"尝试调整搜索条件或筛选器":"暂无待审核的工具"})]}):(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:z.map(t=>(0,r.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("img",{src:t.logo,alt:t.name,className:"w-12 h-12 rounded-lg object-cover border border-gray-200"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 truncate",children:t.name}),J(t.status),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:j[t.category]}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:N[t.pricing]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:t.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 mr-1"}),t.submittedBy]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 mr-1"}),H(t.submittedAt)]}),t.launchDate&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-1"}),"已发布: ",new Date(t.launchDate).toLocaleDateString("zh-CN")]})]}),t.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-1 mt-3",children:t.tags.map((e,t)=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700",children:e},t))})]})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,r.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"访问网站",children:(0,r.jsx)(b.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>e.push(`/admin/tools/${t._id}`),className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"查看详情",children:"查看详情"}),"pending"===t.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>X(t._id),disabled:O,className:"px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:O?"处理中...":"批准"}),(0,r.jsx)("button",{onClick:()=>{E(t._id),$(!0)},disabled:O,className:"px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"拒绝"})]})]})]})},t._id))})}),q&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请说明拒绝的原因，这将帮助提交者改进他们的提交。"}),(0,r.jsx)("textarea",{value:I,onChange:e=>M(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入拒绝原因..."}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>{$(!1),M(""),E(null)},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",children:"取消"}),(0,r.jsx)("button",{onClick:()=>R&&G(R,I),disabled:!I.trim()||O,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:O?"处理中...":"确认拒绝"})]})]})})]})})}},56976:(e,t,s)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function n(){return"production"}function l(){return"development"===n()}s.d(t,{u:()=>d});let i={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:n(),isDevelopment:l(),isProduction:"production"===n(),port:process.env.PORT||"3001"};l()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port));let o=a();class c{constructor(e=o){this.baseURL=e}async request(e,t={}){try{let s=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),n=await a.json();if(!a.ok)throw Error(n.error||`HTTP error! status: ${a.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/tools${s?`?${s}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/user/liked-tools${s?`?${s}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/admin/tools${s?`?${s}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}}let d=new c},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78890:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(60687),a=s(5336),n=s(11860);function l({message:e,onClose:t,className:s=""}){return(0,r.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${s}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:e})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(n.A,{className:"w-4 h-4"})})]})})}},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},85478:(e,t,s)=>{Promise.resolve().then(s.bind(s,44987))},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,4825,2579,1525,1226],()=>s(17928));module.exports=r})();