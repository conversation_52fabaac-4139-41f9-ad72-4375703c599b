(()=>{var e={};e.id=9713,e.ids=[9713],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3650:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let d={children:["",{children:["[locale]",{children:["admin",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,56891)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/admin/tools/[id]/page",pathname:"/[locale]/admin/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22234:(e,s,t)=>{Promise.resolve().then(t.bind(t,56891))},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},40162:(e,s,t)=>{Promise.resolve().then(t.bind(t,84488))},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},56891:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx","default")},56976:(e,s,t)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function l(){return"production"}function n(){return"development"===l()}t.d(s,{u:()=>c});let i={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:l(),isDevelopment:n(),isProduction:"production"===l(),port:process.env.PORT||"3001"};n()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port));let o=a();class d{constructor(e=o){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}}let c=new d},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78890:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(60687),a=t(5336),l=t(11860);function n({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},79551:e=>{"use strict";e.exports=require("url")},84488:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var r=t(60687),a=t(43210),l=t(16189),n=t(98402),i=t(33823),o=t(11011),d=t(78890),c=t(56976),m=t(5336),x=t(48730),h=t(35071),u=t(43649),p=t(28559),g=t(23928);let y=(0,t(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var b=t(25334),v=t(37360),j=t(58869),f=t(40228);let N={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},w={free:"免费",freemium:"免费增值",paid:"付费"};function A(){(0,l.useParams)();let e=(0,l.useRouter)(),[s,t]=(0,a.useState)(null),[A,k]=(0,a.useState)(!0),[P,_]=(0,a.useState)(""),[U,L]=(0,a.useState)(""),[T,R]=(0,a.useState)(!1),[S,C]=(0,a.useState)(""),[q,E]=(0,a.useState)(!1),D=async e=>{try{k(!0),_("");let s=await c.u.getTool(e);s.success&&s.data?t(s.data):_(s.error||"获取工具详情失败")}catch(e){_("网络错误，请重试")}finally{k(!1)}},$=async()=>{if(s){E(!0);try{_("");let e=await c.u.approveTool(s._id,{reviewedBy:"admin",reviewNotes:"审核通过",launchDate:new Date().toISOString()});e.success?(L("工具审核通过！"),await D(s._id)):_(e.error||"审核操作失败")}catch(e){_("网络错误，请重试")}finally{E(!1)}}},M=async()=>{if(s){if(!S.trim())return void _("请输入拒绝原因");E(!0);try{_("");let e=await c.u.rejectTool(s._id,{reviewedBy:"admin",rejectReason:S});e.success?(L("工具已拒绝！"),R(!1),C(""),await D(s._id)):_(e.error||"拒绝操作失败")}catch(e){_("网络错误，请重试")}finally{E(!1)}}};return A?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.A,{size:"lg",className:"py-20"})})}):P||!s?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(u.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:P||"工具不存在"}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("button",{onClick:()=>e.push("/admin"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"返回管理页面"]})})]})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[U&&(0,r.jsx)(d.A,{message:U,onClose:()=>L(""),className:"mb-6"}),P&&(0,r.jsx)(o.A,{message:P,onClose:()=>_(""),className:"mb-6"}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"返回审核列表"]}),(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsx)("img",{src:s.logo,alt:s.name,className:"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),(e=>{if("approved"===e.status&&e.launchDate&&new Date(e.launchDate)<=new Date)return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"已发布"]});switch(e.status){case"pending":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"待审核"]});case"approved":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"已通过审核"]});case"rejected":return(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"已拒绝"]});default:return null}})(s)]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-4",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:N[s.category]}),(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[(0,r.jsx)(g.A,{className:"w-3 h-3 mr-1"}),w[s.pricing]]}),(0,r.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[(0,r.jsx)(y,{className:"w-4 h-4 mr-1"}),"访问网站",(0,r.jsx)(b.A,{className:"w-3 h-3 ml-1"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 max-w-3xl",children:s.tagline})]})]}),"pending"===s.status&&(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("button",{onClick:$,disabled:q,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 mr-2"}),q?"处理中...":"批准"]}),(0,r.jsxs)("button",{onClick:()=>R(!0),disabled:q,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"拒绝"]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"工具描述"}),(0,r.jsxs)("div",{className:"prose max-w-none",children:[(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:s.description}),s.longDescription&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"详细描述"}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:s.longDescription})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:s.tags.map((e,s)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[(0,r.jsx)(v.A,{className:"w-3 h-3 mr-1"}),e]},s))})]}),s.screenshots&&s.screenshots.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"截图预览"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.screenshots.map((e,t)=>(0,r.jsx)("img",{src:e,alt:`${s.name} 截图 ${t+1}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200"},t))})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"提交信息"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.submittedBy}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"提交者ID"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.submittedAt).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"提交时间"})]})]}),s.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.selectedLaunchDate).toLocaleDateString("zh-CN")}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"选择的发布日期"})]})]}),s.launchDate&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.launchDate).toLocaleDateString("zh-CN")}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"实际发布日期"})]})]})]})]}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"审核指南"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• 验证工具网站是否可正常访问"}),(0,r.jsx)("li",{children:"• 检查工具描述是否准确客观"}),(0,r.jsx)("li",{children:"• 确认分类和标签是否合适"}),(0,r.jsx)("li",{children:"• 评估工具质量和实用性"}),(0,r.jsx)("li",{children:"• 检查是否存在重复提交"})]})]})]})})]})]}),T&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请详细说明拒绝的原因，这将帮助提交者了解问题并改进他们的提交。"}),(0,r.jsx)("textarea",{value:S,onChange:e=>C(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入详细的拒绝原因..."}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:()=>{R(!1),C("")},disabled:q,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:"取消"}),(0,r.jsx)("button",{onClick:M,disabled:!S.trim()||q,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:q?"处理中...":"确认拒绝"})]})]})})]})})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,4825,2579,1525,1226],()=>t(3650));module.exports=r})();