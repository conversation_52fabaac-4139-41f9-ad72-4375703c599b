(()=>{var e={};e.id=4926,e.ids=[4926],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48564:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var r=t(60687),a=t(43210),l=t(98402),n=t(33823),o=t(11011),i=t(56976),d=t(48730),c=t(5336),x=t(35071);let m=(0,t(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var h=t(53411),p=t(25541),u=t(93613),g=t(13861);function j(){let[e,s]=(0,a.useState)("7d"),[t,j]=(0,a.useState)(null),[b,v]=(0,a.useState)(!0),[y,f]=(0,a.useState)(""),N=async()=>{try{v(!0),f("");let s=await i.u.getAdminStats(e);s.success&&s.data?j(s.data):f(s.error||"获取统计数据失败")}catch(e){f("网络错误，请重试")}finally{v(!1)}};return b?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(n.A,{size:"lg",className:"py-20"})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[y&&(0,r.jsx)(o.A,{message:y,onClose:()=>f(""),className:"mb-6"}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(h.A,{className:"mr-3 h-8 w-8 text-blue-600"}),"管理员统计面板"]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"查看网站运营数据和审核统计"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"1d",children:"今天"}),(0,r.jsx)("option",{value:"7d",children:"最近7天"}),(0,r.jsx)("option",{value:"30d",children:"最近30天"}),(0,r.jsx)("option",{value:"90d",children:"最近90天"})]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总工具数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t?.totalTools||0})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+12%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"待审核"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:t?.pendingReview||0})]}),(0,r.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,r.jsx)(d.A,{className:"w-6 h-6 text-yellow-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,r.jsx)("span",{className:"text-yellow-600",children:"需要关注"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"今日批准"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:(t?.approvedToday||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-green-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+8%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"今日拒绝"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:(t?.rejectedToday||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-red-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+15%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"审核概览"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"总工具数"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:t?.totalTools||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-yellow-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 text-yellow-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"待审核"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:t?.pendingReview||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(c.A,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"今日批准"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:t?.approvedToday||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-red-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-red-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"今日拒绝"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-red-600",children:t?.rejectedToday||0})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"快速操作"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>window.location.href="/admin",className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(g.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"查看待审核工具"})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(m,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"查看审核历史"})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"导出统计报告"})]}),(0,r.jsxs)("button",{onClick:()=>N(),className:"w-full flex items-center justify-center space-x-2 p-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"刷新数据"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"系统信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:t?"在线":"离线"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"系统状态"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"统计周期"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:new Date().toLocaleDateString("zh-CN")}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"最后更新"})]})]})]})]})})}},48654:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx","default")},53411:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},54211:(e,s,t)=>{Promise.resolve().then(t.bind(t,48654))},56976:(e,s,t)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function l(){return"production"}function n(){return"development"===l()}t.d(s,{u:()=>c});let o={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:l(),isDevelopment:n(),isProduction:"production"===l(),port:process.env.PORT||"3001"};n()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",o.baseUrl),console.log("  API Base URL:",o.apiBaseUrl),console.log("  NextAuth URL:",o.nextAuthUrl),console.log("  Environment:",o.environment),console.log("  Port:",o.port));let i=a();class d{constructor(e=i){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}}let c=new d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64080:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),o=t(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let d={children:["",{children:["[locale]",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,48654)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/admin/dashboard/page",pathname:"/[locale]/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68283:(e,s,t)=>{Promise.resolve().then(t.bind(t,48564))},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,4825,2579,1525,1226],()=>t(64080));module.exports=r})();