(()=>{var e={};e.id=3919,e.ids=[3919],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14660:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),o=s(88170),l=s.n(o),n=s(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);s.d(t,i);let d={children:["",{children:["[locale]",{children:["test-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,39931)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,11434)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/test-auth/page",pathname:"/[locale]/test-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39931:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx","default")},48280:(e,t,s)=>{Promise.resolve().then(s.bind(s,39931))},48952:(e,t,s)=>{Promise.resolve().then(s.bind(s,58017))},58017:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(60687),a=s(82136),o=s(43210);function l(){let{data:e,status:t}=(0,a.useSession)(),[s,l]=(0,o.useState)(""),[n,i]=(0,o.useState)(""),[d,c]=(0,o.useState)(""),[u,p]=(0,o.useState)(""),m=async()=>{try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s})}),t=await e.json();t.success?(c(t.token),p("验证码已发送！Token: "+t.token)):p("发送失败: "+t.error)}catch(e){p("网络错误")}},h=async()=>{try{let e=await (0,a.signIn)("email-code",{email:s,code:n,token:d,redirect:!1});e?.ok?p("登录成功！"):p("登录失败: "+e?.error)}catch(e){p("验证失败")}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-md p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"认证测试页面"}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"当前状态"}),(0,r.jsxs)("p",{children:["Status: ",t]}),e?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:["已登录用户: ",e.user?.email]}),(0,r.jsxs)("p",{children:["用户名: ",e.user?.name]}),(0,r.jsxs)("p",{children:["用户ID: ",e.user?.id]}),(0,r.jsxs)("p",{children:["用户角色: ",e.user?.role]}),(0,r.jsx)("button",{onClick:()=>(0,a.signOut)(),className:"mt-2 bg-red-500 text-white px-4 py-2 rounded",children:"退出登录"})]}):(0,r.jsx)("p",{children:"未登录"})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"邮件验证码登录测试"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"邮箱"}),(0,r.jsx)("input",{type:"email",value:s,onChange:e=>l(e.target.value),className:"w-full border rounded px-3 py-2",placeholder:"输入邮箱"})]}),(0,r.jsx)("button",{onClick:m,className:"w-full bg-blue-500 text-white py-2 rounded mb-4",children:"发送验证码"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"验证码"}),(0,r.jsx)("input",{type:"text",value:n,onChange:e=>i(e.target.value),className:"w-full border rounded px-3 py-2",placeholder:"输入6位验证码"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Token"}),(0,r.jsx)("input",{type:"text",value:d,onChange:e=>c(e.target.value),className:"w-full border rounded px-3 py-2",placeholder:"自动填充"})]}),(0,r.jsx)("button",{onClick:h,className:"w-full bg-green-500 text-white py-2 rounded mb-4",children:"验证登录"}),u&&(0,r.jsx)("div",{className:"p-3 bg-gray-100 rounded text-sm",children:u})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"OAuth登录测试"}),(0,r.jsx)("button",{onClick:()=>(0,a.signIn)("google"),className:"w-full bg-red-500 text-white py-2 rounded mb-2",children:"Google登录"}),(0,r.jsx)("button",{onClick:()=>(0,a.signIn)("github"),className:"w-full bg-gray-800 text-white py-2 rounded",children:"GitHub登录"})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,4825,2579,1525],()=>s(14660));module.exports=r})();