{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "3485b21c9547f2d17fcade987d9efae9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8b7b9a3b87f261adc405bab8a2b9989caad4b721efd50f805c36288b01eb33d0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bbb8006204e15e62076c34939515535816e3ab5a11a329fd1aa0eac5c9e356bb"}}}, "instrumentation": null, "functions": {}}