{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "b771e04e7e78cfa80f2b6cdbdf1224d5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d5c715b70d1539f86557a03494079513e28591838180ba5e443bed3031c6166f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f3d4dd012c0f40f6fdbcc42ec9a98d5b4cee431d6d3cdd56398e23b2aef08365"}}}, "instrumentation": null, "functions": {}}