{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "D2oXEzSDpaNHk5gMxxJT/dRNB/KQc+TMEcHoxsY4RNg=", "__NEXT_PREVIEW_MODE_ID": "cb8b2e1dfa2d653dc004b1cc74a07cae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4bd577bffe5a0cb3b1e9eb69befc5a8a8ab780b53a7a25ca8d86e5d2cc1b14d3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "325b8b1fb3c75d2bcec273f894eae92b52895effa02c29fef1a966a38956df19"}}}, "instrumentation": null, "functions": {}}