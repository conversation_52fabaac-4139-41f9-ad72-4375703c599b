{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "e88053f2844b9228c9e25cc5ba1aa209", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "872d16effc2713cfa5f6e9e284b82e87e87d9617f52f095d4a095d04ddcb14cc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "13ea991dde3284ae82d54b1604549ecf2f4958af2294f281c3da71a5dca30a9d"}}}, "instrumentation": null, "functions": {}}