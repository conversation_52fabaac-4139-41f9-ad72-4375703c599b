{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4946fe53._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_3d5cd307.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "gdnsvdj5MBZRV476HB5HyWA7AxL3wQ+HHgWRgj0xhxo=", "__NEXT_PREVIEW_MODE_ID": "25a07aac02bc1c0d3d30ad6060cf0500", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a597c66c703c7dc3af2f8a01fe53ee21ed20919ffece33b842308f6412b80778", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8e551c3f239a078acb183a741514c466df3fc4ae3d4dfd10f4e7f124ddfda1a5"}}}, "instrumentation": null, "functions": {}}