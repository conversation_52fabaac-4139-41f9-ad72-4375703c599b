{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "hTHUHp0Vu7ZoD2lx0Bvr6NSbQpiOl2TvijxC6rE2KCg=", "__NEXT_PREVIEW_MODE_ID": "71f70f106b393d8af55dc658a80f9a1c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e3bac14c2b33a7948299056c1283fccdb3ef6a7d6a2fc10908c8186a08440539", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d4a63c4cb5b2085d4f82838446c5ea381763fc572fa7081054fec96ecbae216a"}}}, "instrumentation": null, "functions": {}}