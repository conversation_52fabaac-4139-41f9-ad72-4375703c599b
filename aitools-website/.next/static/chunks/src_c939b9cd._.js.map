{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport default function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    const metrics: PerformanceMetrics = {};\n\n    // 监控 First Contentful Paint (FCP)\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n        if (fcpEntry) {\n          metrics.fcp = fcpEntry.startTime;\n          reportMetric('FCP', fcpEntry.startTime);\n        }\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n\n    // 监控 Largest Contentful Paint (LCP)\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        reportMetric('LCP', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n\n    // 监控 First Input Delay (FID)\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          reportMetric('FID', entry.processingStart - entry.startTime);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n\n    // 监控 Cumulative Layout Shift (CLS)\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        reportMetric('CLS', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n\n    // 监控 Time to First Byte (TTFB)\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        metrics.ttfb = ttfb;\n        reportMetric('TTFB', ttfb);\n      }\n    };\n\n    // 报告性能指标\n    const reportMetric = (name: string, value: number) => {\n      // 在开发环境中输出到控制台\n      if (process.env.NODE_ENV === 'development') {\n        console.log(`Performance Metric - ${name}:`, value);\n      }\n\n      // 在生产环境中可以发送到分析服务\n      // 例如 Google Analytics, Vercel Analytics 等\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', 'web_vitals', {\n          event_category: 'Performance',\n          event_label: name,\n          value: Math.round(value),\n          non_interaction: true,\n        });\n      }\n    };\n\n    // 检查浏览器支持\n    if (typeof PerformanceObserver !== 'undefined') {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n    }\n\n    observeTTFB();\n\n    // 页面卸载时报告最终指标\n    const reportFinalMetrics = () => {\n      if (Object.keys(metrics).length > 0) {\n        // 可以发送到分析服务\n        console.log('Final Performance Metrics:', metrics);\n      }\n    };\n\n    window.addEventListener('beforeunload', reportFinalMetrics);\n\n    return () => {\n      window.removeEventListener('beforeunload', reportFinalMetrics);\n    };\n  }, []);\n\n  return null; // 这是一个无UI的监控组件\n}\n\n// 性能优化建议\nexport const PerformanceOptimizations = {\n  // FCP 优化建议\n  fcp: {\n    good: 1800, // < 1.8s\n    needsImprovement: 3000, // 1.8s - 3s\n    suggestions: [\n      '减少服务器响应时间',\n      '消除阻塞渲染的资源',\n      '压缩CSS和JavaScript',\n      '使用CDN加速资源加载',\n    ],\n  },\n  \n  // LCP 优化建议\n  lcp: {\n    good: 2500, // < 2.5s\n    needsImprovement: 4000, // 2.5s - 4s\n    suggestions: [\n      '优化图片加载',\n      '预加载关键资源',\n      '减少JavaScript执行时间',\n      '使用服务端渲染',\n    ],\n  },\n  \n  // FID 优化建议\n  fid: {\n    good: 100, // < 100ms\n    needsImprovement: 300, // 100ms - 300ms\n    suggestions: [\n      '减少JavaScript执行时间',\n      '分割长任务',\n      '使用Web Workers',\n      '延迟加载非关键JavaScript',\n    ],\n  },\n  \n  // CLS 优化建议\n  cls: {\n    good: 0.1, // < 0.1\n    needsImprovement: 0.25, // 0.1 - 0.25\n    suggestions: [\n      '为图片和视频设置尺寸属性',\n      '避免在现有内容上方插入内容',\n      '使用transform动画而非改变布局的动画',\n      '预留广告位空间',\n    ],\n  },\n};\n\n// 声明全局gtag类型\ndeclare global {\n  interface Window {\n    gtag?: (...args: any[]) => void;\n  }\n}\n"], "names": [], "mappings": ";;;;AAeQ;AAbR;;AAFA;;AAYe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,gBAAgB;YAChB,wCAA2C;gBACzC;YACF;;YAEA,MAAM;YAEN,kCAAkC;YAClC,MAAM;YAYN,oCAAoC;YACpC,MAAM;YAUN,6BAA6B;YAC7B,MAAM;YAWN,mCAAmC;YACnC,MAAM;YAeN,+BAA+B;YAC/B,MAAM;YASN,SAAS;YACT,MAAM;YA4BN,cAAc;YACd,MAAM;QAYR;uCAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B;GApHwB;KAAA;AAuHjB,MAAM,2BAA2B;IACtC,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { FaHeart, FaRegHeart } from 'react-icons/fa';\nimport { useLike } from '@/contexts/LikeContext';\n\ninterface LikeButtonProps {\n  toolId: string;\n  initialLikes?: number;\n  initialLiked?: boolean;\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean;\n  showCount?: boolean; // 是否显示点赞数量\n  size?: 'sm' | 'md' | 'lg'; // 按钮大小\n}\n\nexport default function LikeButton({\n  toolId,\n  initialLikes = 0,\n  initialLiked = false,\n  onLoginRequired,\n  onUnlike,\n  isInLikedPage = false,\n  showCount = true,\n  size = 'md'\n}: LikeButtonProps) {\n  const { data: session } = useSession();\n  const { getToolState, initializeToolState, toggleLike } = useLike();\n\n  // 获取当前工具的状态\n  const toolState = getToolState(toolId);\n\n  // 初始化工具状态\n  useEffect(() => {\n    initializeToolState(toolId, initialLikes, initialLiked);\n  }, [toolId, initialLikes, initialLiked]); // 移除initializeToolState依赖，避免无限循环\n\n  // 处理点赞点击\n  const handleLike = async () => {\n    if (!session) {\n      onLoginRequired?.();\n      return;\n    }\n\n    if (toolState.loading) return;\n\n    // 记录操作前的状态\n    const wasLiked = toolState.liked;\n\n    // 执行点赞操作\n    const success = await toggleLike(toolId, isInLikedPage);\n\n    // 如果是在收藏页面且从已点赞变为未点赞，调用onUnlike回调\n    if (success && isInLikedPage && wasLiked && onUnlike) {\n      onUnlike(toolId);\n    }\n  };\n\n  // 根据size确定样式\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return {\n          button: 'p-1.5',\n          icon: 'h-4 w-4',\n          text: 'text-sm'\n        };\n      case 'lg':\n        return {\n          button: 'p-3',\n          icon: 'h-6 w-6',\n          text: 'text-lg'\n        };\n      default: // md\n        return {\n          button: 'p-2',\n          icon: 'h-5 w-5',\n          text: 'text-base'\n        };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n\n  return (\n    <button\n      onClick={handleLike}\n      disabled={toolState.loading}\n      className={`\n        ${sizeClasses.button}\n        inline-flex items-center space-x-1\n        ${toolState.liked\n          ? 'text-red-500 hover:text-red-600'\n          : 'text-gray-400 hover:text-red-500'\n        }\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      `}\n      title={toolState.liked ? '取消点赞' : '点赞'}\n    >\n      {toolState.loading ? (\n        <div className={`${sizeClasses.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`} />\n      ) : toolState.liked ? (\n        <FaHeart className={sizeClasses.icon} />\n      ) : (\n        <FaRegHeart className={sizeClasses.icon} />\n      )}\n      {showCount && (\n        <span className={`${sizeClasses.text} font-medium`}>\n          {toolState.likes}\n        </span>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAkBe,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,CAAC,EAChB,eAAe,KAAK,EACpB,eAAe,EACf,QAAQ,EACR,gBAAgB,KAAK,EACrB,YAAY,IAAI,EAChB,OAAO,IAAI,EACK;;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhE,YAAY;IACZ,MAAM,YAAY,aAAa;IAE/B,UAAU;IACV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,oBAAoB,QAAQ,cAAc;QAC5C;+BAAG;QAAC;QAAQ;QAAc;KAAa,GAAG,iCAAiC;IAE3E,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,CAAC,SAAS;YACZ;YACA;QACF;QAEA,IAAI,UAAU,OAAO,EAAE;QAEvB,WAAW;QACX,MAAM,WAAW,UAAU,KAAK;QAEhC,SAAS;QACT,MAAM,UAAU,MAAM,WAAW,QAAQ;QAEzC,kCAAkC;QAClC,IAAI,WAAW,iBAAiB,YAAY,UAAU;YACpD,SAAS;QACX;IACF;IAEA,aAAa;IACb,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;YACF;gBACE,OAAO;oBACL,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC;QACC,SAAS;QACT,UAAU,UAAU,OAAO;QAC3B,WAAW,CAAC;QACV,EAAE,YAAY,MAAM,CAAC;;QAErB,EAAE,UAAU,KAAK,GACb,oCACA,mCACH;;;MAGH,CAAC;QACD,OAAO,UAAU,KAAK,GAAG,SAAS;;YAEjC,UAAU,OAAO,iBAChB,6LAAC;gBAAI,WAAW,GAAG,YAAY,IAAI,CAAC,oEAAoE,CAAC;;;;;uBACvG,UAAU,KAAK,iBACjB,6LAAC,iJAAA,CAAA,UAAO;gBAAC,WAAW,YAAY,IAAI;;;;;qCAEpC,6LAAC,iJAAA,CAAA,aAAU;gBAAC,WAAW,YAAY,IAAI;;;;;;YAExC,2BACC,6LAAC;gBAAK,WAAW,GAAG,YAAY,IAAI,CAAC,YAAY,CAAC;0BAC/C,UAAU,KAAK;;;;;;;;;;;;AAK1B;GAlGwB;;QAUI,iJAAA,CAAA,aAAU;QACsB,kIAAA,CAAA,UAAO;;;KAX3C", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  sizes?: string;\n  placeholder?: 'blur' | 'empty';\n  blurDataURL?: string;\n  fallbackSrc?: string;\n  onError?: () => void;\n}\n\nexport default function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  className = '',\n  priority = false,\n  fill = false,\n  sizes,\n  placeholder = 'empty',\n  blurDataURL,\n  fallbackSrc = '/images/placeholder.svg',\n  onError,\n}: OptimizedImageProps) {\n  const [imgSrc, setImgSrc] = useState(src);\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n\n  const handleError = () => {\n    setHasError(true);\n    setIsLoading(false);\n    setImgSrc(fallbackSrc);\n    onError?.();\n  };\n\n  const handleLoad = () => {\n    setIsLoading(false);\n  };\n\n  // 生成模糊占位符\n  const generateBlurDataURL = (w: number = 10, h: number = 10) => {\n    const canvas = document.createElement('canvas');\n    canvas.width = w;\n    canvas.height = h;\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n      ctx.fillStyle = '#f3f4f6';\n      ctx.fillRect(0, 0, w, h);\n    }\n    return canvas.toDataURL();\n  };\n\n  const imageProps = {\n    src: imgSrc,\n    alt,\n    className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,\n    onError: handleError,\n    onLoad: handleLoad,\n    priority,\n    placeholder: placeholder === 'blur' ? 'blur' as const : 'empty' as const,\n    blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n    sizes: sizes || (fill ? '100vw' : undefined),\n  };\n\n  if (fill) {\n    return (\n      <div className=\"relative overflow-hidden\">\n        <Image\n          {...imageProps}\n          fill\n          style={{ objectFit: 'cover' }}\n        />\n        {isLoading && (\n          <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      <Image\n        {...imageProps}\n        width={width}\n        height={height}\n      />\n      {isLoading && (\n        <div \n          className=\"absolute inset-0 bg-gray-200 animate-pulse\"\n          style={{ width, height }}\n        />\n      )}\n    </div>\n  );\n}\n\n// 预设的图片尺寸配置\nexport const ImageSizes = {\n  avatar: { width: 40, height: 40 },\n  avatarLarge: { width: 80, height: 80 },\n  toolLogo: { width: 64, height: 64 },\n  toolLogoLarge: { width: 128, height: 128 },\n  thumbnail: { width: 200, height: 150 },\n  card: { width: 300, height: 200 },\n  hero: { width: 1200, height: 600 },\n} as const;\n\n// 响应式图片尺寸字符串\nexport const ResponsiveSizes = {\n  avatar: '40px',\n  toolLogo: '64px',\n  thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n  hero: '100vw',\n  full: '100vw',\n} as const;\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA;;;AAoBe,SAAS,eAAe,EACrC,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,cAAc,OAAO,EACrB,WAAW,EACX,cAAc,yBAAyB,EACvC,OAAO,EACa;;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,cAAc;QAClB,YAAY;QACZ,aAAa;QACb,UAAU;QACV;IACF;IAEA,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,UAAU;IACV,MAAM,sBAAsB,CAAC,IAAY,EAAE,EAAE,IAAY,EAAE;QACzD,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,KAAK;YACP,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG;QACxB;QACA,OAAO,OAAO,SAAS;IACzB;IAEA,MAAM,aAAa;QACjB,KAAK;QACL;QACA,WAAW,GAAG,UAAU,CAAC,EAAE,YAAY,cAAc,cAAc,gCAAgC,CAAC;QACpG,SAAS;QACT,QAAQ;QACR;QACA,aAAa,gBAAgB,SAAS,SAAkB;QACxD,aAAa,eAAe,CAAC,gBAAgB,SAAS,wBAAwB,SAAS;QACvF,OAAO,SAAS,CAAC,OAAO,UAAU,SAAS;IAC7C;IAEA,IAAI,MAAM;QACR,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,gIAAA,CAAA,UAAK;oBACH,GAAG,UAAU;oBACd,IAAI;oBACJ,OAAO;wBAAE,WAAW;oBAAQ;;;;;;gBAE7B,2BACC,6LAAC;oBAAI,WAAU;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gIAAA,CAAA,UAAK;gBACH,GAAG,UAAU;gBACd,OAAO;gBACP,QAAQ;;;;;;YAET,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE;oBAAO;gBAAO;;;;;;;;;;;;AAKjC;GApFwB;KAAA;AAuFjB,MAAM,aAAa;IACxB,QAAQ;QAAE,OAAO;QAAI,QAAQ;IAAG;IAChC,aAAa;QAAE,OAAO;QAAI,QAAQ;IAAG;IACrC,UAAU;QAAE,OAAO;QAAI,QAAQ;IAAG;IAClC,eAAe;QAAE,OAAO;QAAK,QAAQ;IAAI;IACzC,WAAW;QAAE,OAAO;QAAK,QAAQ;IAAI;IACrC,MAAM;QAAE,OAAO;QAAK,QAAQ;IAAI;IAChC,MAAM;QAAE,OAAO;QAAM,QAAQ;IAAI;AACnC;AAGO,MAAM,kBAAkB;IAC7B,QAAQ;IACR,UAAU;IACV,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Eye, Heart, ExternalLink } from 'lucide-react';\nimport LikeButton from './tools/LikeButton';\nimport OptimizedImage, { ImageSizes, ResponsiveSizes } from './ui/OptimizedImage';\nimport { getToolPricingColor, getToolPricingText } from '@/constants/pricing';\n\ninterface ToolCardProps {\n  tool: {\n    _id: string;\n    name: string;\n    description: string;\n    website: string;\n    logo?: string;\n    category: string;\n    tags: string[];\n    pricing: 'free' | 'freemium' | 'paid';\n    views: number;\n    likes: number;\n  };\n  onLoginRequired?: () => void;\n  onUnlike?: (toolId: string) => void;\n  isInLikedPage?: boolean; // 新增：标识是否在liked页面\n}\n\nconst ToolCard: React.FC<ToolCardProps> = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false }) => {\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            {tool.logo ? (\n              <OptimizedImage\n                src={tool.logo}\n                alt={`${tool.name} logo`}\n                width={ImageSizes.toolLogo.width}\n                height={ImageSizes.toolLogo.height}\n                className=\"rounded-lg object-cover\"\n                sizes={ResponsiveSizes.toolLogo}\n                placeholder=\"blur\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">\n                  {tool.name.charAt(0).toUpperCase()}\n                </span>\n              </div>\n            )}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                {tool.name}\n              </h3>\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getToolPricingColor(tool.pricing)}`}>\n                {getToolPricingText(tool.pricing)}\n              </span>\n            </div>\n          </div>\n          \n          <a\n            href={tool.website}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"text-gray-400 hover:text-blue-600 transition-colors\"\n          >\n            <ExternalLink className=\"h-5 w-5\" />\n          </a>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {tool.description}\n        </p>\n\n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {tool.tags.slice(0, 3).map((tag, index) => (\n            <span\n              key={index}\n              className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\"\n            >\n              {tag}\n            </span>\n          ))}\n          {tool.tags.length > 3 && (\n            <span className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\">\n              +{tool.tags.length - 3}\n            </span>\n          )}\n        </div>\n\n        {/* Stats and Actions */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n            <div className=\"flex items-center space-x-1\">\n              <Eye className=\"h-4 w-4\" />\n              <span>{tool.views}</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Heart className=\"h-4 w-4\" />\n              <span>{tool.likes}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <LikeButton\n              toolId={tool._id}\n              initialLikes={tool.likes}\n              initialLiked={isInLikedPage} // 在收藏页面，所有工具都应该是已点赞状态\n              onLoginRequired={onLoginRequired}\n              onUnlike={onUnlike}\n              isInLikedPage={isInLikedPage}\n            />\n            <Link\n              href={`/tools/${tool._id}`}\n              className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\"\n            >\n              查看详情\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ToolCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AA2BA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,gBAAgB,KAAK,EAAE;IAEnG,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,iBACR,6LAAC,6IAAA,CAAA,UAAc;oCACb,KAAK,KAAK,IAAI;oCACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;oCACxB,OAAO,6IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK;oCAChC,QAAQ,6IAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,MAAM;oCAClC,WAAU;oCACV,OAAO,6IAAA,CAAA,kBAAe,CAAC,QAAQ;oCAC/B,aAAY;;;;;yDAGd,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8CAItC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAW,CAAC,wEAAwE,EAAE,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,OAAO,GAAG;sDAC5H,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,OAAO;;;;;;;;;;;;;;;;;;sCAKtC,6LAAC;4BACC,MAAM,KAAK,OAAO;4BAClB,QAAO;4BACP,KAAI;4BACJ,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,6LAAC;oBAAE,WAAU;8BACV,KAAK,WAAW;;;;;;8BAInB,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;wBAMR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;4BAAK,WAAU;;gCAA8F;gCAC1G,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8BAM3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,4IAAA,CAAA,UAAU;oCACT,QAAQ,KAAK,GAAG;oCAChB,cAAc,KAAK,KAAK;oCACxB,cAAc;oCACd,iBAAiB;oCACjB,UAAU;oCACV,eAAe;;;;;;8CAEjB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oCAC1B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KApGM;uCAsGS", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts"], "sourcesContent": ["// AI工具分类的统一配置文件\n// 这个文件包含所有分类的完整信息，确保整个应用中的一致性\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 完整的分类配置\nexport const CATEGORY_CONFIGS: CategoryConfig[] = [\n  {\n    slug: 'text-generation',\n    name: '文本生成',\n    description: '利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    name: '图像生成',\n    description: '使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    name: '代码生成',\n    description: '智能代码生成和编程辅助工具，提高开发效率',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'data-analysis',\n    name: '数据分析',\n    description: '数据分析和可视化工具，帮助洞察数据价值',\n    icon: '📊',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'audio-processing',\n    name: '音频处理',\n    description: '音频处理、语音合成、音乐生成等音频AI工具',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    name: '视频编辑',\n    description: '视频生成、编辑、剪辑等视频处理AI工具',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    name: '语言翻译',\n    description: '多语言翻译和本地化AI工具',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    name: '搜索引擎',\n    description: '智能搜索和信息检索AI工具',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'education',\n    name: '教育学习',\n    description: '教育培训和学习辅助AI工具',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'marketing',\n    name: '营销工具',\n    description: '数字营销和推广AI工具',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'productivity',\n    name: '生产力工具',\n    description: '提高工作效率的AI工具',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'customer-service',\n    name: '客户服务',\n    description: '客户支持和服务AI工具',\n    icon: '🎧',\n    color: '#F59E0B'\n  }\n];\n\n// 生成分类选项（用于下拉框等）\nexport const CATEGORY_OPTIONS: CategoryOption[] = CATEGORY_CONFIGS.map(config => ({\n  value: config.slug,\n  label: config.name\n}));\n\n// 包含\"所有分类\"选项的分类选项\nexport const CATEGORY_OPTIONS_WITH_ALL: CategoryOption[] = [\n  { value: '', label: '所有分类' },\n  ...CATEGORY_OPTIONS\n];\n\n// 分类标签映射（slug -> 名称）\nexport const CATEGORY_LABELS: Record<string, string> = CATEGORY_CONFIGS.reduce(\n  (acc, config) => {\n    acc[config.slug] = config.name;\n    return acc;\n  },\n  {} as Record<string, string>\n);\n\n// 分类元数据映射（slug -> 完整配置）\nexport const CATEGORY_METADATA: Record<string, CategoryConfig> = CATEGORY_CONFIGS.reduce(\n  (acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  },\n  {} as Record<string, CategoryConfig>\n);\n\n// 获取分类配置的辅助函数\nexport const getCategoryConfig = (slug: string): CategoryConfig | undefined => {\n  return CATEGORY_METADATA[slug];\n};\n\n// 获取分类名称的辅助函数\nexport const getCategoryName = (slug: string): string => {\n  return CATEGORY_LABELS[slug] || slug;\n};\n\n// 验证分类是否存在的辅助函数\nexport const isValidCategory = (slug: string): boolean => {\n  return slug in CATEGORY_METADATA;\n};\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_CONFIGS.map(config => config.slug);\n"], "names": [], "mappings": "AAAA,gBAAgB;AAChB,8BAA8B;;;;;;;;;;;;AAgBvB,MAAM,mBAAqC;IAChD;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,mBAAqC,iBAAiB,GAAG,MAAC,CAAA,SAAU,CAAC;QAChF,OAAO,OAAO,IAAI;QAClB,OAAO,OAAO,IAAI;IACpB,CAAC;;AAGM,MAAM,4BAA8C;IACzD;QAAE,OAAO;QAAI,OAAO;IAAO;OACxB;CACJ;AAGM,MAAM,kBAA0C,iBAAiB,MAAM,OAC5E,CAAC,KAAK;IACJ,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI;IAC9B,OAAO;AACT,GACA,CAAC;;AAII,MAAM,oBAAoD,iBAAiB,MAAM,OACtF,CAAC,KAAK;IACJ,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GACA,CAAC;;AAII,MAAM,oBAAoB,CAAC;IAChC,OAAO,iBAAiB,CAAC,KAAK;AAChC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,eAAe,CAAC,KAAK,IAAI;AAClC;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,QAAQ;AACjB;AAGO,MAAM,iBAAiB,iBAAiB,GAAG,OAAC,CAAA,SAAU,OAAO,IAAI", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport ToolCard from '@/components/ToolCard';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport { Tool } from '@/lib/api';\nimport { TOOL_PRICING_OPTIONS } from '@/constants/pricing';\nimport { CATEGORY_OPTIONS_WITH_ALL } from '@/constants/categories';\nimport { Search, Filter, Grid, List, ChevronDown } from 'lucide-react';\n\n// 使用统一的分类选项配置\nconst categories = CATEGORY_OPTIONS_WITH_ALL;\n\n// 使用统一的价格选项配置\nconst pricingOptions = TOOL_PRICING_OPTIONS;\n\nconst sortOptions = [\n  { value: 'popular', label: '最受欢迎' },\n  { value: 'newest', label: '最新添加' },\n  { value: 'name', label: '名称排序' },\n  { value: 'views', label: '浏览量' }\n];\n\ninterface ToolsPageClientProps {\n  initialTools: Tool[];\n  error: string | null;\n}\n\nexport default function ToolsPageClient({ initialTools, error }: ToolsPageClientProps) {\n  const [tools] = useState<Tool[]>(initialTools);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [selectedPricing, setSelectedPricing] = useState('');\n  const [sortBy, setSortBy] = useState('popular');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Filter and sort tools\n  const filteredTools = tools.filter((tool: Tool) => {\n    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         tool.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesCategory = !selectedCategory || tool.category === selectedCategory;\n    const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;\n\n    return matchesSearch && matchesCategory && matchesPricing;\n  });\n\n  const sortedTools = [...filteredTools].sort((a, b) => {\n    switch (sortBy) {\n      case 'popular':\n        return (b.likes || 0) - (a.likes || 0);\n      case 'views':\n        return (b.views || 0) - (a.views || 0);\n      case 'name':\n        return a.name.localeCompare(b.name);\n      case 'newest':\n        if (a.launchDate && b.launchDate) {\n          return new Date(b.launchDate).getTime() - new Date(a.launchDate).getTime();\n        }\n        return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();\n      default:\n        return 0;\n    }\n  });\n\n  if (error) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <ErrorMessage message={error} />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">AI 工具目录</h1>\n        <p className=\"text-lg text-gray-600\">\n          发现 {tools.length} 个精选的 AI 工具，提升您的工作效率\n        </p>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n        {/* Search Bar */}\n        <div className=\"relative mb-4\">\n          <input\n            type=\"text\"\n            placeholder=\"搜索工具名称、描述或标签...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          <Search className=\"absolute left-3 top-3 h-5 w-5 text-gray-400\" />\n        </div>\n\n        {/* Filter Toggle Button (Mobile) */}\n        <div className=\"md:hidden mb-4\">\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\"\n          >\n            <Filter className=\"mr-2 h-4 w-4\" />\n            筛选选项\n            <ChevronDown className={`ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`} />\n          </button>\n        </div>\n\n        {/* Filters */}\n        <div className={`grid grid-cols-1 md:grid-cols-4 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`}>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">分类</label>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {categories.map(category => (\n                <option key={category.value} value={category.value}>\n                  {category.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">价格</label>\n            <select\n              value={selectedPricing}\n              onChange={(e) => setSelectedPricing(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {pricingOptions.map(option => (\n                <option key={option.value} value={option.value}>\n                  {option.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">排序</label>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {sortOptions.map(option => (\n                <option key={option.value} value={option.value}>\n                  {option.label}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">视图</label>\n            <div className=\"flex rounded-lg border border-gray-300\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${\n                  viewMode === 'grid'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                <Grid className=\"h-4 w-4 mx-auto\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${\n                  viewMode === 'list'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-white text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                <List className=\"h-4 w-4 mx-auto\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Results */}\n      <div className=\"mb-6\">\n        <p className=\"text-gray-600\">\n          显示 {sortedTools.length} 个结果\n          {searchTerm && ` 搜索 \"${searchTerm}\"`}\n          {selectedCategory && ` 在 \"${categories.find(c => c.value === selectedCategory)?.label}\"`}\n        </p>\n      </div>\n\n      {/* Tools Grid/List */}\n      {sortedTools.length > 0 ? (\n        <div className={viewMode === 'grid' \n          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'\n          : 'space-y-4'\n        }>\n          {sortedTools.map((tool) => (\n            <ToolCard key={tool._id} tool={tool} />\n          ))}\n        </div>\n      ) : (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-400 mb-4\">\n            <Search className=\"h-12 w-12 mx-auto\" />\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">未找到匹配的工具</h3>\n          <p className=\"text-gray-600\">\n            尝试调整搜索条件或筛选选项\n          </p>\n        </div>\n      )}\n\n      {/* Pagination would go here in a real application */}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AAUA,cAAc;AACd,MAAM,aAAa,iIAAA,CAAA,4BAAyB;AAE5C,cAAc;AACd,MAAM,iBAAiB,8HAAA,CAAA,uBAAoB;AAE3C,MAAM,cAAc;IAClB;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAU,OAAO;IAAO;IACjC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAS,OAAO;IAAM;CAChC;AAOc,SAAS,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAwB;;IACnF,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,wBAAwB;IACxB,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC;QAClC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,MAAgB,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACtG,MAAM,kBAAkB,CAAC,oBAAoB,KAAK,QAAQ,KAAK;QAC/D,MAAM,iBAAiB,CAAC,mBAAmB,KAAK,OAAO,KAAK;QAE5D,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,MAAM,cAAc;WAAI;KAAc,CAAC,IAAI,CAAC,CAAC,GAAG;QAC9C,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;YACvC,KAAK;gBACH,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;YACvC,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,IAAI,EAAE,UAAU,IAAI,EAAE,UAAU,EAAE;oBAChC,OAAO,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;gBAC1E;gBACA,OAAO,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO;YACpF;gBACE,OAAO;QACX;IACF;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;;;;;;IAG7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;;4BAAwB;4BAC/B,MAAM,MAAM;4BAAC;;;;;;;;;;;;;0BAKrB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;0CAEZ,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAU;;8CAEV,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;8CAEnC,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAW,CAAC,uBAAuB,EAAE,cAAc,eAAe,IAAI;;;;;;;;;;;;;;;;;kCAKvF,6LAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,cAAc,UAAU,kBAAkB;;0CACjG,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;kDAET,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;gDAA4B,OAAO,SAAS,KAAK;0DAC/C,SAAS,KAAK;+CADJ,SAAS,KAAK;;;;;;;;;;;;;;;;0CAOjC,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;kDAET,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;gDAA0B,OAAO,OAAO,KAAK;0DAC3C,OAAO,KAAK;+CADF,OAAO,KAAK;;;;;;;;;;;;;;;;0CAO/B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;kDAET,YAAY,GAAG,CAAC,CAAA,uBACf,6LAAC;gDAA0B,OAAO,OAAO,KAAK;0DAC3C,OAAO,KAAK;+CADF,OAAO,KAAK;;;;;;;;;;;;;;;;0CAO/B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,kDAAkD,EAC5D,aAAa,SACT,2BACA,2CACJ;0DAEF,cAAA,6LAAC,4MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,kDAAkD,EAC5D,aAAa,SACT,2BACA,2CACJ;0DAEF,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;;wBAAgB;wBACvB,YAAY,MAAM;wBAAC;wBACtB,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;wBACnC,oBAAoB,CAAC,IAAI,EAAE,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,mBAAmB,MAAM,CAAC,CAAC;;;;;;;;;;;;YAK3F,YAAY,MAAM,GAAG,kBACpB,6LAAC;gBAAI,WAAW,aAAa,SACzB,yDACA;0BAED,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,iIAAA,CAAA,UAAQ;wBAAgB,MAAM;uBAAhB,KAAK,GAAG;;;;;;;;;qCAI3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AASvC;GA/LwB;KAAA", "debugId": null}}]}