"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1092],{365:(e,t,s)=>{s.d(t,{default:()=>l});var r=s(2115);function l(){return(0,r.useEffect)(()=>{let e={},t=(e,t)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let s;new PerformanceObserver(s=>{let r=s.getEntries().find(e=>"first-contentful-paint"===e.name);r&&(e.fcp=r.startTime,t("FCP",r.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(s=>{let r=s.getEntries(),l=r[r.length-1];e.lcp=l.startTime,t("LCP",l.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(s=>{s.getEntries().forEach(s=>{e.fid=s.processingStart-s.startTime,t("FID",s.processingStart-s.startTime)})}).observe({entryTypes:["first-input"]}),s=0,new PerformanceObserver(r=>{r.getEntries().forEach(e=>{e.hadRecentInput||(s+=e.value)}),e.cls=s,t("CLS",s)}).observe({entryTypes:["layout-shift"]})}let s=performance.getEntriesByType("navigation")[0];if(s){let r=s.responseStart-s.requestStart;e.ttfb=r,t("TTFB",r)}let r=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",r),()=>{window.removeEventListener("beforeunload",r)}},[]),null}},2731:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5155);function l(e){let{size:t="md",className:s=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3467:(e,t,s)=>{s.d(t,{$g:()=>d,Ef:()=>c,Y$:()=>i,kX:()=>r,mV:()=>o,tF:()=>u,v4:()=>n,vS:()=>l});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},l=[{id:"free",title:"免费发布",description:r.FREE_LAUNCH.description,price:r.FREE_LAUNCH.displayPrice,features:r.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:r.PRIORITY_LAUNCH.description,price:r.PRIORITY_LAUNCH.displayPrice,features:r.PRIORITY_LAUNCH.features,recommended:!0}],a={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],i=[{value:a.FREE.value,label:a.FREE.label},{value:a.FREEMIUM.value,label:a.FREEMIUM.label},{value:a.PAID.value,label:a.PAID.label}],c=e=>{switch(e){case a.FREE.value:return a.FREE.color;case a.FREEMIUM.value:return a.FREEMIUM.color;case a.PAID.value:return a.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case a.FREE.value:return a.FREE.label;case a.FREEMIUM.value:return a.FREEMIUM.label;case a.PAID.value:return a.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4478:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5155);s(2115);var l=s(6874),a=s.n(l),n=s(365);let i=e=>{let{children:t}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-1",children:t}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(a(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(a(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(a(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},5131:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(5155),l=s(2115),a=s(4416),n=s(7924),i=s(3332),c=s(8146);function o(e){let{selectedTags:t,onTagsChange:s,maxTags:o=c.z}=e,[d,u]=(0,l.useState)(""),[m,x]=(0,l.useState)(!1),g=e=>{t.includes(e)?s(t.filter(t=>t!==e)):t.length<o&&s([...t,e])},h=e=>{s(t.filter(t=>t!==e))},b=c.MV.filter(e=>e.toLowerCase().includes(d.toLowerCase())&&!t.includes(e));return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"选择标签"}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["已选择 ",t.length,"/",o," 个标签"]})]}),t.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:"已选择的标签："}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[e,(0,r.jsx)("button",{type:"button",onClick:()=>h(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,r.jsx)(a.A,{className:"h-3 w-3"})})]},e))})]}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["选择标签（最多",o,"个）"]}),(0,r.jsxs)("div",{className:"relative mb-3",children:[(0,r.jsx)("input",{type:"text",placeholder:"搜索标签...",value:d,onChange:e=>u(e.target.value),onFocus:()=>x(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(n.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(m||d)&&(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:b.length>0?(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 gap-1",children:b.map(e=>{let s=t.length>=o;return(0,r.jsx)("button",{type:"button",onClick:()=>{g(e),u(""),x(!1)},disabled:s,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(s?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(i.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e]})},e)})}),b.length>50&&(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:["找到 ",b.length," 个匹配标签"]})]}):(0,r.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:d?"未找到匹配的标签":"开始输入以搜索标签"})})})]})}),(m||d)&&(0,r.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{x(!1),u("")}}),t.length>=o&&(0,r.jsxs)("p",{className:"text-sm text-amber-600",children:["最多只能选择",o,"个标签"]})]})}},6063:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5155),l=s(2115),a=s(2108),n=s(9911);function i(e){let{isOpen:t,onClose:s}=e,[i,c]=(0,l.useState)("method"),[o,d]=(0,l.useState)(""),[u,m]=(0,l.useState)(""),[x,g]=(0,l.useState)(!1),[h,b]=(0,l.useState)(""),p=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",s=document.createElement("div");s.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},y=()=>{c("method"),d(""),m(""),b(""),s()},f=async e=>{try{g(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{g(!1)}},v=async()=>{if(!o)return void b("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return void b("请输入有效的邮箱地址");b(""),g(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o})}),t=await e.json();t.success?(m(t.token),c("code"),p("验证码已发送，请查看您的邮箱")):p(t.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{g(!1)}},j=async e=>{if(6===e.length){g(!0);try{let t=await (0,a.signIn)("email-code",{email:o,code:e,token:u,redirect:!1});(null==t?void 0:t.ok)?(p("登录成功，欢迎回来！"),y()):p((null==t?void 0:t.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{g(!1)}}},N=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");if(s[e].value=t,t&&e<5){var r;null==(r=s[e+1])||r.focus()}let l=Array.from(s).map(e=>e.value).join("");6===l.length&&j(l)};return t?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:y}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,r.jsx)("button",{onClick:y,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(n.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===i&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>f("google"),disabled:x,children:[(0,r.jsx)(n.DSS,{}),"使用 Google 登录"]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>f("github"),disabled:x,children:[(0,r.jsx)(n.hL4,{}),"使用 GitHub 登录"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>c("email"),children:[(0,r.jsx)(n.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,r.jsx)("input",{type:"email",value:o,onChange:e=>d(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&v(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),h&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:h})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:v,disabled:x,children:x?"发送中...":"发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]}),"code"===i&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",o," 的6位验证码"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:t=>N(e,t.target.value),disabled:x,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("email"),children:"重新发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]})]})]})]}):null}},8146:(e,t,s)=>{s.d(t,{MV:()=>r,z:()=>l});let r=["AI助手","ChatGPT","对话AI","智能问答","语言模型","写作助手","内容生成","文案创作","博客写作","营销文案","图像生成","图像编辑","AI绘画","头像生成","背景移除","视频生成","视频编辑","视频剪辑","短视频制作","视频字幕","语音合成","语音识别","音乐生成","语音转文字","文字转语音","代码生成","代码补全","代码审查","开发助手","低代码平台","数据分析","数据可视化","商业智能","机器学习","深度学习","办公自动化","文档处理","项目管理","团队协作","笔记工具","UI设计","Logo设计","网页设计","平面设计","原型设计","SEO优化","社交媒体营销","邮件营销","内容营销","市场分析","机器翻译","实时翻译","文档翻译","语音翻译"],l=3},8331:(e,t,s)=>{s.d(t,{Bi:()=>n,ch:()=>l,xO:()=>a});let r=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],l=r.map(e=>({value:e.slug,label:e.name})),a=[{value:"",label:"所有分类"},...l],n=r.reduce((e,t)=>(e[t.slug]=t.name,e),{});r.reduce((e,t)=>(e[t.slug]=t,e),{}),r.map(e=>e.slug)}}]);