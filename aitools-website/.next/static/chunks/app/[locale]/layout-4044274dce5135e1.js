(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8450],{347:()=>{},848:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});var r=t(5155),l=t(2108);function a(e){let{children:s}=e;return(0,r.jsx)(l<PERSON>,{children:s})}},3800:(e,s,t)=>{var r={"./en.json":[7581,7581],"./zh.json":[9804,9804]};function l(e){if(!t.o(r,e))return Promise.resolve().then(()=>{var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s});var s=r[e],l=s[0];return t.e(s[1]).then(()=>t.t(l,19))}l.keys=()=>Object.keys(r),l.id=3800,e.exports=l},5071:(e,s,t)=>{Promise.resolve().then(t.bind(t,6096)),Promise.resolve().then(t.t.bind(t,6396,23)),Promise.resolve().then(t.t.bind(t,4556,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,6566)),Promise.resolve().then(t.bind(t,848)),Promise.resolve().then(t.bind(t,6214))},6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(5155),l=t(2115),a=t(2108),n=t(9911);function i(e){let{isOpen:s,onClose:t}=e,[i,o]=(0,l.useState)("method"),[c,d]=(0,l.useState)(""),[x,u]=(0,l.useState)(""),[m,h]=(0,l.useState)(!1),[g,b]=(0,l.useState)(""),p=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},f=()=>{o("method"),d(""),u(""),b(""),t()},j=async e=>{try{h(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{h(!1)}},y=async()=>{if(!c)return void b("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c))return void b("请输入有效的邮箱地址");b(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:c})}),s=await e.json();s.success?(u(s.token),o("code"),p("验证码已发送，请查看您的邮箱")):p(s.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}},v=async e=>{if(6===e.length){h(!0);try{let s=await (0,a.signIn)("email-code",{email:c,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(p("登录成功，欢迎回来！"),f()):p((null==s?void 0:s.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}}},N=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var r;null==(r=t[e+1])||r.focus()}let l=Array.from(t).map(e=>e.value).join("");6===l.length&&v(l)};return s?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:f}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,r.jsx)("button",{onClick:f,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(n.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===i&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>j("google"),disabled:m,children:[(0,r.jsx)(n.DSS,{}),"使用 Google 登录"]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>j("github"),disabled:m,children:[(0,r.jsx)(n.hL4,{}),"使用 GitHub 登录"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>o("email"),children:[(0,r.jsx)(n.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,r.jsx)("input",{type:"email",value:c,onChange:e=>d(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&y(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:y,disabled:m,children:m?"发送中...":"发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("method"),children:"返回"})]})]}),"code"===i&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",c," 的6位验证码"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:s=>N(e,s.target.value),disabled:m,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("email"),children:"重新发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("method"),children:"返回"})]})]})]})]})]}):null}},6214:(e,s,t)=>{"use strict";t.d(s,{LikeProvider:()=>o,X:()=>c});var r=t(5155),l=t(2115),a=t(2108);let n={liked:!1,likes:0,loading:!1},i=(0,l.createContext)(null);function o(e){let{children:s}=e,{data:t}=(0,a.useSession)(),[o,c]=(0,l.useState)({}),d=(0,l.useCallback)(e=>o[e]||n,[o]),x=(0,l.useCallback)(function(e,s){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];c(r=>r[e]?r:{...r,[e]:{liked:t,likes:s,loading:!1}})},[]),u=(0,l.useCallback)(async e=>{if(t)try{let s=await fetch("/api/tools/".concat(e,"/like"));if(s.ok){let t=await s.json();t.success&&c(s=>({...s,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[t]),m=(0,l.useCallback)(async function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t)return!1;c(s=>({...s,[e]:{...s[e]||n,loading:!0}}));try{let t=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s?{forceUnlike:!0}:{})});if(t.ok){let s=await t.json();if(s.success)return c(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}})),!0}return c(s=>({...s,[e]:{...s[e]||n,loading:!1}})),!1}catch(s){return console.error("Like request failed:",s),c(s=>({...s,[e]:{...s[e]||n,loading:!1}})),!1}},[t]);return(0,l.useEffect)(()=>{t?Object.keys(o).forEach(e=>{u(e)}):c(e=>{let s={};return Object.keys(e).forEach(t=>{s[t]={...e[t],liked:!1,loading:!1}}),s})},[t]),(0,r.jsx)(i.Provider,{value:{toolStates:o,toggleLike:m,getToolState:d,initializeToolState:x,refreshToolState:u},children:s})}function c(){let e=(0,l.useContext)(i);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},6566:(e,s,t)=>{"use strict";t.d(s,{default:()=>f});var r=t(5155),l=t(2115),a=t(6874),n=t.n(a),i=t(5695),o=t(7652),c=t(9911),d=t(2108),x=t(6063);function u(){var e,s,t,a,n,o,u,m,h,g,b;let{data:p,status:f}=(0,d.useSession)(),j=(0,i.useRouter)(),[y,v]=(0,l.useState)(!1),[N,k]=(0,l.useState)(!1),w=async()=>{await (0,d.signOut)({callbackUrl:"/"})},C=e=>{k(!1),j.push(e)};return"loading"===f?(0,r.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:"加载中..."}):p?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>k(!N),children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(e=p.user)?void 0:e.image)?(0,r.jsx)("img",{src:p.user.image,alt:p.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:(null==(t=p.user)||null==(s=t.name)?void 0:s.charAt(0))||"U"})}),(0,r.jsx)("span",{className:"text-sm hidden md:block",children:null==(a=p.user)?void 0:a.name}),(0,r.jsx)(c.Vr3,{className:"text-xs transition-transform ".concat(N?"rotate-180":"")})]}),N&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>k(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(n=p.user)?void 0:n.image)?(0,r.jsx)("img",{src:p.user.image,alt:p.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-lg font-medium text-gray-600",children:(null==(u=p.user)||null==(o=u.name)?void 0:o.charAt(0))||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:null==(m=p.user)?void 0:m.name}),(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:null==(h=p.user)?void 0:h.email}),(null==(g=p.user)?void 0:g.role)==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"})]})]})}),(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>C("/profile"),children:[(0,r.jsx)(c.x$1,{}),"个人资料"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>C("/profile/submitted"),children:[(0,r.jsx)(c.svy,{}),"我提交的工具"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>C("/profile/liked"),children:[(0,r.jsx)(c.Mbv,{}),"我的收藏"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>C("/submit"),children:[(0,r.jsx)(c.OiG,{}),"提交工具"]})]}),(null==(b=p.user)?void 0:b.role)==="admin"&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"border-t py-2",children:(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>C("/admin"),children:[(0,r.jsx)(c.Pcn,{}),"管理后台"]})})}),(0,r.jsxs)("div",{className:"border-t py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>C("/settings"),children:[(0,r.jsx)(c.Pcn,{}),"设置"]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",onClick:w,children:[(0,r.jsx)(c.axc,{}),"退出登录"]})]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>v(!0),children:[(0,r.jsx)(c.Zu,{}),"登录"]}),(0,r.jsx)(x.A,{isOpen:y,onClose:()=>v(!1)})]})}var m=t(1360);let h=["zh","en"],g={zh:"中文",en:"English"};(0,m.M6)(async e=>{let{locale:s}=e;return h.includes(s)||(0,i.notFound)(),{messages:(await t(3800)("./".concat(s,".json"))).default}});let b=e=>{let{children:s,href:t}=e;return(0,r.jsx)(n(),{href:t,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:s})},p=e=>{let{currentLocale:s}=e,[t,a]=(0,l.useState)(!1),n=(0,i.usePathname)(),o=(0,i.useRouter)(),d=e=>{let s=(n||"/").replace(/^\/[a-z]{2}/,"")||"/",t="zh"===e?s:"/".concat(e).concat(s);o.push(t),a(!1)};return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>a(!t),className:"flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors",children:[(0,r.jsx)(c.f35,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:g[s]})]}),t&&(0,r.jsx)("div",{className:"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50",children:h.map(e=>(0,r.jsx)("button",{onClick:()=>d(e),className:"block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ".concat(e===s?"bg-blue-50 text-blue-600":"text-gray-700"),children:g[e]},e))})]})};function f(){let[e,s]=(0,l.useState)(!1),t=(0,i.useRouter)(),a=(0,i.usePathname)(),d=(0,o.c3)("navigation"),x=(null==a?void 0:a.startsWith("/en"))?"en":"zh",m=e=>"zh"===x?e:"/en".concat(e),h=[{name:d("home"),href:m("/")},{name:d("tools"),href:m("/tools")},{name:d("categories"),href:m("/categories")},{name:d("submit"),href:m("/submit")}],g=e=>{e.preventDefault();let s=new FormData(e.currentTarget).get("search");s.trim()&&t.push(m("/search?q=".concat(encodeURIComponent(s.trim()))))};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("header",{className:"bg-white px-4 shadow-sm border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)(n(),{href:m("/"),className:"flex items-center space-x-2 hover:no-underline",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"zh"===x?"AI工具导航":"AI Tools"})]}),(0,r.jsx)("nav",{className:"hidden md:flex space-x-4",children:h.map(e=>(0,r.jsx)(b,{href:e.href,children:e.name},e.name))})]}),(0,r.jsx)("div",{className:"flex-1 max-w-md mx-8 hidden md:block",children:(0,r.jsx)("form",{onSubmit:g,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:d("search_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p,{currentLocale:x}),(0,r.jsx)(u,{}),(0,r.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>s(!e),"aria-label":"Open Menu",children:e?(0,r.jsx)(c.QCr,{}):(0,r.jsx)(c.OXb,{})})]})]}),e&&(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("nav",{className:"space-y-4",children:[h.map(e=>(0,r.jsx)(b,{href:e.href,children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)("form",{onSubmit:g,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:d("search_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})})]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[768,6711,6874,2108,8323,8441,1684,7358],()=>s(5071)),_N_E=e.O()}]);