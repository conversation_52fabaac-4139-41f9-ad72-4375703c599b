(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{1201:(e,s,t)=>{Promise.resolve().then(t.bind(t,7316))},5734:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(5155),l=t(646),a=t(4416);function i(e){let{message:s,onClose:t,className:i=""}=e;return(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(l.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:s})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(a.A,{className:"w-4 h-4"})})]})})}},7316:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(5155),l=t(2115),a=t(2108),i=t(5695),c=t(4478),n=t(2731),d=t(9783),o=t(5734),m=t(6063),x=t(3467),g=t(8331),u=t(9869),b=t(8164),h=t(1284),p=t(8146),j=t(5131);let f=g.ch,N=x.Y$;function v(){var e,s,t;let{data:x}=(0,a.useSession)(),g=(0,i.useRouter)(),[v,y]=(0,l.useState)({name:"",tagline:"",description:"",website:"",logo:"",category:"",tags:[],pricing:""}),[w,k]=(0,l.useState)(!1),[A,C]=(0,l.useState)("idle"),[S,O]=(0,l.useState)({}),[P,T]=(0,l.useState)(""),[E,I]=(0,l.useState)(!1),[_,G]=(0,l.useState)(null),[L,D]=(0,l.useState)(""),F=()=>{let e={};return v.name.trim()||(e.name="工具名称是必填项"),v.description.trim()||(e.description="工具描述是必填项"),v.website.trim()||(e.website="官方网站是必填项"),v.category||(e.category="请选择一个分类"),v.pricing||(e.pricing="请选择价格模式"),v.website&&!v.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),O(e),0===Object.keys(e).length},R=async e=>{if(e.preventDefault(),!x)return void I(!0);if(F()){k(!0),C("idle");try{let e=v.logo;if(_){let s=new FormData;s.append("logo",_);let t=await fetch("/api/upload/logo",{method:"POST",body:s}),r=await t.json();if(r.success)e=r.data.url;else throw Error(r.message||"Logo上传失败")}let s=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:v.name,tagline:v.tagline,description:v.description,website:v.website,logo:e||void 0,category:v.category,tags:v.tags,pricing:v.pricing})}),t=await s.json();t.success?g.push("/submit/launch-date/".concat(t.data.toolId)):(C("error"),T(t.message||"提交失败，请重试"))}catch(e){console.error("Error submitting tool:",e),C("error"),T("网络错误，请检查连接后重试")}finally{k(!1)}}};return(0,r.jsxs)(c.A,{children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,r.jsx)(u.A,{className:"inline-block mr-3 h-8 w-8 text-blue-600"}),"提交 AI 工具"]}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。"})]}),"success"===A&&(0,r.jsx)(o.A,{message:P||"工具提交成功！我们会在 1-3 个工作日内审核您的提交。",onClose:()=>C("idle"),className:"mb-6"}),"error"===A&&(0,r.jsx)(d.A,{message:P||"提交失败，请检查网络连接后重试。",onClose:()=>C("idle"),className:"mb-6"}),(0,r.jsxs)("form",{onSubmit:R,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,r.jsx)("input",{type:"text",value:v.name,onChange:e=>y(s=>({...s,name:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(S.name?"border-red-300":"border-gray-300"),placeholder:"例如：ChatGPT"}),S.name&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:S.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语（可选）"}),(0,r.jsx)("input",{type:"text",value:v.tagline,onChange:e=>y(s=>({...s,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"简短描述工具的核心价值"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"url",value:v.website,onChange:e=>y(s=>({...s,website:e.target.value})),className:"w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(S.website?"border-red-300":"border-gray-300"),placeholder:"https://example.com"}),(0,r.jsx)(b.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]}),S.website&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:S.website})]})}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,r.jsx)("textarea",{value:v.description,onChange:e=>y(s=>({...s,description:e.target.value})),rows:4,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(S.description?"border-red-300":"border-gray-300"),placeholder:"详细描述这个 AI 工具的功能和特点..."}),S.description&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:S.description})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Logo图片（可选）"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){G(t);let e=new FileReader;e.onload=e=>{var s;D(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(t)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),L&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("img",{src:L,alt:"Logo预览",className:"w-16 h-16 object-cover rounded-lg border border-gray-300"})})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB"})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,r.jsxs)("select",{value:v.category,onChange:e=>y(s=>({...s,category:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(S.category?"border-red-300":"border-gray-300"),children:[(0,r.jsx)("option",{value:"",children:"请选择分类"}),f.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),S.category&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:S.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,r.jsxs)("select",{value:v.pricing,onChange:e=>y(s=>({...s,pricing:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ".concat(S.pricing?"border-red-300":"border-gray-300"),children:[(0,r.jsx)("option",{value:"",children:"请选择价格模式"}),N.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),S.pricing&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:S.pricing})]})]})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(j.A,{selectedTags:v.tags,onTagsChange:e=>y(s=>({...s,tags:e})),maxTags:p.z})}),x&&(0,r.jsxs)("div",{className:"mb-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 mb-2",children:"提交者信息"}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:["提交者：",(null==(e=x.user)?void 0:e.name)||(null==(s=x.user)?void 0:s.email)]}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:["邮箱：",null==(t=x.user)?void 0:t.email]})]}),(0,r.jsx)("div",{className:"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"提交指南"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• 请确保提交的是真实存在且可正常访问的 AI 工具"}),(0,r.jsx)("li",{children:"• 工具描述应该准确、客观，避免过度营销"}),(0,r.jsx)("li",{children:"• 我们会在 1-3 个工作日内审核您的提交"}),(0,r.jsx)("li",{children:"• 审核通过后，工具将出现在我们的目录中"}),(0,r.jsx)("li",{children:"• 如有问题，我们会通过邮箱联系您"})]})]})]})}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{type:"submit",disabled:w,className:"px-8 py-3 rounded-lg font-medium transition-colors ".concat(w?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:w?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{size:"sm",className:"mr-2"}),"提交中..."]}):"提交工具"})})]})]}),(0,r.jsx)(m.A,{isOpen:E,onClose:()=>I(!1)})]})}},9783:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(5155),l=t(5339),a=t(4416);function i(e){let{message:s,onClose:t,className:i=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(i),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(l.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:s})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(a.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,2556,1092,8441,1684,7358],()=>s(1201)),_N_E=e.O()}]);