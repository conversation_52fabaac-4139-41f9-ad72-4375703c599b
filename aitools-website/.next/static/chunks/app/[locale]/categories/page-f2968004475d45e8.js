(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5534],{365:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(2115);function l(){return(0,s.useEffect)(()=>{let e={},t=(e,t)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let r;new PerformanceObserver(r=>{let s=r.getEntries().find(e=>"first-contentful-paint"===e.name);s&&(e.fcp=s.startTime,t("FCP",s.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(r=>{let s=r.getEntries(),l=s[s.length-1];e.lcp=l.startTime,t("LCP",l.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(r=>{r.getEntries().forEach(r=>{e.fid=r.processingStart-r.startTime,t("FID",r.processingStart-r.startTime)})}).observe({entryTypes:["first-input"]}),r=0,new PerformanceObserver(s=>{s.getEntries().forEach(e=>{e.hadRecentInput||(r+=e.value)}),e.cls=r,t("CLS",r)}).observe({entryTypes:["layout-shift"]})}let r=performance.getEntriesByType("navigation")[0];if(r){let s=r.responseStart-r.requestStart;e.ttfb=s,t("TTFB",s)}let s=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",s),()=>{window.removeEventListener("beforeunload",s)}},[]),null}},3109:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3850:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(5155);r(2115);var l=r(6874),a=r.n(l);let n=e=>{let{category:t}=e;return(0,s.jsx)(a(),{href:"/categories/".concat(t.slug),children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:t.color||"#3B82F6"},children:(0,s.jsx)("span",{className:"text-white",children:t.icon||"\uD83D\uDD27"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[t.toolCount," 个工具"]})]})]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:t.description})]})})})};var i=r(9783),c=r(4653),d=r(3109);function o(e){let{categories:t,error:r}=e;if(r)return(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsx)(i.A,{message:r})});let l=t.sort((e,t)=>t.toolCount-e.toolCount).slice(0,6);return(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:[(0,s.jsx)(c.A,{className:"inline-block mr-3 h-10 w-10 text-blue-600"}),"AI 工具分类"]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"按功能分类浏览我们精选的 AI 工具集合。每个分类都包含经过验证的高质量工具，帮助您快速找到所需的解决方案。"})]}),(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-100 rounded-lg p-8 mb-12",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:t.length}),(0,s.jsx)("div",{className:"text-gray-700",children:"个分类"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:t.reduce((e,t)=>e+t.toolCount,0)}),(0,s.jsx)("div",{className:"text-gray-700",children:"个工具"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:t.length>0?Math.round(t.reduce((e,t)=>e+t.toolCount,0)/t.length):0}),(0,s.jsx)("div",{className:"text-gray-700",children:"平均每分类工具数"})]})]})}),(0,s.jsxs)("section",{className:"mb-16",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"热门分类"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:l.map(e=>(0,s.jsx)(n,{category:e},e._id))})]}),(0,s.jsxs)("section",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"所有分类"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[t.length," 个分类"]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:t.map(e=>(0,s.jsx)(n,{category:e},e._id))})]}),(0,s.jsxs)("section",{className:"mt-16 bg-blue-600 rounded-lg p-8 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"没有找到您需要的分类？"}),(0,s.jsx)("p",{className:"text-blue-100 mb-6",children:"我们持续添加新的工具和分类。如果您有建议或想要提交新工具，请联系我们。"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)("a",{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition-colors",children:"提交新工具"}),(0,s.jsx)("a",{href:"/contact",className:"inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white hover:bg-blue-700 transition-colors",children:"联系我们"})]})]})]})}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4653:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5339:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8201:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,3850)),Promise.resolve().then(r.bind(r,365))},9783:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(5155),l=r(5339),a=r(4416);function n(e){let{message:t,onClose:r,className:n=""}=e;return(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,s.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,s.jsx)(a.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var s=r(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:n,className:o="",children:x,iconNode:m,...h}=e;return(0,s.createElement)("svg",{ref:t,...d,width:l,height:l,stroke:r,strokeWidth:n?24*Number(a)/Number(l):a,className:i("lucide",o),...!x&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let r=(0,s.forwardRef)((r,a)=>{let{className:c,...d}=r;return(0,s.createElement)(o,{ref:a,iconNode:t,className:i("lucide-".concat(l(n(e))),"lucide-".concat(e),c),...d})});return r.displayName=n(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(8201)),_N_E=e.O()}]);