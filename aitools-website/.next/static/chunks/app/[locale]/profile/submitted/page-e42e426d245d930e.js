(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1569],{855:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(5155),r=t(2115),l=t(2108),n=t(5695),c=t(6874),d=t.n(c),i=t(4478),x=t(2731),o=t(9783),h=t(646),m=t(4186),u=t(4861),p=t(3717),g=t(7550),j=t(4616),b=t(2713),N=t(2657),y=t(9074),f=t(3786);let v=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"approved":return"已通过";case"pending":return"审核中";case"rejected":return"已拒绝";case"draft":return"草稿";default:return e}},A=e=>{switch(e){case"approved":return(0,a.jsx)(h.A,{className:"h-4 w-4"});case"pending":return(0,a.jsx)(m.A,{className:"h-4 w-4"});case"rejected":return(0,a.jsx)(u.A,{className:"h-4 w-4"});case"draft":return(0,a.jsx)(p.A,{className:"h-4 w-4"});default:return null}};function k(){let{data:e,status:s}=(0,l.useSession)(),t=(0,n.useRouter)(),[c,m]=(0,r.useState)("all"),[u,k]=(0,r.useState)([]),[D,M]=(0,r.useState)(!0),[S,_]=(0,r.useState)("");(0,r.useEffect)(()=>{if("unauthenticated"===s)return void t.push("/");"authenticated"===s&&C()},[s,t]);let C=async()=>{try{M(!0),_("");let e=await fetch("/api/user/tools"),s=await e.json();s.success&&s.data?k(s.data.tools):_(s.message||"获取工具列表失败")}catch(e){_("网络错误，请重试")}finally{M(!1)}},L=u.filter(e=>"all"===c||e.status===c),P={total:u.length,draft:u.filter(e=>"draft"===e.status).length,approved:u.filter(e=>"approved"===e.status).length,pending:u.filter(e=>"pending"===e.status).length,rejected:u.filter(e=>"rejected"===e.status).length,totalViews:u.reduce((e,s)=>e+s.views,0),totalLikes:u.reduce((e,s)=>e+s.likes,0)};return"loading"===s||D?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(x.A,{size:"lg",className:"py-20"})})}):e?(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(d(),{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(g.A,{className:"h-5 w-5"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"我提交的AI工具"})]}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"管理您提交的所有AI工具"})]}),(0,a.jsxs)(d(),{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"mr-2 h-5 w-5"}),"提交新工具"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(b.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总提交数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.total})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.approved})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(N.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.totalViews})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总点赞数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P.totalLikes})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)("button",{onClick:()=>m("all"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("all"===c?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["全部 (",P.total,")"]}),(0,a.jsxs)("button",{onClick:()=>m("draft"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("draft"===c?"bg-gray-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["草稿 (",P.draft,")"]}),(0,a.jsxs)("button",{onClick:()=>m("approved"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("approved"===c?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已通过 (",P.approved,")"]}),(0,a.jsxs)("button",{onClick:()=>m("pending"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("pending"===c?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["审核中 (",P.pending,")"]}),(0,a.jsxs)("button",{onClick:()=>m("rejected"),className:"px-4 py-2 rounded-lg text-sm font-medium transition-colors ".concat("rejected"===c?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["已拒绝 (",P.rejected,")"]})]})}),S&&(0,a.jsx)(o.A,{message:S,onClose:()=>_(""),className:"mb-6"}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:L.length>0?(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:L.map(e=>(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(v(e.status)),children:[A(e.status),(0,a.jsx)("span",{className:"ml-1",children:w(e.status)})]})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["提交于 ",new Date(e.submittedAt).toLocaleDateString("zh-CN")]})]}),e.launchDate&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["发布于 ",new Date(e.launchDate).toLocaleDateString("zh-CN")]})]}),"approved"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[e.views," 浏览"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:"❤️"}),(0,a.jsxs)("span",{children:[e.likes," 点赞"]})]})]})]}),"rejected"===e.status&&e.reviewNotes&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-red-800",children:[(0,a.jsx)("strong",{children:"拒绝原因："})," ",e.reviewNotes]})}),"draft"===e.status&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,a.jsxs)("p",{className:"text-sm text-blue-800 mb-2",children:[(0,a.jsx)("strong",{children:"下一步："})," 选择发布日期"]}),(0,a.jsx)(d(),{href:"/submit/launch-date/".concat(e._id),className:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors",children:"选择发布日期"})]}),"pending"===e.status&&e.launchOption&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,a.jsx)("span",{children:(0,a.jsx)("strong",{children:"发布选项："})}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"),children:"paid"===e.launchOption?"优先发布":"免费发布"})]}),e.selectedLaunchDate&&(0,a.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,a.jsx)("span",{children:(0,a.jsx)("strong",{children:"计划发布："})}),(0,a.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh-CN")})]}),e.paymentRequired&&(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{children:(0,a.jsx)("strong",{children:"支付状态："})}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("completed"===e.paymentStatus?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"completed"===e.paymentStatus?"已支付":"待支付"})]}),(0,a.jsx)("div",{className:"flex justify-end mt-2",children:(0,a.jsxs)(d(),{href:"/submit/edit-launch-date/".concat(e._id),className:"inline-flex items-center px-3 py-1 bg-yellow-600 text-white text-xs rounded-lg hover:bg-yellow-700 transition-colors",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"修改发布日期"]})})]})}),"approved"===e.status&&e.launchOption&&(0,a.jsx)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsxs)("div",{className:"text-sm text-green-800",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,a.jsx)("span",{children:(0,a.jsx)("strong",{children:"发布选项："})}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"),children:"paid"===e.launchOption?"优先发布":"免费发布"})]}),e.selectedLaunchDate&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{children:(0,a.jsx)("strong",{children:"发布日期："})}),(0,a.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh-CN")})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,a.jsx)(d(),{href:"/tools/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"查看详情",children:(0,a.jsx)(N.A,{className:"h-5 w-5"})}),"draft"===e.status&&!e.launchDateSelected&&(0,a.jsx)(d(),{href:"/submit/launch-date/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"设定发布日期",children:(0,a.jsx)(y.A,{className:"h-5 w-5"})}),["pending","approved"].includes(e.status)&&e.launchDateSelected&&(0,a.jsx)(d(),{href:"/submit/edit-launch-date/".concat(e._id),className:"p-2 text-gray-400 hover:text-orange-600 transition-colors",title:"修改发布日期",children:(0,a.jsx)(y.A,{className:"h-5 w-5"})}),(0,a.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:"访问网站",children:(0,a.jsx)(f.A,{className:"h-5 w-5"})}),["draft","pending","rejected","approved","published"].includes(e.status)&&(0,a.jsx)(d(),{href:"/submit/edit/".concat(e._id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"approved"===e.status&&e.launchDate&&new Date(e.launchDate)<=new Date?"编辑基础信息":"approved"===e.status?"编辑基础信息（不可修改URL）":"编辑工具信息",children:(0,a.jsx)(p.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(b.A,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===c?"还没有提交任何工具":"没有".concat(w(c),"的工具")}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===c?"开始提交您的第一个 AI 工具吧！":"尝试选择其他状态查看工具"}),"all"===c&&(0,a.jsxs)(d(),{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"提交工具"]})]})})]})}):null}},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3786:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"notFound")&&t.d(s,{notFound:function(){return a.notFound}}),t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},6316:(e,s,t)=>{Promise.resolve().then(t.bind(t,855))},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,2108,7350,8441,1684,7358],()=>s(6316)),_N_E=e.O()}]);