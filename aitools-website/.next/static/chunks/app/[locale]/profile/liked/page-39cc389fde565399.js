(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2797],{2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(5155);function a(e){let{size:s="md",className:t=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},2839:(e,s,t)=>{"use strict";t.d(s,{u:()=>d});var r=t(9509);function a(){if(r.env.NEXT_PUBLIC_APP_URL)return r.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:s,port:t}=window.location;return"".concat(e,"//").concat(s).concat(t?":".concat(t):"")}}function l(){if(r.env.NEXT_PUBLIC_API_BASE_URL)return r.env.NEXT_PUBLIC_API_BASE_URL;let e=a();return"".concat(e,"/api")}function n(){return"production"}function c(){return"development"===n()}a(),l(),r.env.NEXTAUTH_URL?r.env.NEXTAUTH_URL:a(),n(),c(),n(),window.location.port||window.location.protocol,c();let o=l();class i{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&s.append(t,r.toString())});let t=s.toString();return this.request("/tools".concat(t?"?".concat(t):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&s.append(t,r.toString())});let t=s.toString();return this.request("/user/liked-tools".concat(t?"?".concat(t):""))}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&s.append(t,r.toString())});let t=s.toString();return this.request("/admin/tools".concat(t?"?".concat(t):""))}async approveTool(e,s){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=o){this.baseURL=e}}let d=new i},3979:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(5155),a=t(2115),l=t(2108),n=t(5695),c=t(6874),o=t.n(c),i=t(4478),d=t(2731),u=t(9783),m=t(7797),x=t(2839),h=t(8331),g=t(7550),p=t(7924),f=t(6932),y=t(1976);let j=h.Bi;function b(){let{data:e,status:s}=(0,l.useSession)(),t=(0,n.useRouter)(),[c,h]=(0,a.useState)([]),[b,v]=(0,a.useState)([]),[N,A]=(0,a.useState)(!0),[w,k]=(0,a.useState)(""),[P,S]=(0,a.useState)(""),[T,E]=(0,a.useState)("all");(0,a.useEffect)(()=>{if("unauthenticated"===s)return void t.push("/");"authenticated"===s&&I()},[s,t]),(0,a.useEffect)(()=>{L()},[c,P,T]);let I=async()=>{try{A(!0),k("");let e=await x.u.getLikedTools({limit:100});e.success&&e.data?h(e.data.tools):k(e.error||"获取收藏列表失败")}catch(e){k("网络错误，请重试")}finally{A(!1)}},L=()=>{let e=c;P&&(e=e.filter(e=>e.name.toLowerCase().includes(P.toLowerCase())||e.description.toLowerCase().includes(P.toLowerCase()))),"all"!==T&&(e=e.filter(e=>e.category===T)),v(e)},_=async e=>{try{h(s=>s.filter(s=>s._id!==e)),setTimeout(()=>{I()},500)}catch(e){console.error("Error unliking tool:",e),I()}},C=Array.from(new Set(c.map(e=>e.category)));return"loading"===s||N?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(d.A,{size:"lg",className:"py-20"})})}):e?(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(o(),{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"我的收藏"})]}),(0,r.jsxs)("p",{className:"text-lg text-gray-600",children:["您收藏的AI工具 (",c.length,")"]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"搜索收藏的工具...",value:P,onChange:e=>S(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,r.jsx)("div",{className:"sm:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsxs)("select",{value:T,onChange:e=>E(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"all",children:"所有分类"}),C.map(e=>(0,r.jsx)("option",{value:e,children:j[e]||e},e))]})]})})]})}),w&&(0,r.jsx)(u.A,{message:w,onClose:()=>k(""),className:"mb-6"}),b.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>(0,r.jsx)(m.default,{tool:e,onUnlike:_,isInLikedPage:!0},e._id))}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(y.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:P||"all"!==T?"没有找到匹配的工具":"还没有收藏任何工具"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:P||"all"!==T?"尝试调整搜索条件或筛选器":"开始探索并收藏您喜欢的AI工具吧！"}),!P&&"all"===T&&(0,r.jsx)(o(),{href:"/tools",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:"浏览工具"})]})]})}):null}},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4478:(e,s,t)=>{"use strict";t.d(s,{A:()=>c});var r=t(5155);t(2115);var a=t(6874),l=t.n(a),n=t(365);let c=e=>{let{children:s}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-1",children:s}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4884:(e,s,t)=>{Promise.resolve().then(t.bind(t,3979))},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"notFound")&&t.d(s,{notFound:function(){return r.notFound}}),t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},6932:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8331:(e,s,t)=>{"use strict";t.d(s,{Bi:()=>n,ch:()=>a,xO:()=>l});let r=[{slug:"text-generation",name:"文本生成",description:"利用AI技术生成高质量文本内容，包括文章、邮件、创意写作等",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",name:"图像生成",description:"使用AI创建和编辑图像，包括艺术创作、图片增强、风格转换等",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",name:"代码生成",description:"智能代码生成和编程辅助工具，提高开发效率",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",name:"数据分析",description:"数据分析和可视化工具，帮助洞察数据价值",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",name:"音频处理",description:"音频处理、语音合成、音乐生成等音频AI工具",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",name:"视频编辑",description:"视频生成、编辑、剪辑等视频处理AI工具",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",name:"语言翻译",description:"多语言翻译和本地化AI工具",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",name:"搜索引擎",description:"智能搜索和信息检索AI工具",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",name:"教育学习",description:"教育培训和学习辅助AI工具",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",name:"营销工具",description:"数字营销和推广AI工具",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",name:"生产力工具",description:"提高工作效率的AI工具",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",name:"客户服务",description:"客户支持和服务AI工具",icon:"\uD83C\uDFA7",color:"#F59E0B"}],a=r.map(e=>({value:e.slug,label:e.name})),l=[{value:"",label:"所有分类"},...a],n=r.reduce((e,s)=>(e[s.slug]=s.name,e),{});r.reduce((e,s)=>(e[s.slug]=s,e),{}),r.map(e=>e.slug)},9783:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(5155),a=t(5339),l=t(4416);function n(e){let{message:s,onClose:t,className:n=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:s})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>s(4884)),_N_E=e.O()}]);