(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[810,4477,4638,8111],{365:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var n=r(2115);function s(){return(0,n.useEffect)(()=>{let e={},t=(e,t)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let r;new PerformanceObserver(r=>{let n=r.getEntries().find(e=>"first-contentful-paint"===e.name);n&&(e.fcp=n.startTime,t("FCP",n.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(r=>{let n=r.getEntries(),s=n[n.length-1];e.lcp=s.startTime,t("LCP",s.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(r=>{r.getEntries().forEach(r=>{e.fid=r.processingStart-r.startTime,t("FID",r.processingStart-r.startTime)})}).observe({entryTypes:["first-input"]}),r=0,new PerformanceObserver(n=>{n.getEntries().forEach(e=>{e.hadRecentInput||(r+=e.value)}),e.cls=r,t("CLS",r)}).observe({entryTypes:["layout-shift"]})}let r=performance.getEntriesByType("navigation")[0];if(r){let n=r.responseStart-r.requestStart;e.ttfb=n,t("TTFB",n)}let n=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",n),()=>{window.removeEventListener("beforeunload",n)}},[]),null}},8957:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,365))}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(8957)),_N_E=e.O()}]);