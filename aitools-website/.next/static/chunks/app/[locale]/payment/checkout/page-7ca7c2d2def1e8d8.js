(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6577],{365:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(2115);function a(){return(0,r.useEffect)(()=>{let e={},t=(e,t)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let s;new PerformanceObserver(s=>{let r=s.getEntries().find(e=>"first-contentful-paint"===e.name);r&&(e.fcp=r.startTime,t("FCP",r.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(s=>{let r=s.getEntries(),a=r[r.length-1];e.lcp=a.startTime,t("LCP",a.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(s=>{s.getEntries().forEach(s=>{e.fid=s.processingStart-s.startTime,t("FID",s.processingStart-s.startTime)})}).observe({entryTypes:["first-input"]}),s=0,new PerformanceObserver(r=>{r.getEntries().forEach(e=>{e.hadRecentInput||(s+=e.value)}),e.cls=s,t("CLS",s)}).observe({entryTypes:["layout-shift"]})}let s=performance.getEntriesByType("navigation")[0];if(s){let r=s.responseStart-s.requestStart;e.ttfb=r,t("TTFB",r)}let r=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",r),()=>{window.removeEventListener("beforeunload",r)}},[]),null}},646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1586:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},3467:(e,t,s)=>{"use strict";s.d(t,{$g:()=>o,Ef:()=>i,Y$:()=>c,kX:()=>r,mV:()=>d,tF:()=>m,v4:()=>n,vS:()=>a});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:r.FREE_LAUNCH.description,price:r.FREE_LAUNCH.displayPrice,features:r.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:r.PRIORITY_LAUNCH.description,price:r.PRIORITY_LAUNCH.displayPrice,features:r.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],c=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],i=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},d=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},o=e=>0===e?"免费":"\xa5".concat(e),m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4478:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(5155);s(2115);var a=s(6874),l=s.n(a),n=s(365);let c=e=>{let{children:t}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-1",children:t}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4530:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(5155),a=s(2115),l=s(5695),n=s(2108),c=s(7368),i=s(5855),d=s(4478);let o=(0,s(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var m=s(1586),u=s(3467);function x(e){let{onSuccess:t,amount:s}=e,l=(0,i.useStripe)(),n=(0,i.useElements)(),[c,d]=(0,a.useState)(!1),[x,h]=(0,a.useState)(""),b=async e=>{if(e.preventDefault(),l&&n){d(!0),h("");try{let{error:e}=await l.confirmPayment({elements:n,confirmParams:{return_url:"".concat(window.location.origin,"/submit/success")},redirect:"if_required"});e?"card_error"===e.type||"validation_error"===e.type?h(e.message||"支付失败"):h("支付过程中发生错误，请重试"):t()}catch(e){h("支付处理失败，请重试")}finally{d(!1)}}};return(0,r.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"支付方式"}),(0,r.jsx)(i.PaymentElement,{options:{layout:"tabs",defaultValues:{billingDetails:{address:{country:"CN"}}}}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"账单地址"}),(0,r.jsx)(i.AddressElement,{options:{mode:"billing",defaultValues:{address:{country:"CN"}}}})]}),x&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-red-600 text-sm",children:x})}),(0,r.jsx)("button",{type:"submit",disabled:!l||!n||c,className:"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o,{className:"h-5 w-5 mr-2 animate-spin"}),"处理支付中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"立即支付 ",(0,u.tF)(s)]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:"\uD83D\uDD12 您的支付信息通过SSL加密传输，安全可靠"})})]})}var h=s(5339),b=s(646),p=s(5525);let f=(0,c.c)("pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM");function y(){var e;let t=(0,l.useRouter)(),s=(0,l.useSearchParams)(),{data:c,status:o}=(0,n.useSession)(),[y,j]=(0,a.useState)(null),[g,v]=(0,a.useState)(!0),[N,w]=(0,a.useState)(""),[E,A]=(0,a.useState)(""),P=s.get("orderId");(0,a.useEffect)(()=>{if("unauthenticated"===o)return void t.push("/");"authenticated"===o&&P&&I()},[o,P]);let I=async()=>{try{let e=await fetch("/api/orders/".concat(P)),s=await e.json();if(s.success){if(j(s.data),"completed"===s.data.status)return void t.push("/submit/success?toolId=".concat(s.data.toolId));if("pending"!==s.data.status)return void w("订单状态异常");await F()}else w("订单不存在")}catch(e){w("获取订单信息失败")}finally{v(!1)}},F=async()=>{try{let e=await fetch("/api/stripe/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:P})}),t=await e.json();t.success?A(t.data.clientSecret):w(t.message||"创建支付失败")}catch(e){w("创建支付失败")}};return g?(0,r.jsx)(d.A,{children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"加载订单信息..."})]})})}):N?(0,r.jsx)(d.A,{children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(h.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"支付出错"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:N}),(0,r.jsx)("button",{onClick:()=>t.push("/submit"),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"返回提交页面"})]})})}):y?(0,r.jsx)(d.A,{children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(m.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"完成支付"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"为您的工具选择优先发布服务"})]}),(0,r.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"订单详情"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"服务类型"}),(0,r.jsx)("span",{className:"font-medium",children:"工具优先发布"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"工具名称"}),(0,r.jsx)("span",{className:"font-medium",children:(null==(e=y.tool)?void 0:e.name)||"加载中..."})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"发布日期"}),(0,r.jsx)("span",{className:"font-medium",children:new Date(y.selectedLaunchDate).toLocaleDateString("zh-CN")})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"订单号"}),(0,r.jsx)("span",{className:"font-medium text-sm",children:y._id})]}),(0,r.jsx)("hr",{className:"my-4"}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,r.jsx)("span",{children:"总计"}),(0,r.jsx)("span",{className:"text-blue-600",children:(0,u.tF)(y.amount)})]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"优先发布服务包含："}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),"可选择任意发布日期"]}),(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),"优先审核处理（1个工作日内）"]}),(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),"首页推荐位置展示"]}),(0,r.jsxs)("li",{className:"flex items-center text-blue-800",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2 flex-shrink-0"}),"专属客服支持"]})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 mr-2 text-green-500"}),(0,r.jsx)("span",{className:"text-sm",children:"您的支付信息受到银行级别的安全保护"})]})}),E&&(0,r.jsx)(i.Elements,{stripe:f,options:{clientSecret:E,appearance:{theme:"stripe",variables:{colorPrimary:"#2563eb"}}},children:(0,r.jsx)(x,{onSuccess:()=>{t.push("/submit/success?toolId=".concat(y.toolId,"&paid=true"))},amount:y.amount})}),N&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-red-600 text-sm mt-4",children:N})}),(0,r.jsx)("p",{className:"text-gray-500 text-sm mt-4 text-center",children:"点击支付即表示您同意我们的服务条款和隐私政策"})]})}):null}},5074:(e,t,s)=>{Promise.resolve().then(s.bind(s,4530))},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5525:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"notFound")&&s.d(t,{notFound:function(){return r.notFound}}),s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:o="",children:m,iconNode:u,...x}=e;return(0,r.createElement)("svg",{ref:t,...d,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:c("lucide",o),...!m&&!i(x)&&{"aria-hidden":"true"},...x},[...u.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:i,...d}=s;return(0,r.createElement)(o,{ref:l,iconNode:t,className:c("lucide-".concat(a(n(e))),"lucide-".concat(e),i),...d})});return s.displayName=n(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,2108,1834,8441,1684,7358],()=>t(5074)),_N_E=e.O()}]);