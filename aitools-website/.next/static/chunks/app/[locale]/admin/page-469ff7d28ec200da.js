(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6089],{543:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(5155),r=s(2115),n=s(5695),l=s(4478),c=s(2731),i=s(9783),o=s(5734),d=s(2839),x=s(4186),m=s(646),u=s(4861),h=s(5525),g=s(7924),p=s(6932),y=s(1243),j=s(1007),f=s(9074),v=s(3786);let N={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},b={free:"免费",freemium:"免费增值",paid:"付费"};function w(){let e=(0,n.useRouter)(),[t,s]=(0,r.useState)([]),[w,A]=(0,r.useState)(!0),[k,S]=(0,r.useState)(""),[P,T]=(0,r.useState)(""),[C,_]=(0,r.useState)(""),[L,E]=(0,r.useState)("pending"),[q,U]=(0,r.useState)(null),[R,O]=(0,r.useState)(!1),[M,B]=(0,r.useState)(""),[D,I]=(0,r.useState)(!1);(0,r.useEffect)(()=>{z()},[L]);let z=async()=>{try{A(!0),S("");let e=await d.u.getAdminTools({status:"all"===L?void 0:L,limit:50});e.success&&e.data?s(e.data.tools):S(e.error||"获取工具列表失败")}catch(e){S("网络错误，请重试")}finally{A(!1)}},H=t.filter(e=>{let t=e.name.toLowerCase().includes(C.toLowerCase())||e.description.toLowerCase().includes(C.toLowerCase()),s="all"===L||e.status===L;return t&&s}),X=async e=>{try{I(!0),S("");let t=await d.u.approveTool(e,{reviewedBy:"admin",reviewNotes:"审核通过",launchDate:new Date().toISOString()});t.success?(T("工具审核通过！"),await z()):S(t.error||"审核操作失败")}catch(e){S("网络错误，请重试")}finally{I(!1)}},F=async(e,t)=>{try{I(!0),S("");let s=await d.u.rejectTool(e,{reviewedBy:"admin",rejectReason:t});s.success?(T("工具已拒绝！"),await z(),O(!1),B(""),U(null)):S(s.error||"拒绝操作失败")}catch(e){S("网络错误，请重试")}finally{I(!1)}},J=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),V=e=>{switch(e){case"pending":return(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,a.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"待审核"]});case"approved":return(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,a.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"已批准"]});case"rejected":return(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,a.jsx)(u.A,{className:"w-3 h-3 mr-1"}),"已拒绝"]});default:return null}};return w?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(c.A,{size:"lg",className:"py-20"})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,a.jsx)(h.A,{className:"mr-3 h-8 w-8 text-blue-600"}),"管理员审核中心"]}),(0,a.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"审核和管理用户提交的 AI 工具"})]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:t.filter(e=>"pending"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"待审核"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.filter(e=>"approved"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"已批准"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:t.filter(e=>"rejected"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"已拒绝"})]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或提交者...",value:C,onChange:e=>_(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,a.jsxs)("select",{value:L,onChange:e=>E(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none",children:[(0,a.jsx)("option",{value:"all",children:"所有状态"}),(0,a.jsx)("option",{value:"pending",children:"待审核"}),(0,a.jsx)("option",{value:"approved",children:"已批准"}),(0,a.jsx)("option",{value:"rejected",children:"已拒绝"})]})]})})]})}),P&&(0,a.jsx)(o.A,{message:P,onClose:()=>T(""),className:"mb-6"}),k&&(0,a.jsx)(i.A,{message:k,onClose:()=>S(""),className:"mb-6"}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===H.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(y.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"没有找到工具"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:C||"all"!==L?"尝试调整搜索条件或筛选器":"暂无待审核的工具"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:H.map(t=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("img",{src:t.logo,alt:t.name,className:"w-12 h-12 rounded-lg object-cover border border-gray-200"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 truncate",children:t.name}),V(t.status),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:N[t.category]}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:b[t.pricing]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:t.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 mr-1"}),t.submittedBy]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-1"}),J(t.submittedAt)]}),t.launchDate&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-1"}),"已发布: ",new Date(t.launchDate).toLocaleDateString("zh-CN")]})]}),t.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-3",children:t.tags.map((e,t)=>(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700",children:e},t))})]})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"访问网站",children:(0,a.jsx)(v.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>e.push("/admin/tools/".concat(t._id)),className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"查看详情",children:"查看详情"}),"pending"===t.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>X(t._id),disabled:D,className:"px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:D?"处理中...":"批准"}),(0,a.jsx)("button",{onClick:()=>{U(t._id),O(!0)},disabled:D,className:"px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"拒绝"})]})]})]})},t._id))})}),R&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请说明拒绝的原因，这将帮助提交者改进他们的提交。"}),(0,a.jsx)("textarea",{value:M,onChange:e=>B(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入拒绝原因..."}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>{O(!1),B(""),U(null)},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",children:"取消"}),(0,a.jsx)("button",{onClick:()=>q&&F(q,M),disabled:!M.trim()||D,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:D?"处理中...":"确认拒绝"})]})]})})]})})}},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2114:(e,t,s)=>{Promise.resolve().then(s.bind(s,543))},2839:(e,t,s)=>{"use strict";s.d(t,{u:()=>d});var a=s(9509);function r(){if(a.env.NEXT_PUBLIC_APP_URL)return a.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:s}=window.location;return"".concat(e,"//").concat(t).concat(s?":".concat(s):"")}}function n(){if(a.env.NEXT_PUBLIC_API_BASE_URL)return a.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return"".concat(e,"/api")}function l(){return"production"}function c(){return"development"===l()}r(),n(),a.env.NEXTAUTH_URL?a.env.NEXTAUTH_URL:r(),l(),c(),l(),window.location.port||window.location.protocol,c();let i=n();class o{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...t.headers},...t},r=await fetch(s,a),n=await r.json();if(!r.ok)throw Error(n.error||"HTTP error! status: ".concat(r.status));return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=i){this.baseURL=e}}let d=new o},3786:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},5525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"notFound")&&s.d(t,{notFound:function(){return a.notFound}}),s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},5734:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(5155),r=s(646),n=s(4416);function l(e){let{message:t,onClose:s,className:l=""}=e;return(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(l),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-green-800 text-sm",children:t})}),s&&(0,a.jsx)("button",{onClick:s,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(n.A,{className:"w-4 h-4"})})]})})}},6932:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,7350,8441,1684,7358],()=>t(2114)),_N_E=e.O()}]);