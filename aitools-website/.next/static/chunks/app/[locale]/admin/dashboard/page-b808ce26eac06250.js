(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4926],{2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2839:(e,s,t)=>{"use strict";t.d(s,{u:()=>o});var a=t(9509);function l(){if(a.env.NEXT_PUBLIC_APP_URL)return a.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:s,port:t}=window.location;return"".concat(e,"//").concat(s).concat(t?":".concat(t):"")}}function r(){if(a.env.NEXT_PUBLIC_API_BASE_URL)return a.env.NEXT_PUBLIC_API_BASE_URL;let e=l();return"".concat(e,"/api")}function n(){return"production"}function c(){return"development"===n()}l(),r(),a.env.NEXTAUTH_URL?a.env.NEXTAUTH_URL:l(),n(),c(),n(),window.location.port||window.location.protocol,c();let d=r();class i{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...s.headers},...s},l=await fetch(t,a),r=await l.json();if(!l.ok)throw Error(r.error||"HTTP error! status: ".concat(l.status));return r}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/tools".concat(t?"?".concat(t):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/user/liked-tools".concat(t?"?".concat(t):""))}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/admin/tools".concat(t?"?".concat(t):""))}async approveTool(e,s){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=d){this.baseURL=e}}let o=new i},3109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},6149:(e,s,t)=>{Promise.resolve().then(t.bind(t,8097))},8097:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(5155),l=t(2115),r=t(4478),n=t(2731),c=t(9783),d=t(2839),i=t(4186),o=t(646),x=t(4861);let m=(0,t(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var h=t(2713),g=t(3109),u=t(5339),j=t(2657);function p(){let[e,s]=(0,l.useState)("7d"),[t,p]=(0,l.useState)(null),[N,y]=(0,l.useState)(!0),[b,v]=(0,l.useState)("");(0,l.useEffect)(()=>{f()},[e]);let f=async()=>{try{y(!0),v("");let s=await d.u.getAdminStats(e);s.success&&s.data?p(s.data):v(s.error||"获取统计数据失败")}catch(e){v("网络错误，请重试")}finally{y(!1)}};return N?(0,a.jsx)(r.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(n.A,{size:"lg",className:"py-20"})})}):(0,a.jsx)(r.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[b&&(0,a.jsx)(c.A,{message:b,onClose:()=>v(""),className:"mb-6"}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,a.jsx)(h.A,{className:"mr-3 h-8 w-8 text-blue-600"}),"管理员统计面板"]}),(0,a.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"查看网站运营数据和审核统计"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"1d",children:"今天"}),(0,a.jsx)("option",{value:"7d",children:"最近7天"}),(0,a.jsx)("option",{value:"30d",children:"最近30天"}),(0,a.jsx)("option",{value:"90d",children:"最近90天"})]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总工具数"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==t?void 0:t.totalTools)||0})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)(h.A,{className:"w-6 h-6 text-blue-600"})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,a.jsx)("span",{className:"text-green-600",children:"+12%"}),(0,a.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"待审核"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:(null==t?void 0:t.pendingReview)||0})]}),(0,a.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,a.jsx)(i.A,{className:"w-6 h-6 text-yellow-600"})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,a.jsx)("span",{className:"text-yellow-600",children:"需要关注"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"今日批准"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:((null==t?void 0:t.approvedToday)||0).toLocaleString()})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-green-600"})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,a.jsx)("span",{className:"text-green-600",children:"+8%"}),(0,a.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"今日拒绝"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:((null==t?void 0:t.rejectedToday)||0).toLocaleString()})]}),(0,a.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,a.jsx)(x.A,{className:"w-6 h-6 text-red-600"})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,a.jsx)(g.A,{className:"w-4 h-4 text-green-500 mr-1"}),(0,a.jsx)("span",{className:"text-green-600",children:"+15%"}),(0,a.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"审核概览"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"总工具数"})]}),(0,a.jsx)("span",{className:"text-lg font-bold text-blue-600",children:(null==t?void 0:t.totalTools)||0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-yellow-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(i.A,{className:"w-5 h-5 text-yellow-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"待审核"})]}),(0,a.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:(null==t?void 0:t.pendingReview)||0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-green-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"今日批准"})]}),(0,a.jsx)("span",{className:"text-lg font-bold text-green-600",children:(null==t?void 0:t.approvedToday)||0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"今日拒绝"})]}),(0,a.jsx)("span",{className:"text-lg font-bold text-red-600",children:(null==t?void 0:t.rejectedToday)||0})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"快速操作"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{onClick:()=>window.location.href="/admin",className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"查看待审核工具"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,a.jsx)(m,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"查看审核历史"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"导出统计报告"})]}),(0,a.jsxs)("button",{onClick:()=>f(),className:"w-full flex items-center justify-center space-x-2 p-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"刷新数据"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"系统信息"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:t?"在线":"离线"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"系统状态"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"统计周期"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:new Date().toLocaleDateString("zh-CN")}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"最后更新"})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,7350,8441,1684,7358],()=>s(6149)),_N_E=e.O()}]);