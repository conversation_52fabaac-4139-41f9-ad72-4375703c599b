(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5230],{2839:(e,t,s)=>{"use strict";s.d(t,{u:()=>d});var r=s(9509);function a(){if(r.env.NEXT_PUBLIC_APP_URL)return r.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:s}=window.location;return"".concat(e,"//").concat(t).concat(s?":".concat(s):"")}}function n(){if(r.env.NEXT_PUBLIC_API_BASE_URL)return r.env.NEXT_PUBLIC_API_BASE_URL;let e=a();return"".concat(e,"/api")}function c(){return"production"}function l(){return"development"===c()}a(),n(),r.env.NEXTAUTH_URL?r.env.NEXTAUTH_URL:a(),c(),l(),c(),window.location.port||window.location.protocol,l();let o=n();class i{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),n=await a.json();if(!a.ok)throw Error(n.error||"HTTP error! status: ".concat(a.status));return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=o){this.baseURL=e}}let d=new i},4478:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(5155);s(2115);var a=s(6874),n=s.n(a),c=s(365);let l=e=>{let{children:t}=e;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(c.default,{}),(0,r.jsx)("main",{className:"flex-1",children:t}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(n(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}},4871:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(5155),a=s(2115),n=s(5695),c=s(4478),l=s(7797),o=s(2839),i=s(7924);function d(e){var t;let{initialQuery:s,initialResults:d,initialCategories:u,initialPage:h=1,initialCategory:m="",initialSort:x="createdAt"}=e,g=(0,n.useRouter)(),p=(0,n.useSearchParams)(),[y,b]=(0,a.useState)(s),[j,f]=(0,a.useState)(d),[v]=(0,a.useState)(u),[N,w]=(0,a.useState)(m),[S,P]=(0,a.useState)(x),[T,A]=(0,a.useState)("grid"),[_,E]=(0,a.useState)(!1),[L,U]=(0,a.useState)(h),R=e=>{let t=new URLSearchParams(p);Object.entries(e).forEach(e=>{let[s,r]=e;r?t.set(s,r):t.delete(s)}),g.push("/search?".concat(t.toString()))},q=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"createdAt";E(!0);try{let a=await o.u.getTools({search:e,page:t,limit:12,category:s||void 0,sort:r,order:"desc"});a.success?f(a.data):(console.error("Search failed:",a.error),f(null))}catch(e){console.error("Search error:",e),f(null)}finally{E(!1)}},I=e=>{U(e),R({q:y,page:e.toString(),category:N,sort:S}),q(y,e,N,S)};return(0,a.useEffect)(()=>{y.trim()||q(void 0,1,N,S)},[y,N,S]),(0,r.jsx)(c.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"搜索 AI 工具"}),y&&(0,r.jsxs)("p",{className:"text-lg text-gray-600",children:['搜索 "',y,'" 的结果',j&&" - 找到 ".concat(j.pagination.totalItems," 个工具")]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,r.jsx)("form",{onSubmit:e=>{null==e||e.preventDefault(),U(1),R({q:null==y?void 0:y.trim(),page:"1",category:N,sort:S}),q(null==y?void 0:y.trim(),1,N,S)},children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或标签...",value:y,onChange:e=>b(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(i.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,r.jsxs)("button",{type:"submit",className:"absolute right-2 top-2 px-4 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",style:{whiteSpace:"nowrap",marginLeft:10},children:[!y.trim()&&"全部",y.trim()&&"搜索"]})]})})}),!y&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(i.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"开始搜索 AI 工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"输入关键词来搜索工具名称、描述或标签"})]}),_&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"搜索中..."})]}),!_&&j&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["显示 ",j.tools.length," 个结果，共 ",j.pagination.totalItems," 个",N&&' 在 "'.concat(null==(t=v.find(e=>e.slug===N))?void 0:t.name,'"')]})}),j.tools.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid"===T?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8":"space-y-4 mb-8",children:j.tools.map(e=>(0,r.jsx)(l.default,{tool:e},e._id))}),j.pagination.totalPages>1&&(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>I(L-1),disabled:!j.pagination.hasPrevPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"上一页"}),(0,r.jsxs)("span",{className:"px-3 py-2 text-sm text-gray-700",children:["第 ",L," 页，共 ",j.pagination.totalPages," 页"]}),(0,r.jsx)("button",{onClick:()=>I(L+1),disabled:!j.pagination.hasNextPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"下一页"})]})})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(i.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"尝试使用不同的关键词或调整筛选条件"})]})]})]})})}},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"notFound")&&s.d(t,{notFound:function(){return r.notFound}}),s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},7915:(e,t,s)=>{Promise.resolve().then(s.bind(s,4871))},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>t(7915)),_N_E=e.O()}]);