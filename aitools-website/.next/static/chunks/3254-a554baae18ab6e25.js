"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3254],{365:(e,t,a)=>{a.d(t,{default:()=>r});var l=a(2115);function r(){return(0,l.useEffect)(()=>{let e={},t=(e,t)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let a;new PerformanceObserver(a=>{let l=a.getEntries().find(e=>"first-contentful-paint"===e.name);l&&(e.fcp=l.startTime,t("FCP",l.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(a=>{let l=a.getEntries(),r=l[l.length-1];e.lcp=r.startTime,t("LCP",r.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(a=>{a.getEntries().forEach(a=>{e.fid=a.processingStart-a.startTime,t("FID",a.processingStart-a.startTime)})}).observe({entryTypes:["first-input"]}),a=0,new PerformanceObserver(l=>{l.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)}),e.cls=a,t("CLS",a)}).observe({entryTypes:["layout-shift"]})}let a=performance.getEntriesByType("navigation")[0];if(a){let l=a.responseStart-a.requestStart;e.ttfb=l,t("TTFB",l)}let l=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",l),()=>{window.removeEventListener("beforeunload",l)}},[]),null}},3467:(e,t,a)=>{a.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>i,kX:()=>l,mV:()=>c,tF:()=>u,v4:()=>n,vS:()=>r});let l={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},r=[{id:"free",title:"免费发布",description:l.FREE_LAUNCH.description,price:l.FREE_LAUNCH.displayPrice,features:l.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:l.PRIORITY_LAUNCH.description,price:l.PRIORITY_LAUNCH.displayPrice,features:l.PRIORITY_LAUNCH.features,recommended:!0}],s={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],i=[{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],o=e=>{switch(e){case s.FREE.value:return s.FREE.color;case s.FREEMIUM.value:return s.FREEMIUM.color;case s.PAID.value:return s.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case s.FREE.value:return s.FREE.label;case s.FREEMIUM.value:return s.FREEMIUM.label;case s.PAID.value:return s.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,t,a)=>{a.d(t,{A:()=>o});var l=a(5155),r=a(2115),s=a(2108),n=a(9911),i=a(6214);function o(e){let{toolId:t,initialLikes:a=0,initialLiked:o=!1,onLoginRequired:c,onUnlike:d,isInLikedPage:u=!1,showCount:m=!0,size:f="md"}=e,{data:x}=(0,s.useSession)(),{getToolState:g,initializeToolState:b,toggleLike:h}=(0,i.X)(),p=g(t);(0,r.useEffect)(()=>{b(t,a,o)},[t,a,o]);let v=async()=>{if(!x){null==c||c();return}if(p.loading)return;let e=p.liked;await h(t,u)&&u&&e&&d&&d(t)},y=(()=>{switch(f){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,l.jsxs)("button",{onClick:v,disabled:p.loading,className:"\n        ".concat(y.button,"\n        inline-flex items-center space-x-1\n        ").concat(p.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500","\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      "),title:p.liked?"取消点赞":"点赞",children:[p.loading?(0,l.jsx)("div",{className:"".concat(y.icon," animate-spin rounded-full border-2 border-gray-300 border-t-red-500")}):p.liked?(0,l.jsx)(n.Mbv,{className:y.icon}):(0,l.jsx)(n.sOK,{className:y.icon}),m&&(0,l.jsx)("span",{className:"".concat(y.text," font-medium"),children:p.likes})]})}},6214:(e,t,a)=>{a.d(t,{LikeProvider:()=>o,X:()=>c});var l=a(5155),r=a(2115),s=a(2108);let n={liked:!1,likes:0,loading:!1},i=(0,r.createContext)(null);function o(e){let{children:t}=e,{data:a}=(0,s.useSession)(),[o,c]=(0,r.useState)({}),d=(0,r.useCallback)(e=>o[e]||n,[o]),u=(0,r.useCallback)(function(e,t){let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];c(l=>l[e]?l:{...l,[e]:{liked:a,likes:t,loading:!1}})},[]),m=(0,r.useCallback)(async e=>{if(a)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let a=await t.json();a.success&&c(t=>({...t,[e]:{liked:a.data.liked,likes:a.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[a]),f=(0,r.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!a)return!1;c(t=>({...t,[e]:{...t[e]||n,loading:!0}}));try{let a=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(a.ok){let t=await a.json();if(t.success)return c(a=>({...a,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return c(t=>({...t,[e]:{...t[e]||n,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),c(t=>({...t,[e]:{...t[e]||n,loading:!1}})),!1}},[a]);return(0,r.useEffect)(()=>{a?Object.keys(o).forEach(e=>{m(e)}):c(e=>{let t={};return Object.keys(e).forEach(a=>{t[a]={...e[a],liked:!1,loading:!1}}),t})},[a]),(0,l.jsx)(i.Provider,{value:{toolStates:o,toggleLike:f,getToolState:d,initializeToolState:u,refreshToolState:m},children:t})}function c(){let e=(0,r.useContext)(i);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},7797:(e,t,a)=>{a.d(t,{default:()=>b});var l=a(5155),r=a(2115),s=a(6874),n=a.n(s),i=a(3786),o=a(2657),c=a(1976),d=a(4601),u=a(6766);function m(e){let{src:t,alt:a,width:s,height:n,className:i="",priority:o=!1,fill:c=!1,sizes:d,placeholder:m="empty",blurDataURL:f,fallbackSrc:x="/images/placeholder.svg",onError:g}=e,[b,h]=(0,r.useState)(t),[p,v]=(0,r.useState)(!0),[y,E]=(0,r.useState)(!1),w={src:b,alt:a,className:"".concat(i," ").concat(p?"opacity-0":"opacity-100"," transition-opacity duration-300"),onError:()=>{E(!0),v(!1),h(x),null==g||g()},onLoad:()=>{v(!1)},priority:o,placeholder:"blur"===m?"blur":"empty",blurDataURL:f||("blur"===m?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=document.createElement("canvas");a.width=e,a.height=t;let l=a.getContext("2d");return l&&(l.fillStyle="#f3f4f6",l.fillRect(0,0,e,t)),a.toDataURL()}():void 0),sizes:d||(c?"100vw":void 0)};return c?(0,l.jsxs)("div",{className:"relative overflow-hidden",children:[(0,l.jsx)(u.default,{...w,fill:!0,style:{objectFit:"cover"}}),p&&(0,l.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]}):(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(u.default,{...w,width:s,height:n}),p&&(0,l.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse",style:{width:s,height:n}})]})}let f={toolLogo:{width:64,height:64}},x={toolLogo:"64px"};var g=a(3467);let b=e=>{let{tool:t,onLoginRequired:a,onUnlike:r,isInLikedPage:s=!1}=e;return(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[t.logo?(0,l.jsx)(m,{src:t.logo,alt:"".concat(t.name," logo"),width:f.toolLogo.width,height:f.toolLogo.height,className:"rounded-lg object-cover",sizes:x.toolLogo,placeholder:"blur"}):(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)("span",{className:"text-white font-bold text-lg",children:t.name.charAt(0).toUpperCase()})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,l.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((0,g.Ef)(t.pricing)),children:(0,g.mV)(t.pricing)})]})]}),(0,l.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,l.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,l.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:t.description}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[t.tags.slice(0,3).map((e,t)=>(0,l.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),t.tags.length>3&&(0,l.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",t.tags.length-3]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:t.views})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,l.jsx)(c.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{children:t.likes})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.A,{toolId:t._id,initialLikes:t.likes,initialLiked:s,onLoginRequired:a,onUnlike:r,isInLikedPage:s}),(0,l.jsx)(n(),{href:"/tools/".concat(t._id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:"查看详情"})]})]})]})})}}}]);