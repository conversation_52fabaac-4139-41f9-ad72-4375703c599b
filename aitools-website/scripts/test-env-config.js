#!/usr/bin/env node

/**
 * 测试动态环境配置
 * 用于验证不同环境下的URL配置是否正确
 */

// 模拟不同的环境变量设置
const testCases = [
  {
    name: '默认开发环境',
    env: {
      NODE_ENV: 'development',
    },
    expected: {
      baseUrl: 'http://localhost:3001',
      apiBaseUrl: 'http://localhost:3001/api',
    }
  },
  {
    name: '自定义端口',
    env: {
      NODE_ENV: 'development',
      PORT: '4000',
    },
    expected: {
      baseUrl: 'http://localhost:4000',
      apiBaseUrl: 'http://localhost:4000/api',
    }
  },
  {
    name: '明确设置的URL',
    env: {
      NODE_ENV: 'development',
      NEXT_PUBLIC_APP_URL: 'https://example.com',
      NEXT_PUBLIC_API_BASE_URL: 'https://api.example.com',
    },
    expected: {
      baseUrl: 'https://example.com',
      apiBaseUrl: 'https://api.example.com',
    }
  },
  {
    name: 'Vercel环境',
    env: {
      NODE_ENV: 'production',
      VERCEL_URL: 'myapp-abc123.vercel.app',
    },
    expected: {
      baseUrl: 'https://myapp-abc123.vercel.app',
      apiBaseUrl: 'https://myapp-abc123.vercel.app/api',
    }
  },
  {
    name: 'Netlify环境',
    env: {
      NODE_ENV: 'production',
      NETLIFY: 'true',
      URL: 'https://myapp.netlify.app',
    },
    expected: {
      baseUrl: 'https://myapp.netlify.app',
      apiBaseUrl: 'https://myapp.netlify.app/api',
    }
  },
];

function runTest(testCase) {
  console.log(`\n🧪 测试: ${testCase.name}`);
  
  // 备份原始环境变量
  const originalEnv = { ...process.env };
  
  try {
    // 清除相关环境变量
    delete process.env.NODE_ENV;
    delete process.env.PORT;
    delete process.env.NEXT_PUBLIC_APP_URL;
    delete process.env.NEXT_PUBLIC_API_BASE_URL;
    delete process.env.VERCEL_URL;
    delete process.env.NETLIFY;
    delete process.env.URL;
    
    // 设置测试环境变量
    Object.assign(process.env, testCase.env);
    
    // 清除模块缓存，重新加载配置
    delete require.cache[require.resolve('../src/lib/env.ts')];
    
    // 由于是TypeScript文件，我们需要使用不同的方法
    // 这里我们直接测试逻辑
    const { getBaseUrl, getApiBaseUrl } = require('../src/lib/env.ts');
    
    const result = {
      baseUrl: getBaseUrl(),
      apiBaseUrl: getApiBaseUrl(),
    };
    
    console.log('  预期结果:', testCase.expected);
    console.log('  实际结果:', result);
    
    const passed = 
      result.baseUrl === testCase.expected.baseUrl &&
      result.apiBaseUrl === testCase.expected.apiBaseUrl;
    
    if (passed) {
      console.log('  ✅ 测试通过');
    } else {
      console.log('  ❌ 测试失败');
    }
    
    return passed;
  } catch (error) {
    console.log('  ❌ 测试出错:', error.message);
    return false;
  } finally {
    // 恢复原始环境变量
    process.env = originalEnv;
  }
}

function main() {
  console.log('🔧 动态环境配置测试');
  console.log('='.repeat(50));
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  for (const testCase of testCases) {
    if (runTest(testCase)) {
      passedTests++;
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！');
    process.exit(0);
  } else {
    console.log('⚠️  部分测试失败');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
