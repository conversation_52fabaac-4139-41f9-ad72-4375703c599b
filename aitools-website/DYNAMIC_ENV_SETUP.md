# 动态环境配置实现

本文档说明了如何实现动态环境配置，让应用能够自动根据运行时环境判断URL配置，而不需要在配置文件中硬编码。

## 🎯 实现目标

- ✅ 自动检测运行时端口和域名
- ✅ 支持开发环境任意端口
- ✅ 支持多种部署平台（Vercel、Netlify、Railway等）
- ✅ 保持向后兼容性
- ✅ 提供调试和测试工具

## 📁 修改的文件

### 1. 新增文件

- `src/lib/env.ts` - 动态环境配置工具
- `docs/ENVIRONMENT_CONFIG.md` - 详细使用文档
- `scripts/test-env-config.js` - 配置测试脚本

### 2. 修改的文件

- `src/lib/api.ts` - 使用动态API URL
- `src/lib/auth.ts` - 添加动态URL支持
- `.env.local` - 注释掉硬编码URL，添加说明
- `.env.example` - 更新示例和文档
- `package.json` - 添加测试脚本

## 🔧 核心实现

### 动态URL检测逻辑

```typescript
export function getBaseUrl(): string {
  // 1. 优先使用明确设置的环境变量
  if (process.env.NEXT_PUBLIC_APP_URL) {
    return process.env.NEXT_PUBLIC_APP_URL;
  }

  // 2. 在服务器端运行时
  if (typeof window === 'undefined') {
    // Vercel部署环境
    if (process.env.VERCEL_URL) {
      return `https://${process.env.VERCEL_URL}`;
    }
    
    // 其他部署平台...
    
    // 开发环境默认值
    const port = process.env.PORT || '3001';
    return `http://localhost:${port}`;
  }

  // 3. 在客户端运行时
  if (typeof window !== 'undefined') {
    const { protocol, hostname, port } = window.location;
    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;
  }

  return 'http://localhost:3001';
}
```

### API客户端更新

```typescript
// 之前：硬编码URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api';

// 现在：动态检测
import { getApiBaseUrl } from './env';
const API_BASE_URL = getApiBaseUrl();
```

## 🚀 使用方法

### 开发环境

```bash
# 使用默认端口 3001
npm run dev

# 使用自定义端口 - 无需修改配置
npm run dev -- --port 3002
npm run dev -- --port 4000
```

### 生产环境

#### 自动检测（推荐）
大多数部署平台会自动设置环境变量，无需手动配置：

- **Vercel**: 自动设置 `VERCEL_URL`
- **Netlify**: 自动设置 `URL`
- **Railway**: 自动设置 `RAILWAY_STATIC_URL`

#### 手动指定
如果需要强制指定URL：

```bash
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXTAUTH_URL=https://yourdomain.com
```

## 🧪 测试

运行配置测试：

```bash
npm run test:env
```

测试输出示例：
```
🔧 动态环境配置测试
==================================================

🧪 测试: 默认开发环境
  预期结果: { baseUrl: 'http://localhost:3001', apiBaseUrl: 'http://localhost:3001/api' }
  实际结果: { baseUrl: 'http://localhost:3001', apiBaseUrl: 'http://localhost:3001/api' }
  ✅ 测试通过

🧪 测试: 自定义端口
  预期结果: { baseUrl: 'http://localhost:4000', apiBaseUrl: 'http://localhost:4000/api' }
  实际结果: { baseUrl: 'http://localhost:4000', apiBaseUrl: 'http://localhost:4000/api' }
  ✅ 测试通过

==================================================
📊 测试结果: 5/5 通过
🎉 所有测试通过！
```

## 🔍 调试

在开发环境中，启动应用时会在控制台显示当前配置：

```
🔧 Dynamic Environment Configuration:
  Base URL: http://localhost:3002
  API Base URL: http://localhost:3002/api
  NextAuth URL: http://localhost:3002
  Environment: development
  Port: 3002
```

## 📋 迁移指南

### 从硬编码配置迁移

1. **备份现有配置**
   ```bash
   cp .env.local .env.local.backup
   ```

2. **更新代码**
   - 已自动完成，使用 `getBaseUrl()` 和 `getApiBaseUrl()`

3. **清理配置文件**
   - 注释掉 `.env.local` 中的硬编码URL
   - 或者完全删除这些行

4. **测试**
   ```bash
   npm run test:env
   npm run dev -- --port 3002  # 测试不同端口
   ```

### 回滚方案

如果需要回滚到硬编码配置，取消注释 `.env.local` 中的相关行：

```bash
# 取消注释这些行
NEXTAUTH_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
```

## 🎉 优势

1. **开发体验提升**：可以使用任意端口，无需修改配置
2. **部署简化**：支持多种部署平台的自动检测
3. **维护性**：减少硬编码，降低配置错误
4. **向后兼容**：保持对现有环境变量的支持
5. **调试友好**：提供详细的配置信息和测试工具

## 📚 相关文档

- [详细使用文档](./docs/ENVIRONMENT_CONFIG.md)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [NextAuth.js Configuration](https://next-auth.js.org/configuration/options)
